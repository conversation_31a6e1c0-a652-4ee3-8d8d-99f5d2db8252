<?php
// 安全导航入口页面
session_start();

// 检查是否已登录
$is_logged_in = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
$user_info = null;

if ($is_logged_in) {
    require_once 'auth_middleware.php';
    $user_info = get_current_user_info();

    // 检查是否为系统管理员
    if (!$user_info || $user_info['role'] !== 'admin') {
        // 非管理员用户，重定向到监控面板
        header('Location: monitor.php');
        exit;
    }

    // 检查管理员身份验证状态
    $admin_verified = isset($_SESSION['admin_verified']) && $_SESSION['admin_verified'] === true;
    $verification_time = $_SESSION['admin_verified_time'] ?? 0;
    $verification_ip = $_SESSION['admin_verified_ip'] ?? '';
    $current_ip = $_SERVER['REMOTE_ADDR'] ?? '';

    // 验证是否在有效期内（10分钟）且IP地址匹配
    $verification_valid = $admin_verified &&
                         (time() - $verification_time) < 600 &&
                         $verification_ip === $current_ip;

    if (!$verification_valid) {
        // 身份验证已过期或无效，清除验证状态并重定向
        unset($_SESSION['admin_verified']);
        unset($_SESSION['admin_verified_time']);
        unset($_SESSION['admin_verified_ip']);

        // 记录未授权访问尝试
        log_user_action('admin_index_unauthorized', 'Unauthorized access attempt to index.php');

        // 显示错误页面
        ?>
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>访问受限</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
                .error-container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { color: #dc3545; font-size: 24px; margin-bottom: 15px; }
                .error-message { color: #666; margin-bottom: 25px; line-height: 1.5; }
                .back-btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
                .back-btn:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-icon"></div>
                <h1 class="error-title">访问受限</h1>
                <p class="error-message">
                    您需要通过系统管理员身份验证才能访问此页面。<br>
                    请返回监控面板，点击"系统管理"按钮进行身份验证。
                </p>
                <a href="monitor.php" class="back-btn">返回监控面板</a>
            </div>
        </body>
        </html>
        <?php
        exit;
    }

    // 记录成功访问
    log_user_action('admin_index_accessed', 'Admin successfully accessed index.php');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控系统 - 安全入口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid;
        }
        
        .status-logged-in {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .status-logged-out {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }
        
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .status-logged-in .status-title {
            color: #155724;
        }
        
        .status-logged-out .status-title {
            color: #856404;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 15px;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        
        .user-details h3 {
            margin-bottom: 5px;
            color: #155724;
        }
        
        .user-details p {
            color: #666;
            font-size: 14px;
        }
        
        .navigation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .nav-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }
        
        .nav-card.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f8f9fa;
        }
        
        .nav-card.disabled:hover {
            transform: none;
            box-shadow: none;
            border-color: #e9ecef;
        }
        
        .nav-icon {
            font-size: 32px;
            margin-bottom: 15px;
            display: block;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .nav-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .nav-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-login-required {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-admin-only {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .security-info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            color: #004085;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .navigation-grid {
                grid-template-columns: 1fr;
            }
            
            .user-info {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 设备监控系统</h1>
            <p>安全的设备管理和监控平台</p>
        </div>
        
        <!-- 登录状态显示 -->
        <?php if ($is_logged_in): ?>
            <div class="status-card status-logged-in">
                                    <div class="status-title">已登录</div>
                <div class="user-info">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($user_info['name'], 0, 1)); ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($user_info['name']); ?></h3>
                        <p>角色: <?php echo htmlspecialchars($user_info['role']); ?> | 登录时间: <?php echo date('Y-m-d H:i:s', $user_info['login_time']); ?></p>
                        <?php if ($user_info['role'] === 'admin'): ?>
                            <p style="color: #28a745; font-weight: bold; font-size: 12px; margin-top: 5px;">
                                系统管理员身份已验证 | 验证时间: <?php echo date('Y-m-d H:i:s', $_SESSION['admin_verified_time']); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="status-card status-logged-out">
                                    <div class="status-title">未登录</div>
                <p>您需要登录才能访问系统功能。请使用有效的用户名和密码登录。</p>
            </div>
        <?php endif; ?>
        
        <!-- 功能导航 -->
        <div class="navigation-grid">
            <!-- 登录页面 -->
            <?php if (!$is_logged_in): ?>
                <a href="login.php" class="nav-card">
                    <span class="nav-status status-available">可用</span>
                    <span class="nav-icon">🔐</span>
                    <div class="nav-title">用户登录</div>
                    <div class="nav-description">使用用户名和密码登录系统</div>
                </a>
            <?php endif; ?>
            
            <!-- 监控页面 -->
            <a href="monitor.php" class="nav-card <?php echo !$is_logged_in ? 'disabled' : ''; ?>">
                <span class="nav-status <?php echo $is_logged_in ? 'status-available' : 'status-login-required'; ?>">
                    <?php echo $is_logged_in ? '可用' : '需要登录'; ?>
                </span>
                                    <span class="nav-icon"></span>
                <div class="nav-title">设备监控</div>
                <div class="nav-description">查看设备状态、心跳信息和实时监控</div>
            </a>
            
            <!-- 启动日志 -->
            <a href="startup_logs.php" class="nav-card <?php echo !$is_logged_in ? 'disabled' : ''; ?>">
                <span class="nav-status <?php echo $is_logged_in ? 'status-available' : 'status-login-required'; ?>">
                    <?php echo $is_logged_in ? '可用' : '需要登录'; ?>
                </span>
                <span class="nav-icon">📝</span>
                <div class="nav-title">启动日志</div>
                <div class="nav-description">查看设备启动记录和日志信息</div>
            </a>
            
            <!-- 用户管理 -->
            <?php 
            $can_manage_users = $is_logged_in && isset($user_info) && 
                               (function() use ($user_info) {
                                   require_once 'auth_config.php';
                                   global $permissions;
                                   return in_array('manage_users', $permissions[$user_info['role']] ?? []);
                               })();
            ?>
            <a href="user_management.php" class="nav-card <?php echo !$can_manage_users ? 'disabled' : ''; ?>">
                <span class="nav-status <?php echo $can_manage_users ? 'status-available' : 'status-admin-only'; ?>">
                    <?php echo $can_manage_users ? '可用' : '仅管理员'; ?>
                </span>
                <span class="nav-icon">👥</span>
                <div class="nav-title">用户管理</div>
                <div class="nav-description">管理用户账户、权限和登录记录</div>
            </a>
            
            <!-- 权限测试 -->
            <a href="permission_test.php" class="nav-card <?php echo !$is_logged_in ? 'disabled' : ''; ?>">
                <span class="nav-status <?php echo $is_logged_in ? 'status-available' : 'status-login-required'; ?>">
                    <?php echo $is_logged_in ? '可用' : '需要登录'; ?>
                </span>
                <span class="nav-icon">🔐</span>
                <div class="nav-title">权限测试</div>
                <div class="nav-description">测试当前用户的权限设置和功能访问</div>
            </a>

            <!-- 系统测试 -->
            <a href="test_auth_system.php" class="nav-card">
                <span class="nav-status status-available">可用</span>
                <span class="nav-icon">🧪</span>
                <div class="nav-title">系统测试</div>
                <div class="nav-description">测试身份验证系统的各项功能</div>
            </a>
            
            <!-- 系统监控 -->
            <a href="test_monitor.php" class="nav-card <?php echo !$can_manage_users ? 'disabled' : ''; ?>">
                <span class="nav-status <?php echo $can_manage_users ? 'status-available' : 'status-admin-only'; ?>">
                    <?php echo $can_manage_users ? '可用' : '仅管理员'; ?>
                </span>
                                    <span class="nav-icon"></span>
                <div class="nav-title">系统监控</div>
                <div class="nav-description">系统状态检查和维护工具</div>
            </a>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
            <?php if ($is_logged_in): ?>
                <a href="monitor.php" class="btn btn-primary">进入监控系统</a>
                <?php if ($can_manage_users): ?>
                    <a href="user_management.php" class="btn btn-secondary">用户管理</a>
                <?php endif; ?>
                <a href="logout.php" class="btn btn-danger">退出登录</a>
            <?php else: ?>
                <a href="login.php" class="btn btn-primary">立即登录</a>
                <a href="test_auth_system.php" class="btn btn-secondary">系统测试</a>
            <?php endif; ?>
        </div>
        
        <!-- 安全信息 -->
        <div class="security-info">
                            <strong>安全提示：</strong>
            <ul style="margin: 10px 0 0 20px;">
                <li>所有页面都受到身份验证保护</li>
                <li>会话将在1小时后自动过期</li>
                <li>系统记录所有登录尝试和用户操作</li>
                <li>请妥善保管您的登录凭据</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 禁用已禁用的链接
        document.querySelectorAll('.nav-card.disabled').forEach(function(card) {
            card.addEventListener('click', function(e) {
                e.preventDefault();
                alert('此功能需要登录或更高权限才能访问');
            });
        });
        
        // 自动刷新登录状态
        <?php if ($is_logged_in): ?>
        setInterval(function() {
            fetch('monitor.php?action=check_session')
                .then(response => response.json())
                .then(data => {
                    if (!data.logged_in) {
                        alert('会话已过期，请重新登录');
                        window.location.href = 'login.php';
                    }
                })
                .catch(error => {
                    console.log('会话检查失败:', error);
                });
        }, 300000); // 每5分钟检查一次
        <?php endif; ?>
    </script>
</body>
</html>
