-- 设备使用时间限制表创建SQL（适配MySQL 5.0和phpMyAdmin）
-- 请在phpMyAdmin中执行以下SQL命令

CREATE TABLE IF NOT EXISTS `device_usage_limits` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `device_id` varchar(100) NOT NULL COMMENT '设备唯一标识',
    `device_model` varchar(255) DEFAULT NULL COMMENT '设备型号',
    `limit_type` enum('day','month','year') NOT NULL DEFAULT 'day' COMMENT '限制类型：day日，month月，year年',
    `limit_value` int(11) NOT NULL DEFAULT 1 COMMENT '限制数值（天数/月数/年数）',
    `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始使用时间',
    `end_time` timestamp NOT NULL COMMENT '限制结束时间',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
    `auto_blacklist` tinyint(1) NOT NULL DEFAULT 1 COMMENT '到期是否自动拉黑：1是，0否',
    `blacklist_reason` varchar(255) DEFAULT '设备使用时间到期自动拉黑' COMMENT '拉黑原因',
    `created_by` varchar(100) DEFAULT 'system' COMMENT '创建者',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_device_limit` (`device_id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_limit_type` (`limit_type`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='设备使用时间限制表';