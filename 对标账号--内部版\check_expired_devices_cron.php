<?php
/**
 * 定时检查过期设备脚本
 * 用于自动检查设备使用时间限制，过期设备自动拉黑
 * 建议每小时运行一次
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// 记录开始时间
$start_time = microtime(true);
$log_prefix = "[" . date('Y-m-d H:i:s') . "]";

echo "{$log_prefix} 开始检查过期设备...\n";

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

try {
    // 创建连接
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // 检查连接
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset("utf8mb4");
    
    echo "{$log_prefix} 数据库连接成功\n";
    
    // 确保device_usage_limits表存在（MySQL 5.0兼容版本）
    $create_usage_limits_table = "CREATE TABLE IF NOT EXISTS device_usage_limits (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL,
        device_model VARCHAR(255),
        limit_type ENUM('day','month','year') NOT NULL DEFAULT 'day',
        limit_value INT NOT NULL DEFAULT 1,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        is_active TINYINT(1) DEFAULT 1,
        auto_blacklist TINYINT(1) DEFAULT 1,
        blacklist_reason VARCHAR(255) DEFAULT '设备使用时间到期自动拉黑',
        created_by VARCHAR(100) DEFAULT 'system',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL,
        UNIQUE KEY unique_device_limit (device_id),
        INDEX idx_device_id (device_id),
        INDEX idx_limit_type (limit_type),
        INDEX idx_is_active (is_active),
        INDEX idx_end_time (end_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci";
    
    $conn->query($create_usage_limits_table);
    
    // 确保device_blacklist表存在（MySQL 5.0兼容版本）
    $create_blacklist_table = "CREATE TABLE IF NOT EXISTS device_blacklist (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL UNIQUE,
        device_model VARCHAR(255),
        blacklist_reason TEXT,
        trigger_count INT DEFAULT 0,
        blacklist_start_time DATETIME NOT NULL,
        blacklist_end_time DATETIME,
        blacklist_duration_hours INT DEFAULT 24,
        is_active TINYINT(1) DEFAULT 1,
        blacklisted_by VARCHAR(100) DEFAULT 'system',
        updated_at DATETIME NOT NULL,
        INDEX idx_device_id (device_id),
        INDEX idx_is_active (is_active),
        INDEX idx_blacklist_end_time (blacklist_end_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci";
    
    $conn->query($create_blacklist_table);
    
    // 查找过期设备
    $expired_sql = "SELECT device_id, device_model, blacklist_reason 
                   FROM device_usage_limits 
                   WHERE is_active = 1 AND auto_blacklist = 1 AND end_time <= NOW()";
    
    $result = $conn->query($expired_sql);
    
    if (!$result) {
        throw new Exception("查询过期设备失败: " . $conn->error);
    }
    
    $expired_count = 0;
    $processed_devices = [];
    $skipped_devices = [];
    
    echo "{$log_prefix} 发现 " . $result->num_rows . " 个过期设备\n";
    
    if ($result->num_rows > 0) {
        $conn->begin_transaction();
        
        try {
            while ($row = $result->fetch_assoc()) {
                $device_id = $row['device_id'];
                $device_model = $row['device_model'] ?? '未知设备';
                $reason = $row['blacklist_reason'];
                
                echo "{$log_prefix} 处理设备: {$device_id} ({$device_model}) - {$reason}\n";
                
                // 检查是否已在黑名单中
                $check_blacklist = "SELECT COUNT(*) as count FROM device_blacklist WHERE device_id = ? AND is_active = 1";
                $check_stmt = $conn->prepare($check_blacklist);
                $check_stmt->bind_param("s", $device_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $is_blacklisted = $check_result->fetch_assoc()['count'] > 0;
                $check_stmt->close();
                
                if (!$is_blacklisted) {
                    // 添加到黑名单（MySQL 5.0兼容版本）
                    $blacklist_sql = "INSERT INTO device_blacklist
                                     (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, blacklisted_by, updated_at)
                                     VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, 'usage-limit-system', NOW())";
                    
                    $blacklist_stmt = $conn->prepare($blacklist_sql);
                    $blacklist_stmt->bind_param("sss", $device_id, $device_model, $reason);
                    
                    if ($blacklist_stmt->execute()) {
                        $expired_count++;
                        $processed_devices[] = $device_id;
                        echo "{$log_prefix} ✓ 设备 {$device_id} 已加入黑名单\n";
                    } else {
                        echo "{$log_prefix} ✗ 设备 {$device_id} 加入黑名单失败: " . $blacklist_stmt->error . "\n";
                    }
                    
                    $blacklist_stmt->close();
                } else {
                    $skipped_devices[] = $device_id;
                    echo "{$log_prefix} - 设备 {$device_id} 已在黑名单中，跳过\n";
                }
                
                // 将使用时间限制设为非活跃（MySQL 5.0兼容版本）
                $deactivate_sql = "UPDATE device_usage_limits SET is_active = 0, updated_at = NOW() WHERE device_id = ?";
                $deactivate_stmt = $conn->prepare($deactivate_sql);
                $deactivate_stmt->bind_param("s", $device_id);
                
                if ($deactivate_stmt->execute()) {
                    echo "{$log_prefix} ✓ 设备 {$device_id} 的使用时间限制已停用\n";
                } else {
                    echo "{$log_prefix} ✗ 设备 {$device_id} 停用使用时间限制失败: " . $deactivate_stmt->error . "\n";
                }
                
                $deactivate_stmt->close();
            }
            
            $conn->commit();
            echo "{$log_prefix} 事务提交成功\n";
            
        } catch (Exception $e) {
            $conn->rollback();
            echo "{$log_prefix} 事务回滚: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    // 关闭连接
    $conn->close();
    
    // 计算执行时间
    $end_time = microtime(true);
    $execution_time = round(($end_time - $start_time) * 1000, 2);
    
    // 输出结果统计
    echo "{$log_prefix} ====== 执行完成 ======\n";
    echo "{$log_prefix} 执行时间: {$execution_time}ms\n";
    echo "{$log_prefix} 新拉黑设备: {$expired_count} 个\n";
    echo "{$log_prefix} 跳过设备: " . count($skipped_devices) . " 个\n";
    
    if (!empty($processed_devices)) {
        echo "{$log_prefix} 新拉黑设备列表: " . implode(', ', $processed_devices) . "\n";
    }
    
    if (!empty($skipped_devices)) {
        echo "{$log_prefix} 跳过设备列表: " . implode(', ', $skipped_devices) . "\n";
    }
    
    // 写入日志文件
    $log_file = __DIR__ . '/expired_devices_check.log';
    $log_entry = "{$log_prefix} 检查完成 - 新拉黑: {$expired_count}, 跳过: " . count($skipped_devices) . ", 执行时间: {$execution_time}ms\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    
    echo "{$log_prefix} 日志已写入: {$log_file}\n";
    
} catch (Exception $e) {
    $error_msg = "{$log_prefix} 错误: " . $e->getMessage() . "\n";
    echo $error_msg;
    
    // 写入错误日志
    $error_log_file = __DIR__ . '/expired_devices_error.log';
    file_put_contents($error_log_file, $error_msg, FILE_APPEND | LOCK_EX);
    
    exit(1);
}

echo "{$log_prefix} 脚本执行完成\n";
?>