# 设备使用时间限制功能说明

## 功能概述

本功能允许管理员为设备设置使用时间限制，到达设定时间后可以自动进行拉黑操作。支持按日、月、年三种时间单位进行限制。

## 主要特性

1. **灵活的时间限制**：支持按天、月、年设置使用时间
2. **自动拉黑**：到期后可自动将设备加入黑名单
3. **手动检查**：提供手动检查过期设备的功能
4. **定时自动检查**：支持定时任务自动检查过期设备
5. **完整的权限控制**：只有有权限的管理员才能使用此功能
6. **图标清理**：移除了界面中的所有图标表情符号

## 安装步骤

### 1. 创建数据库表

在phpMyAdmin中执行以下SQL语句：

```sql
-- 请复制对标账号--内部版/设备使用时间限制表创建.sql 文件中的内容到phpMyAdmin中执行
```

### 2. 访问管理界面

1. 登录系统后，在监控面板的导航标签页中找到"使用时间限制"
2. 点击进入设备使用时间限制管理页面

### 3. 设置定时任务

1. 以管理员身份运行 `setup_cron_task.bat`
2. 脚本会自动创建Windows定时任务，每小时检查一次过期设备

## 使用说明

### 添加设备使用时间限制

1. 在管理界面中填写以下信息：
   - **设备ID**：要限制的设备唯一标识
   - **设备型号**：设备型号（可选）
   - **限制类型**：选择"按天"、"按月"或"按年"
   - **限制数值**：输入具体的数值（如：7天、1个月、1年）
   - **到期自动拉黑**：选择到期后是否自动拉黑
   - **拉黑原因**：自定义拉黑原因

2. 点击"添加限制"按钮保存

### 管理现有限制

- 在限制列表中可以查看所有设备的使用时间限制情况
- 每个设备显示剩余时间和当前状态
- 可以点击"移除限制"按钮取消某个设备的时间限制

### 手动检查过期设备

1. 在监控面板的心跳监控页面中，找到"检查过期设备"按钮
2. 点击按钮会立即检查所有过期设备并进行相应处理
3. 系统会显示处理结果

### 定时自动检查

- 系统每小时自动运行一次过期设备检查
- 检查日志保存在 `expired_devices_check.log` 文件中
- 错误日志保存在 `expired_devices_error.log` 文件中

## 权限要求

- **添加/移除时间限制**：需要 `manage_devices` 权限
- **手动检查过期设备**：需要 `manage_blacklist` 权限
- **查看限制列表**：需要登录系统

## 数据库表结构

### device_usage_limits 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键ID |
| device_id | VARCHAR(100) | 设备唯一标识 |
| device_model | VARCHAR(255) | 设备型号 |
| limit_type | ENUM | 限制类型（day/month/year） |
| limit_value | INT | 限制数值 |
| start_time | TIMESTAMP | 开始时间 |
| end_time | TIMESTAMP | 结束时间 |
| is_active | TINYINT(1) | 是否启用 |
| auto_blacklist | TINYINT(1) | 是否自动拉黑 |
| blacklist_reason | VARCHAR(255) | 拉黑原因 |
| created_by | VARCHAR(100) | 创建者 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 文件说明

- `设备使用时间限制表创建.sql`：数据库表创建SQL脚本
- `device_usage_limits.php`：设备使用时间限制管理页面
- `check_expired_devices_cron.php`：定时检查过期设备的脚本
- `setup_cron_task.bat`：Windows定时任务设置脚本
- `remove_all_icons.php`：图标表情移除脚本

## 注意事项

1. 时间限制的计算基于服务器时间
2. 过期设备被拉黑后，其使用时间限制会自动设为非活跃状态
3. 如果设备已在黑名单中，不会重复添加
4. 定时任务需要系统支持PHP命令行运行
5. 建议定期检查日志文件以监控系统运行状态

## 故障排除

### 定时任务不工作
1. 检查PHP是否正确安装并在PATH中
2. 确认定时任务是否成功创建：`schtasks /query /tn "设备使用时间限制检查"`
3. 查看错误日志文件

### 页面访问权限不足
1. 确认当前用户有相应权限
2. 检查 `auth_config.php` 中的权限配置

### 数据库连接失败
1. 检查数据库连接参数是否正确
2. 确认数据库服务是否正常运行
3. 检查数据库用户权限

## 更新日志

- 2025-01-24：初始版本发布
  - 支持按日/月/年设置设备使用时间限制
  - 支持自动拉黑过期设备
  - 提供手动和自动检查功能
  - 移除所有界面图标表情符号
  - 添加完整的权限控制