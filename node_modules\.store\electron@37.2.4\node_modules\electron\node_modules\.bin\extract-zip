#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/C:/Users/<USER>/Desktop/历史autojs6版本/node_modules/.store/extract-zip@2.0.1/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/C:/Users/<USER>/Desktop/历史autojs6版本/node_modules/.store/extract-zip@2.0.1/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../extract-zip@2.0.1/node_modules/extract-zip/cli.js" "$@"
else
  exec node  "$basedir/../../../../../extract-zip@2.0.1/node_modules/extract-zip/cli.js" "$@"
fi
