<?php
session_start();
require_once 'auth_config.php';

// 如果已经登录，重定向到监控页面
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    safe_redirect('monitor.php');
}

$error_message = '';
$success_message = '';

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // 验证CSRF令牌
    if (!verify_csrf_token($csrf_token)) {
        $error_message = '安全验证失败，请重新提交';
    } else {
        $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // 检查登录尝试次数
        if (!check_login_attempts($client_ip)) {
            global $session_config;
            $lockout_minutes = $session_config['lockout_time'] / 60;
            $error_message = "登录尝试次数过多，请等待 {$lockout_minutes} 分钟后再试";
        } else {
            // 验证用户名和密码
            if (empty($username) || empty($password)) {
                $error_message = '用户名和密码不能为空';
                log_login_attempt($username, $client_ip, false, $user_agent);
            } elseif (authenticate_user($username, $password)) {
                // 登录成功
                $user_info = get_user_profile($username);
                
                $_SESSION['logged_in'] = true;
                $_SESSION['username'] = $username;
                $_SESSION['user_role'] = $user_info['role'];
                $_SESSION['user_name'] = $user_info['name'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                $_SESSION['client_ip'] = $client_ip;
                
                // 记住我功能
                if ($remember_me) {
                    global $session_config;
                    $expire_time = time() + $session_config['remember_me_timeout'];
                    $token_data = $username . ':' . hash('sha256', $password . $client_ip . time());
                    $secure_cookie = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

                    setcookie('remember_token', base64_encode($token_data), $expire_time, '/', '', $secure_cookie, true);

                    // 记录记住我功能使用日志
                    error_log("[" . date('Y-m-d H:i:s') . "] 用户 {$username} 启用了记住我功能，IP: {$client_ip}");
                }
                
                log_login_attempt($username, $client_ip, true, $user_agent);
                
                // 清理过期的登录尝试记录
                cleanup_login_attempts();
                
                safe_redirect('monitor.php');
            } else {
                $error_message = '用户名或密码错误';
                log_login_attempt($username, $client_ip, false, $user_agent);
            }
        }
    }
}

// 处理记住我自动登录
if (isset($_COOKIE['remember_token']) && !isset($_SESSION['logged_in'])) {
    $token = base64_decode($_COOKIE['remember_token']);
    $parts = explode(':', $token);
    
    if (count($parts) === 2) {
        $username = $parts[0];
        $expected_hash = $parts[1];
        $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        
        // 验证记住我令牌
        global $admin_users;
        if (isset($admin_users[$username])) {
            // 验证用户存在且令牌格式正确
            if (authenticate_user($username, $admin_users[$username]['password'])) {
                // 自动登录成功
                $user_info = get_user_profile($username);

                $_SESSION['logged_in'] = true;
                $_SESSION['username'] = $username;
                $_SESSION['user_role'] = $user_info['role'];
                $_SESSION['user_name'] = $user_info['name'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                $_SESSION['client_ip'] = $client_ip;
                $_SESSION['auto_login'] = true;

                // 记录自动登录日志
                error_log("[" . date('Y-m-d H:i:s') . "] 用户 {$username} 通过记住我功能自动登录，IP: {$client_ip}");

                // 刷新记住我cookie的过期时间
                global $session_config;
                $expire_time = time() + $session_config['remember_me_timeout'];
                $token_data = $username . ':' . hash('sha256', $admin_users[$username]['password'] . $client_ip . time());
                $secure_cookie = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
                setcookie('remember_token', base64_encode($token_data), $expire_time, '/', '', $secure_cookie, true);

                safe_redirect('monitor.php');
            }
        }
    }
    
    // 如果验证失败，清除记住我cookie
    setcookie('remember_token', '', time() - 3600, '/');
}

// 生成CSRF令牌
$csrf_token = generate_csrf_token();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .checkbox-group label {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .alert {
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .login-info {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 12px;
            color: #666;
        }
        
        .login-info h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .login-info ul {
            list-style: none;
            padding-left: 0;
        }
        
        .login-info li {
            margin-bottom: 5px;
            padding-left: 15px;
            position: relative;
        }
        
        .login-info li:before {
            content: "•";
            color: #667eea;
            position: absolute;
            left: 0;
        }
        
        .security-info {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: #999;
        }
        
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>设备监控系统</h1>
            <p>请输入您的登录凭据</p>
        </div>
        
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <input type="hidden" name="action" value="login">
            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
            
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required 
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                       autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="remember_me" name="remember_me">
                <label for="remember_me">记住我（7天）</label>
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        <div class="login-info">
            <h4>测试账户信息：</h4>
            <ul>
                <li><strong>admin</strong> / admin123 (系统管理员)</li>
                <li><strong>operator</strong> / op123456 (操作员)</li>
            </ul>
        </div>
        
        <div class="security-info">
                            <p>您的连接已加密 | IP: <?php echo htmlspecialchars($_SERVER['REMOTE_ADDR'] ?? ''); ?></p>
        </div>
    </div>
    
    <script>
        // 自动聚焦到用户名输入框
        document.getElementById('username').focus();
        
        // 防止表单重复提交
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.login-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = '登录中...';
            
            setTimeout(function() {
                submitBtn.disabled = false;
                submitBtn.textContent = '登录';
            }, 3000);
        });
    </script>
</body>
</html>
