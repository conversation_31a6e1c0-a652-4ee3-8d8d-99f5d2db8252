<?php
/**
 * 设备ID统计报告页面
 * 用于监控设备ID的唯一性和一致性
 */

header('Content-Type: text/html; charset=utf-8');

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>设备ID统计报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; text-align: center; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card h3 { margin: 0 0 10px 0; color: #666; font-size: 14px; }
        .stat-card .number { font-size: 32px; font-weight: bold; margin: 0; }
        .green { color: #2ecc71; }
        .red { color: #e74c3c; }
        .yellow { color: #f39c12; }
        .blue { color: #3498db; }
        table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .button { display: inline-block; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .button:hover { background: #2980b9; }
        .alert { padding: 15px; border-radius: 4px; margin: 10px 0; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>设备ID统计报告</h1>
            <p>监控设备ID的唯一性和一致性</p>
        </div>";

try {
    // 创建连接
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // 检查连接
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset("utf8mb4");
    
    // 获取统计数据
    $stats = [];
    
    // 设备表统计
    $devices_stats = $conn->query("SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT device_id) as unique_devices,
        COUNT(*) - COUNT(DISTINCT device_id) as duplicate_records
        FROM devices")->fetch_assoc();
    
    // 心跳表统计
    $heartbeat_stats = $conn->query("SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT device_id) as unique_devices,
        COUNT(*) - COUNT(DISTINCT device_id) as duplicate_records
        FROM device_heartbeat")->fetch_assoc();
    
    // 设备ID格式分析
    $id_format_stats = $conn->query("SELECT 
        SUM(CASE WHEN device_id REGEXP '^[a-f0-9]{16}$' THEN 1 ELSE 0 END) as android_id_format,
        SUM(CASE WHEN device_id LIKE 'SN_%' THEN 1 ELSE 0 END) as serial_format,
        SUM(CASE WHEN device_id LIKE 'DEV_%' THEN 1 ELSE 0 END) as generated_format,
        SUM(CASE WHEN device_id LIKE 'FALLBACK_%' THEN 1 ELSE 0 END) as fallback_format,
        COUNT(*) as total
        FROM device_heartbeat")->fetch_assoc();
    
    echo "<div class='stats-grid'>";
    
    // 设备表统计卡片
    echo "<div class='stat-card'>";
    echo "<h3>设备表记录</h3>";
    echo "<p class='number blue'>" . number_format($devices_stats['total_records']) . "</p>";
    echo "<small>总记录数</small>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h3>设备表唯一设备</h3>";
    echo "<p class='number green'>" . number_format($devices_stats['unique_devices']) . "</p>";
    echo "<small>唯一设备ID数</small>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h3>心跳表记录</h3>";
    echo "<p class='number blue'>" . number_format($heartbeat_stats['total_records']) . "</p>";
    echo "<small>总心跳记录数</small>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h3>心跳表唯一设备</h3>";
    echo "<p class='number green'>" . number_format($heartbeat_stats['unique_devices']) . "</p>";
    echo "<small>唯一设备ID数</small>";
    echo "</div>";
    
    echo "</div>";
    
    // 显示警告信息
    if ($devices_stats['duplicate_records'] > 0 || $heartbeat_stats['duplicate_records'] > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<strong>发现重复记录！</strong><br>";
        if ($devices_stats['duplicate_records'] > 0) {
            echo "设备表中有 " . $devices_stats['duplicate_records'] . " 条重复记录<br>";
        }
        if ($heartbeat_stats['duplicate_records'] > 0) {
            echo "心跳表中有 " . $heartbeat_stats['duplicate_records'] . " 条重复记录<br>";
        }
        echo "<a href='monitor.php?view=heartbeat' class='button'>去心跳监控页面清理</a>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>";
        echo "<strong>设备ID唯一性正常</strong><br>";
        echo "所有设备记录的ID都是唯一的，无重复记录。";
        echo "</div>";
    }
    
    // 设备ID格式分析
    echo "<h2>📋 设备ID格式分析</h2>";
    echo "<table>";
    echo "<thead><tr><th>格式类型</th><th>数量</th><th>占比</th><th>说明</th></tr></thead>";
    echo "<tbody>";
    
    $total = $id_format_stats['total'];
    
    echo "<tr>";
    echo "<td>Android ID</td>";
    echo "<td>" . number_format($id_format_stats['android_id_format']) . "</td>";
    echo "<td>" . ($total > 0 ? round($id_format_stats['android_id_format'] / $total * 100, 1) : 0) . "%</td>";
    echo "<td>标准16位十六进制Android ID</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>序列号格式</td>";
    echo "<td>" . number_format($id_format_stats['serial_format']) . "</td>";
    echo "<td>" . ($total > 0 ? round($id_format_stats['serial_format'] / $total * 100, 1) : 0) . "%</td>";
    echo "<td>以SN_开头的设备序列号</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>生成格式</td>";
    echo "<td>" . number_format($id_format_stats['generated_format']) . "</td>";
    echo "<td>" . ($total > 0 ? round($id_format_stats['generated_format'] / $total * 100, 1) : 0) . "%</td>";
    echo "<td>以DEV_开头的生成ID</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td>备用格式</td>";
    echo "<td>" . number_format($id_format_stats['fallback_format']) . "</td>";
    echo "<td>" . ($total > 0 ? round($id_format_stats['fallback_format'] / $total * 100, 1) : 0) . "%</td>";
    echo "<td>以FALLBACK_开头的备用ID</td>";
    echo "</tr>";
    
    echo "</tbody></table>";
    
    // 最近的设备ID示例
    echo "<h2>最近的设备ID示例</h2>";
    $recent_devices = $conn->query("SELECT device_id, device_model, last_heartbeat, 
                                           CASE 
                                               WHEN device_id REGEXP '^[a-f0-9]{16}$' THEN 'Android ID'
                                               WHEN device_id LIKE 'SN_%' THEN '序列号'
                                               WHEN device_id LIKE 'DEV_%' THEN '生成ID'
                                               WHEN device_id LIKE 'FALLBACK_%' THEN '备用ID'
                                               ELSE '其他'
                                           END as id_type
                                    FROM device_heartbeat 
                                    ORDER BY last_heartbeat DESC 
                                    LIMIT 10");
    
    echo "<table>";
    echo "<thead><tr><th>设备ID</th><th>设备型号</th><th>ID类型</th><th>最后心跳</th></tr></thead>";
    echo "<tbody>";
    
    while ($row = $recent_devices->fetch_assoc()) {
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($row['device_id']) . "</code></td>";
        echo "<td>" . htmlspecialchars($row['device_model'] ?? '未知') . "</td>";
        echo "<td>" . htmlspecialchars($row['id_type']) . "</td>";
        echo "<td>" . $row['last_heartbeat'] . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody></table>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<strong>错误:</strong> " . $e->getMessage();
    echo "</div>";
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

echo "
        <div style='text-align: center; margin: 30px 0;'>
            <a href='monitor.php' class='button'>返回设备管理</a>
            <a href='monitor.php?view=heartbeat' class='button'>心跳监控</a>
            <a href='javascript:location.reload()' class='button'>刷新报告</a>
        </div>
    </div>
</body>
</html>";
?>
