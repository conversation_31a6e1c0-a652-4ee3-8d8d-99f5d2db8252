<?php
/**
 * 手动触发过期设备检查
 * 提供Web界面来手动执行过期设备检查
 */

// 身份验证保护
require_once 'auth_middleware.php';

// 检查登录状态
require_login();

// 检查权限
require_permission('manage_blacklist');

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

$message = '';
$error = '';

// 处理手动检查请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'manual_check') {
    try {
        // 执行检查脚本
        $script_path = __DIR__ . '/check_expired_devices_cron.php';
        
        if (!file_exists($script_path)) {
            throw new Exception('检查脚本不存在: ' . $script_path);
        }
        
        // 捕获输出
        ob_start();
        include $script_path;
        $output = ob_get_clean();
        
        $message = "手动检查完成！\n\n执行输出:\n" . $output;
        
        // 记录用户操作
        log_user_action('manual_expired_check', 'Manual expired devices check executed');
        
    } catch (Exception $e) {
        $error = '执行失败: ' . $e->getMessage();
    }
}

// 获取最近的日志内容
function getRecentLogs($file, $lines = 20) {
    if (!file_exists($file)) {
        return "日志文件不存在";
    }
    
    $content = file_get_contents($file);
    $log_lines = explode("\n", $content);
    $recent_lines = array_slice($log_lines, -$lines);
    
    return implode("\n", array_filter($recent_lines));
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动检查过期设备</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .nav-buttons {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .nav-buttons a, .nav-buttons button {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .nav-buttons a:hover, .nav-buttons button:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545 !important;
        }
        
        .btn-danger:hover {
            background: #c82333 !important;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .log-section {
            margin-top: 30px;
        }
        
        .log-content {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>手动检查过期设备</h1>
            <p>立即执行过期设备检查和自动拉黑操作</p>
        </div>
        
        <div class="content">
            <div class="nav-buttons">
                <a href="device_usage_limits.php">返回设备限制管理</a>
                <a href="monitor.php">返回监控面板</a>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <pre><?php echo htmlspecialchars($message); ?></pre>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <div class="status-info">
                <h3>系统状态</h3>
                <p><strong>定时检查脚本:</strong> check_expired_devices_cron.php</p>
                <p><strong>建议执行频率:</strong> 每小时一次</p>
                <p><strong>当前时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            </div>
            
            <form method="post" onsubmit="return confirm('确定要立即执行过期设备检查吗？过期设备将被自动拉黑。');">
                <input type="hidden" name="action" value="manual_check">
                <button type="submit" class="nav-buttons btn-danger">立即执行检查</button>
            </form>
            
            <div class="log-section">
                <h3>最近检查日志</h3>
                <div class="log-content">
<?php echo htmlspecialchars(getRecentLogs(__DIR__ . '/expired_devices_check.log')); ?>
                </div>
            </div>
            
            <?php 
            $error_log = __DIR__ . '/expired_devices_error.log';
            if (file_exists($error_log) && filesize($error_log) > 0): 
            ?>
            <div class="log-section">
                <h3>错误日志</h3>
                <div class="log-content">
<?php echo htmlspecialchars(getRecentLogs($error_log)); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
