<?php
/**
 * 测试过期设备检查功能
 * 用于验证设备使用时间限制功能是否正常工作
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 设备使用时间限制功能测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

try {
    // 创建连接
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // 检查连接
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset("utf8mb4");
    
    echo "✓ 数据库连接成功\n\n";
    
    // 1. 检查表是否存在
    echo "1. 检查数据表结构...\n";
    
    $tables_to_check = [
        'device_usage_limits' => '设备使用时间限制表',
        'device_blacklist' => '设备黑名单表',
        'devices' => '设备信息表',
        'device_groups' => '设备分组表'
    ];
    
    foreach ($tables_to_check as $table => $desc) {
        $result = $conn->query("SHOW TABLES LIKE '{$table}'");
        if ($result && $result->num_rows > 0) {
            echo "  ✓ {$desc} ({$table}) 存在\n";
        } else {
            echo "  ✗ {$desc} ({$table}) 不存在\n";
        }
    }
    
    echo "\n";
    
    // 2. 检查当前活跃的使用时间限制
    echo "2. 检查当前活跃的使用时间限制...\n";
    $limits_sql = "SELECT COUNT(*) as total, 
                   SUM(CASE WHEN end_time <= NOW() THEN 1 ELSE 0 END) as expired
                   FROM device_usage_limits 
                   WHERE is_active = 1";
    
    $result = $conn->query($limits_sql);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "  活跃限制总数: {$row['total']}\n";
        echo "  已过期数量: {$row['expired']}\n";
    } else {
        echo "  查询失败: " . $conn->error . "\n";
    }
    
    echo "\n";
    
    // 3. 检查设备分组
    echo "3. 检查设备分组...\n";
    $groups_sql = "SELECT COUNT(*) as total FROM device_groups";
    $result = $conn->query($groups_sql);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "  分组总数: {$row['total']}\n";
        
        if ($row['total'] > 0) {
            $groups_list_sql = "SELECT group_name FROM device_groups ORDER BY group_name LIMIT 5";
            $groups_result = $conn->query($groups_list_sql);
            echo "  分组列表 (前5个): ";
            $groups = [];
            while ($group_row = $groups_result->fetch_assoc()) {
                $groups[] = $group_row['group_name'];
            }
            echo implode(', ', $groups) . "\n";
        }
    } else {
        echo "  查询失败: " . $conn->error . "\n";
    }
    
    echo "\n";
    
    // 4. 检查黑名单状态
    echo "4. 检查黑名单状态...\n";
    $blacklist_sql = "SELECT COUNT(*) as total,
                      SUM(CASE WHEN blacklisted_by = 'usage-limit-system' THEN 1 ELSE 0 END) as auto_blacklisted
                      FROM device_blacklist 
                      WHERE is_active = 1";
    
    $result = $conn->query($blacklist_sql);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "  活跃黑名单总数: {$row['total']}\n";
        echo "  自动拉黑数量: {$row['auto_blacklisted']}\n";
    } else {
        echo "  查询失败: " . $conn->error . "\n";
    }
    
    echo "\n";
    
    // 5. 模拟检查过期设备（不实际执行拉黑操作）
    echo "5. 模拟检查过期设备...\n";
    $expired_sql = "SELECT device_id, device_model, blacklist_reason, end_time
                   FROM device_usage_limits 
                   WHERE is_active = 1 AND auto_blacklist = 1 AND end_time <= NOW()
                   LIMIT 10";
    
    $result = $conn->query($expired_sql);
    if ($result && $result->num_rows > 0) {
        echo "  发现过期设备:\n";
        while ($row = $result->fetch_assoc()) {
            echo "    - 设备ID: {$row['device_id']}, 型号: " . ($row['device_model'] ?? '未知') . ", 过期时间: {$row['end_time']}\n";
        }
    } else {
        echo "  ✓ 没有发现过期设备\n";
    }
    
    echo "\n";
    
    // 6. 检查日志文件
    echo "6. 检查日志文件...\n";
    $log_files = [
        'expired_devices_check.log' => '检查日志',
        'expired_devices_error.log' => '错误日志'
    ];
    
    foreach ($log_files as $file => $desc) {
        $log_path = __DIR__ . '/' . $file;
        if (file_exists($log_path)) {
            $size = filesize($log_path);
            $modified = date('Y-m-d H:i:s', filemtime($log_path));
            echo "  ✓ {$desc} ({$file}) 存在, 大小: {$size} 字节, 修改时间: {$modified}\n";
        } else {
            echo "  - {$desc} ({$file}) 不存在\n";
        }
    }
    
    echo "\n";
    
    // 7. 测试建议
    echo "7. 测试建议:\n";
    echo "  - 可以手动运行 check_expired_devices_cron.php 来测试定时检查功能\n";
    echo "  - 使用 setup_cron_task.bat 设置Windows定时任务\n";
    echo "  - 在 device_usage_limits.php 中测试分组批量设置功能\n";
    echo "  - 检查日志文件以监控系统运行状态\n";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== 测试完成 ===\n";
?>
