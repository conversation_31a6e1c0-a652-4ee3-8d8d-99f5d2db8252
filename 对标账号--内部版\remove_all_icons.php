<?php
/**
 * 批量移除所有PHP文件中的图标小表情
 * 使用方法：在命令行中运行 php remove_all_icons.php
 */

echo "开始移除所有PHP文件中的图标表情符号...\n";

// 获取当前目录下的所有PHP文件
$php_files = glob('*.php');

// 定义需要移除的图标和对应的替换文本
$icon_replacements = [
    // 按钮和操作相关
    '🗑️ 批量删除' => '批量删除',
    '🚫 批量拉黑' => '批量拉黑',
    '⚪ 批量解除拉黑' => '批量解除拉黑',
    '🔄 刷新' => '刷新',
    '⚙️ 操作' => '操作',
    '📁 分组' => '分组',
    '✅ 解除拉黑' => '解除拉黑',
    '🚫 拉黑' => '拉黑',
    '📊 详情' => '详情',
    '🖥️ 查看桌面' => '查看桌面',
    '📷 查看截图' => '查看截图',
    '💓 心跳详情' => '心跳详情',
    '🔧 系统管理' => '系统管理',
    
    // 模态框标题
    '🗑️ 删除分组' => '删除分组',
    '📁 创建新分组' => '创建新分组',
    '✏️ 编辑分组' => '编辑分组',
    '📊 设备粉丝记录' => '设备粉丝记录',
    '📊 设备心跳状态总览' => '设备心跳状态总览',
    '📁 分组详细统计' => '分组详细统计',
    '📱 设备详情' => '设备详情',
    '💓 设备心跳详情' => '设备心跳详情',
    '📊 设备ID统计报告' => '设备ID统计报告',
    
    // 状态和提示信息
    '📵 无截图' => '无截图',
    '🔄 执行中...' => '执行中...',
    '📈 统计概览' => '统计概览',
    '⚠️ 表不存在' => '表不存在',
    '❌ device_heartbeat 表不存在' => 'device_heartbeat 表不存在',
    '✅ device_blacklist 表存在' => 'device_blacklist 表存在',
    '❌ device_blacklist 表不存在' => 'device_blacklist 表不存在',
    '⏰ 有效期内记录数' => '有效期内记录数', 
    '✅ 应该显示的记录数' => '应该显示的记录数',
    '⚠️ 警告' => '警告',
    '✅ 已登录' => '已登录',
    '⚠️ 未登录' => '未登录',
    '🔒 您的连接已加密' => '您的连接已加密',
    '✅ 注销成功' => '注销成功',
    '⚠️ 发现重复记录！' => '发现重复记录！',
    '✅ 设备ID唯一性正常' => '设备ID唯一性正常',
    '📱 最近的设备ID示例' => '最近的设备ID示例',
    '❌ 错误:' => '错误:',
    '✅ 已成功' => '已成功',
    '❌ 操作失败' => '操作失败',
    '⚠️ 没有记录应该显示' => '没有记录应该显示',
    '⚠️ 数据库中没有黑名单记录' => '数据库中没有黑名单记录',
    '❌ 查询结果为空' => '查询结果为空',
    '⚠️ blacklisted_by 字段不存在，使用默认值' => 'blacklisted_by 字段不存在，使用默认值',
    '✅ 有记录应该显示，如果界面显示"暂无设备"，可能是前端问题' => '有记录应该显示，如果界面显示"暂无设备"，可能是前端问题',
    
    // 设备状态相关
    '📱 设备' => '设备',
    '🟢 在线' => '在线',
    '🔴 离线' => '离线',
    '🟡 警告' => '警告',
    '🟢 运行中' => '运行中',
    '🟡 空闲' => '空闲',
    '🔴 已停止' => '已停止',
    
    // 图标符号单独处理
    '🔒' => '',
    '📱' => '',
    '⚠️' => '',
    '✅' => '',
    '❌' => '',
    '🚫' => '',
    '📊' => '',
    '🔧' => '',
    '⏰' => '',
    '📈' => '',
    '📉' => '',
    '💡' => '',
    '🎯' => '',
    '🔄' => '',
    '📁' => '',
    '🗑️' => '',
    '⚙️' => '',
    '🟢' => '',
    '🔴' => '',
    '🟡' => '',
    '💓' => '',
    '📷' => '',
    '🖥️' => '',
    '📵' => '',
    '⏳' => '',
    '👁️' => '',
    '✏️' => '',
    '⚪' => '',
    '💾' => '',
    '🛡️' => '',
    '🔓' => '',
    '📋' => '',
    '📝' => '',
    '📤' => '',
    '📥' => '',
    '🎨' => '',
    '⚡' => '',
    '🌟' => '',
    '🚀' => '',
    '🌐' => '',
    '💻' => '',
    '🖱️' => '',
    '⌨️' => '',
    '🔍' => '',
    '📲' => '',
    '💰' => '',
    '🏆' => '',
    '🎮' => '',
    '🎪' => '',
    '🎭' => '',
    '🎯' => '',
    '🎰' => '',
    '🛠️' => '',
    '🔨' => '',
    '⭐' => '',
    '🌟' => '',
    '🎉' => '',
    '🎊' => '',
    '🎈' => '',
    '🎁' => '',
    '🎂' => '',
    '🎄' => '',
    '🎃' => '',
    '💝' => '',
    '💖' => '',
    '💗' => '',
    '💘' => '',
    '💙' => '',
    '💚' => '',
    '💛' => '',
    '💜' => '',
    '🖤' => '',
    '🤍' => '',
    '🤎' => '',
    '💔' => '',
    '❣️' => '',
    '💕' => '',
    '💞' => '',
    '💓' => '',
    '💗' => '',
    '💖' => '',
    '💘' => '',
    '💝' => '',
];

$total_files_processed = 0;
$total_replacements = 0;

foreach ($php_files as $file_path) {
    // 跳过备份文件和本脚本文件
    if (strpos($file_path, '.backup.') !== false || $file_path === 'remove_all_icons.php') {
        continue;
    }
    
    echo "\n处理文件: {$file_path}\n";
    
    // 检查文件是否存在
    if (!file_exists($file_path)) {
        echo "错误：文件 {$file_path} 不存在\n";
        continue;
    }
    
    // 读取文件内容
    $content = file_get_contents($file_path);
    if ($content === false) {
        echo "错误：无法读取文件 {$file_path}\n";
        continue;
    }
    
    $original_content = $content;
    $file_replacement_count = 0;
    
    foreach ($icon_replacements as $search => $replace) {
        $new_content = str_replace($search, $replace, $content);
        if ($new_content !== $content) {
            $count = substr_count($content, $search);
            $file_replacement_count += $count;
            echo "  替换 '{$search}' -> '{$replace}' ({$count} 次)\n";
            $content = $new_content;
        }
    }
    
    // 使用正则表达式移除其他可能的emoji字符
    $emoji_pattern = '/[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]/u';
    $content_after_regex = preg_replace($emoji_pattern, '', $content);
    
    if ($content_after_regex !== $content) {
        $regex_matches = preg_match_all($emoji_pattern, $content);
        if ($regex_matches) {
            echo "  通过正则表达式移除了 {$regex_matches} 个emoji字符\n";
            $file_replacement_count += $regex_matches;
        }
        $content = $content_after_regex;
    }
    
    // 如果有替换，则写入文件
    if ($content !== $original_content) {
        $backup_file = $file_path . '.backup.' . date('Y-m-d_H-i-s');
        
        // 创建备份
        if (copy($file_path, $backup_file)) {
            echo "  已创建备份文件：{$backup_file}\n";
        } else {
            echo "  警告：无法创建备份文件\n";
        }
        
        // 写入修改后的内容
        if (file_put_contents($file_path, $content) !== false) {
            echo "  成功移除图标！本文件替换了 {$file_replacement_count} 个图标\n";
            $total_files_processed++;
            $total_replacements += $file_replacement_count;
        } else {
            echo "  错误：无法写入文件 {$file_path}\n";
        }
    } else {
        echo "  没有找到需要替换的图标\n";
    }
}

echo "\n================================\n";
echo "图标移除完成！\n";
echo "总共处理了 {$total_files_processed} 个文件\n";
echo "总共替换了 {$total_replacements} 个图标\n";
echo "================================\n";
?>