<?php
// 设备使用时间限制管理
// 身份验证保护
require_once 'auth_middleware.php';

// 检查登录状态
require_login();

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

// 创建连接
$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");

// 确保device_usage_limits表存在（MySQL 5.0兼容版本）
$create_usage_limits_table = "CREATE TABLE IF NOT EXISTS device_usage_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(100) NOT NULL,
    device_model VARCHAR(255),
    limit_type ENUM('day','month','year') NOT NULL DEFAULT 'day',
    limit_value INT NOT NULL DEFAULT 1,
    start_time DATETIME NOT NULL,
    end_time DATETIME NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    auto_blacklist TINYINT(1) DEFAULT 1,
    blacklist_reason VARCHAR(255) DEFAULT '设备使用时间到期自动拉黑',
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL,
    UNIQUE KEY unique_device_limit (device_id),
    INDEX idx_device_id (device_id),
    INDEX idx_limit_type (limit_type),
    INDEX idx_is_active (is_active),
    INDEX idx_end_time (end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci";

$conn->query($create_usage_limits_table);

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');

    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'add_limit':
                // 检查权限
                require_permission('manage_devices');
                
                $device_id = trim($_POST['device_id'] ?? '');
                $device_model = trim($_POST['device_model'] ?? '');
                $limit_type = $_POST['limit_type'] ?? 'day';
                $limit_value = intval($_POST['limit_value'] ?? 1);
                $auto_blacklist = intval($_POST['auto_blacklist'] ?? 1);
                $blacklist_reason = trim($_POST['blacklist_reason'] ?? '设备使用时间到期自动拉黑');
                
                if (empty($device_id) || $limit_value <= 0) {
                    throw new Exception('设备ID不能为空且限制值必须大于0');
                }
                
                // 计算结束时间
                $start_time = date('Y-m-d H:i:s');
                switch ($limit_type) {
                    case 'day':
                        $end_time = date('Y-m-d H:i:s', strtotime("+{$limit_value} days"));
                        break;
                    case 'month':
                        $end_time = date('Y-m-d H:i:s', strtotime("+{$limit_value} months"));
                        break;
                    case 'year':
                        $end_time = date('Y-m-d H:i:s', strtotime("+{$limit_value} years"));
                        break;
                    default:
                        throw new Exception('无效的限制类型');
                }
                
                // 插入或更新限制
                $sql = "INSERT INTO device_usage_limits 
                        (device_id, device_model, limit_type, limit_value, start_time, end_time, is_active, auto_blacklist, blacklist_reason, created_by)
                        VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                        device_model = VALUES(device_model),
                        limit_type = VALUES(limit_type),
                        limit_value = VALUES(limit_value),
                        start_time = VALUES(start_time),
                        end_time = VALUES(end_time),
                        is_active = 1,
                        auto_blacklist = VALUES(auto_blacklist),
                        blacklist_reason = VALUES(blacklist_reason),
                        updated_at = NOW()";
                
                $stmt = $conn->prepare($sql);
                $user_info = get_current_user_info();
                $created_by = $user_info['name'] ?? 'system';
                
                $stmt->bind_param("sssississs", $device_id, $device_model, $limit_type, $limit_value, $start_time, $end_time, $auto_blacklist, $blacklist_reason, $created_by);
                
                if ($stmt->execute()) {
                    log_user_action('device_usage_limit_added', "Added usage limit for device: {$device_id}, type: {$limit_type}, value: {$limit_value}");
                    echo json_encode(['success' => true, 'message' => '设备使用时间限制设置成功']);
                } else {
                    throw new Exception('数据库操作失败: ' . $stmt->error);
                }
                break;
                
            case 'remove_limit':
                // 检查权限
                require_permission('manage_devices');
                
                $device_id = trim($_POST['device_id'] ?? '');
                if (empty($device_id)) {
                    throw new Exception('设备ID不能为空');
                }
                
                $sql = "UPDATE device_usage_limits SET is_active = 0, updated_at = NOW() WHERE device_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("s", $device_id);
                
                if ($stmt->execute()) {
                    log_user_action('device_usage_limit_removed', "Removed usage limit for device: {$device_id}");
                    echo json_encode(['success' => true, 'message' => '设备使用时间限制已移除']);
                } else {
                    throw new Exception('数据库操作失败: ' . $stmt->error);
                }
                break;
                
            case 'check_expired':
                // 检查并处理过期设备
                require_permission('manage_blacklist');
                
                $expired_sql = "SELECT device_id, device_model, blacklist_reason 
                               FROM device_usage_limits 
                               WHERE is_active = 1 AND auto_blacklist = 1 AND end_time <= NOW()";
                
                $result = $conn->query($expired_sql);
                $expired_count = 0;
                
                if ($result && $result->num_rows > 0) {
                    $conn->begin_transaction();
                    
                    try {
                        while ($row = $result->fetch_assoc()) {
                            $device_id = $row['device_id'];
                            $device_model = $row['device_model'] ?? '未知设备';
                            $reason = $row['blacklist_reason'];
                            
                            // 检查是否已在黑名单中
                            $check_blacklist = "SELECT COUNT(*) as count FROM device_blacklist WHERE device_id = ? AND is_active = 1";
                            $check_stmt = $conn->prepare($check_blacklist);
                            $check_stmt->bind_param("s", $device_id);
                            $check_stmt->execute();
                            $check_result = $check_stmt->get_result();
                            $is_blacklisted = $check_result->fetch_assoc()['count'] > 0;
                            $check_stmt->close();
                            
                            if (!$is_blacklisted) {
                                // 添加到黑名单
                                $blacklist_sql = "INSERT INTO device_blacklist 
                                                 (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, blacklisted_by, updated_at)
                                                 VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, 'usage-limit-system', NOW())";
                                
                                $blacklist_stmt = $conn->prepare($blacklist_sql);
                                $blacklist_stmt->bind_param("sss", $device_id, $device_model, $reason);
                                $blacklist_stmt->execute();
                                $blacklist_stmt->close();
                                
                                $expired_count++;
                            }
                            
                            // 将使用时间限制设为非活跃
                            $deactivate_sql = "UPDATE device_usage_limits SET is_active = 0, updated_at = NOW() WHERE device_id = ?";
                            $deactivate_stmt = $conn->prepare($deactivate_sql);
                            $deactivate_stmt->bind_param("s", $device_id);
                            $deactivate_stmt->execute();
                            $deactivate_stmt->close();
                        }
                        
                        $conn->commit();
                        log_user_action('device_usage_expired_check', "Processed {$expired_count} expired devices");
                        echo json_encode(['success' => true, 'message' => "已处理 {$expired_count} 个过期设备", 'count' => $expired_count]);
                        
                    } catch (Exception $e) {
                        $conn->rollback();
                        throw $e;
                    }
                } else {
                    echo json_encode(['success' => true, 'message' => '没有发现过期设备', 'count' => 0]);
                }
                break;

            case 'get_device_groups':
                // 获取设备分组列表
                $groups_sql = "SELECT group_name FROM device_groups ORDER BY group_name";
                $groups_result = $conn->query($groups_sql);
                $groups = [];

                if ($groups_result && $groups_result->num_rows > 0) {
                    while ($row = $groups_result->fetch_assoc()) {
                        $groups[] = $row['group_name'];
                    }
                }

                // 如果device_groups表为空，从实际设备数据中获取分组
                if (empty($groups)) {
                    $devices_groups_sql = "SELECT DISTINCT device_group FROM devices
                                          WHERE device_group IS NOT NULL AND device_group != ''
                                          ORDER BY device_group";
                    $devices_result = $conn->query($devices_groups_sql);

                    if ($devices_result && $devices_result->num_rows > 0) {
                        while ($row = $devices_result->fetch_assoc()) {
                            if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                                $groups[] = $row['device_group'];
                            }
                        }
                    }
                }

                echo json_encode(['success' => true, 'groups' => $groups]);
                break;

            case 'get_devices_by_group':
                // 根据分组获取设备列表
                $group_name = trim($_POST['group_name'] ?? '');

                if (empty($group_name)) {
                    throw new Exception('分组名称不能为空');
                }

                $devices_sql = "SELECT device_id, device_model FROM devices WHERE device_group = ? ORDER BY device_id";
                $stmt = $conn->prepare($devices_sql);
                $stmt->bind_param("s", $group_name);
                $stmt->execute();
                $result = $stmt->get_result();

                $devices = [];
                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $devices[] = [
                            'device_id' => $row['device_id'],
                            'device_model' => $row['device_model'] ?? '未知设备'
                        ];
                    }
                }

                echo json_encode(['success' => true, 'devices' => $devices]);
                break;

            case 'add_group_limit':
                // 批量为分组设备添加使用时间限制
                require_permission('manage_devices');

                $group_name = trim($_POST['group_name'] ?? '');
                $limit_type = $_POST['limit_type'] ?? 'day';
                $limit_value = intval($_POST['limit_value'] ?? 1);
                $auto_blacklist = intval($_POST['auto_blacklist'] ?? 1);
                $blacklist_reason = trim($_POST['blacklist_reason'] ?? '设备使用时间到期自动拉黑');

                if (empty($group_name) || $limit_value <= 0) {
                    throw new Exception('分组名称不能为空且限制值必须大于0');
                }

                // 获取分组中的所有设备
                $devices_sql = "SELECT device_id, device_model FROM devices WHERE device_group = ?";
                $stmt = $conn->prepare($devices_sql);
                $stmt->bind_param("s", $group_name);
                $stmt->execute();
                $devices_result = $stmt->get_result();

                if (!$devices_result || $devices_result->num_rows === 0) {
                    throw new Exception("分组「{$group_name}」中没有找到设备");
                }

                // 计算结束时间
                $start_time = date('Y-m-d H:i:s');
                switch ($limit_type) {
                    case 'day':
                        $end_time = date('Y-m-d H:i:s', strtotime("+{$limit_value} days"));
                        break;
                    case 'month':
                        $end_time = date('Y-m-d H:i:s', strtotime("+{$limit_value} months"));
                        break;
                    case 'year':
                        $end_time = date('Y-m-d H:i:s', strtotime("+{$limit_value} years"));
                        break;
                    default:
                        throw new Exception('无效的限制类型');
                }

                $conn->begin_transaction();
                $success_count = 0;
                $error_devices = [];

                try {
                    $user_info = get_current_user_info();
                    $created_by = $user_info['name'] ?? 'system';

                    while ($device = $devices_result->fetch_assoc()) {
                        $device_id = $device['device_id'];
                        $device_model = $device['device_model'] ?? '未知设备';

                        // 插入或更新限制
                        $sql = "INSERT INTO device_usage_limits
                                (device_id, device_model, limit_type, limit_value, start_time, end_time, is_active, auto_blacklist, blacklist_reason, created_by, updated_at)
                                VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?, ?, NOW())
                                ON DUPLICATE KEY UPDATE
                                device_model = VALUES(device_model),
                                limit_type = VALUES(limit_type),
                                limit_value = VALUES(limit_value),
                                start_time = VALUES(start_time),
                                end_time = VALUES(end_time),
                                is_active = 1,
                                auto_blacklist = VALUES(auto_blacklist),
                                blacklist_reason = VALUES(blacklist_reason),
                                updated_at = NOW()";

                        $limit_stmt = $conn->prepare($sql);
                        $limit_stmt->bind_param("sssississs", $device_id, $device_model, $limit_type, $limit_value, $start_time, $end_time, $auto_blacklist, $blacklist_reason, $created_by);

                        if ($limit_stmt->execute()) {
                            $success_count++;
                        } else {
                            $error_devices[] = $device_id;
                        }
                        $limit_stmt->close();
                    }

                    $conn->commit();

                    if ($success_count > 0) {
                        log_user_action('device_group_usage_limit_added', "Added usage limit for group: {$group_name}, devices: {$success_count}, type: {$limit_type}, value: {$limit_value}");
                        $message = "成功为分组「{$group_name}」中的 {$success_count} 个设备设置使用时间限制";
                        if (!empty($error_devices)) {
                            $message .= "，失败设备：" . implode(', ', $error_devices);
                        }
                        echo json_encode(['success' => true, 'message' => $message, 'count' => $success_count]);
                    } else {
                        throw new Exception('没有设备成功设置限制');
                    }

                } catch (Exception $e) {
                    $conn->rollback();
                    throw $e;
                }
                break;

            default:
                throw new Exception('无效的操作');
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    
    exit();
}

// 处理GET请求
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action'])) {
    header('Content-Type: application/json; charset=utf-8');

    $action = $_GET['action'];

    try {
        switch ($action) {
            case 'get_device_groups':
                // 获取设备分组列表
                $groups_sql = "SELECT group_name FROM device_groups ORDER BY group_name";
                $groups_result = $conn->query($groups_sql);
                $groups = [];

                if ($groups_result && $groups_result->num_rows > 0) {
                    while ($row = $groups_result->fetch_assoc()) {
                        $groups[] = $row['group_name'];
                    }
                }

                // 如果device_groups表为空，从实际设备数据中获取分组
                if (empty($groups)) {
                    $devices_groups_sql = "SELECT DISTINCT device_group FROM devices
                                          WHERE device_group IS NOT NULL AND device_group != ''
                                          ORDER BY device_group";
                    $devices_result = $conn->query($devices_groups_sql);

                    if ($devices_result && $devices_result->num_rows > 0) {
                        while ($row = $devices_result->fetch_assoc()) {
                            if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                                $groups[] = $row['device_group'];
                            }
                        }
                    }
                }

                echo json_encode(['success' => true, 'groups' => $groups]);
                break;

            default:
                throw new Exception('无效的操作');
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }

    exit();
}

// 获取所有活跃的使用时间限制
$limits_sql = "SELECT l.*, 
               CASE 
                   WHEN l.end_time <= NOW() THEN '已过期'
                   ELSE CONCAT(TIMESTAMPDIFF(DAY, NOW(), l.end_time), '天', TIMESTAMPDIFF(HOUR, NOW(), l.end_time) % 24, '小时')
               END as remaining_time,
               CASE WHEN l.end_time <= NOW() THEN 1 ELSE 0 END as is_expired
               FROM device_usage_limits l 
               WHERE l.is_active = 1 
               ORDER BY l.end_time ASC";

$limits_result = $conn->query($limits_sql);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备使用时间限制管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .nav-buttons {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .nav-buttons a, .nav-buttons button {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .nav-buttons a:hover, .nav-buttons button:hover {
            background: #0056b3;
        }
        
        .content {
            padding: 20px;
        }
        
        .add-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .limits-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .limits-table th, .limits-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .limits-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-expired {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 5px;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        /* 选项卡样式 */
        .tab-buttons {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn:hover {
            color: #007bff;
            background: #f8f9fa;
        }

        .tab-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: bold;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 设备筛选样式 */
        #deviceIdSelect, #manualDeviceId {
            width: 100%;
        }

        #singleDeviceCount {
            background-color: #f8f9fa;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备使用时间限制管理</h1>
            <p>设置设备使用时间限制，到期自动拉黑功能</p>
        </div>
        
        <div class="content">
            <div class="nav-buttons">
                <a href="monitor.php">返回监控面板</a>
                <a href="monitor.php?view=cron_tasks">定时任务管理</a>
            </div>
            
            <div class="add-form">
                <h3>添加设备使用时间限制</h3>

                <!-- 添加选项卡 -->
                <div class="tab-buttons">
                    <button type="button" class="tab-btn active" onclick="switchTab('single')">单个设备</button>
                    <button type="button" class="tab-btn" onclick="switchTab('group')">按分组批量</button>
                </div>

                <!-- 单个设备表单 -->
                <div id="single-form" class="tab-content active">
                    <form id="addLimitForm">
                        <!-- 分组筛选区域 -->
                        <div class="form-row">
                            <div class="form-group">
                                <label>按分组筛选设备:</label>
                                <select id="singleGroupFilter" onchange="filterDevicesByGroup()">
                                    <option value="">全部设备</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>设备数量:</label>
                                <input type="text" id="singleDeviceCount" readonly placeholder="选择分组后显示设备数量">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>选择设备ID:</label>
                                <select name="device_id" id="deviceIdSelect" required>
                                    <option value="">请先选择分组或直接输入设备ID</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>或直接输入设备ID:</label>
                                <input type="text" id="manualDeviceId" placeholder="手动输入设备ID" onchange="handleManualDeviceId()">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>设备型号:</label>
                                <input type="text" name="device_model" placeholder="设备型号（可选）">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>限制类型:</label>
                                <select name="limit_type" required>
                                    <option value="day">按天</option>
                                    <option value="month">按月</option>
                                    <option value="year">按年</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>限制数值:</label>
                                <input type="number" name="limit_value" min="1" value="1" required placeholder="数值">
                            </div>
                            <div class="form-group">
                                <label>到期自动拉黑:</label>
                                <select name="auto_blacklist">
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>拉黑原因:</label>
                                <input type="text" name="blacklist_reason" value="设备使用时间到期自动拉黑" placeholder="拉黑原因">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success">添加限制</button>
                    </form>
                </div>

                <!-- 分组批量表单 -->
                <div id="group-form" class="tab-content">
                    <form id="addGroupLimitForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>选择设备分组:</label>
                                <select name="group_name" id="groupSelect" required>
                                    <option value="">请选择分组</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>分组设备数量:</label>
                                <input type="text" id="deviceCount" readonly placeholder="选择分组后显示设备数量">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>限制类型:</label>
                                <select name="limit_type" required>
                                    <option value="day">按天</option>
                                    <option value="month">按月</option>
                                    <option value="year">按年</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>限制数值:</label>
                                <input type="number" name="limit_value" min="1" value="1" required placeholder="数值">
                            </div>
                            <div class="form-group">
                                <label>到期自动拉黑:</label>
                                <select name="auto_blacklist">
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>拉黑原因:</label>
                                <input type="text" name="blacklist_reason" value="设备使用时间到期自动拉黑" placeholder="拉黑原因">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success">批量添加限制</button>
                    </form>
                </div>
            </div>
            
            <div>
                <h3>当前设备使用时间限制</h3>
                <?php if ($limits_result && $limits_result->num_rows > 0): ?>
                    <table class="limits-table">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备型号</th>
                                <th>限制类型</th>
                                <th>限制值</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>剩余时间</th>
                                <th>状态</th>
                                <th>自动拉黑</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = $limits_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($row['device_id']); ?></td>
                                    <td><?php echo htmlspecialchars($row['device_model'] ?? '未知'); ?></td>
                                    <td>
                                        <?php 
                                        $type_map = ['day' => '天', 'month' => '月', 'year' => '年'];
                                        echo $type_map[$row['limit_type']] ?? $row['limit_type']; 
                                        ?>
                                    </td>
                                    <td><?php echo $row['limit_value']; ?></td>
                                    <td><?php echo $row['start_time']; ?></td>
                                    <td><?php echo $row['end_time']; ?></td>
                                    <td><?php echo $row['remaining_time']; ?></td>
                                    <td>
                                        <span class="<?php echo $row['is_expired'] ? 'status-expired' : 'status-active'; ?>">
                                            <?php echo $row['is_expired'] ? '已过期' : '活跃'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $row['auto_blacklist'] ? '是' : '否'; ?></td>
                                    <td>
                                        <button class="btn btn-danger" onclick="removeLimit('<?php echo htmlspecialchars($row['device_id']); ?>')">
                                            移除限制
                                        </button>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="alert alert-info">
                        暂无设备使用时间限制记录
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDeviceGroups();
            loadSingleDeviceGroups();
        });

        // 选项卡切换功能
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有按钮的活跃状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的选项卡内容
            document.getElementById(tabName + '-form').classList.add('active');

            // 激活对应的按钮
            event.target.classList.add('active');
        }

        // 加载设备分组（批量表单用）
        function loadDeviceGroups() {
            fetch('device_usage_limits.php?action=get_device_groups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const groupSelect = document.getElementById('groupSelect');
                        if (groupSelect) {
                            groupSelect.innerHTML = '<option value="">请选择分组</option>';

                            data.groups.forEach(group => {
                                const option = document.createElement('option');
                                option.value = group;
                                option.textContent = group;
                                groupSelect.appendChild(option);
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('加载分组失败:', error);
                });
        }

        // 加载设备分组（单个设备表单用）
        function loadSingleDeviceGroups() {
            fetch('device_usage_limits.php?action=get_device_groups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const singleGroupFilter = document.getElementById('singleGroupFilter');
                        if (singleGroupFilter) {
                            singleGroupFilter.innerHTML = '<option value="">全部设备</option>';

                            data.groups.forEach(group => {
                                const option = document.createElement('option');
                                option.value = group;
                                option.textContent = group;
                                singleGroupFilter.appendChild(option);
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('加载单个设备分组失败:', error);
                });
        }

        // 根据分组筛选设备
        function filterDevicesByGroup() {
            const groupName = document.getElementById('singleGroupFilter').value;
            const deviceSelect = document.getElementById('deviceIdSelect');
            const deviceCountInput = document.getElementById('singleDeviceCount');

            if (groupName) {
                const formData = new FormData();
                formData.append('action', 'get_devices_by_group');
                formData.append('group_name', groupName);

                fetch('device_usage_limits.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        deviceSelect.innerHTML = '<option value="">请选择设备</option>';

                        data.devices.forEach(device => {
                            const option = document.createElement('option');
                            option.value = device.device_id;
                            option.textContent = `${device.device_id} (${device.device_model})`;
                            deviceSelect.appendChild(option);
                        });

                        deviceCountInput.value = data.devices.length + ' 个设备';
                    } else {
                        deviceSelect.innerHTML = '<option value="">获取设备失败</option>';
                        deviceCountInput.value = '获取失败';
                    }
                })
                .catch(error => {
                    deviceSelect.innerHTML = '<option value="">获取设备失败</option>';
                    deviceCountInput.value = '获取失败';
                    console.error('获取设备失败:', error);
                });
            } else {
                deviceSelect.innerHTML = '<option value="">请先选择分组或直接输入设备ID</option>';
                deviceCountInput.value = '';
            }
        }

        // 处理手动输入设备ID
        function handleManualDeviceId() {
            const manualInput = document.getElementById('manualDeviceId');
            const deviceSelect = document.getElementById('deviceIdSelect');

            if (manualInput.value.trim()) {
                // 清空分组选择
                document.getElementById('singleGroupFilter').value = '';
                document.getElementById('singleDeviceCount').value = '';

                // 设置设备选择框
                deviceSelect.innerHTML = `<option value="${manualInput.value.trim()}">${manualInput.value.trim()}</option>`;
                deviceSelect.value = manualInput.value.trim();
            }
        }

        // 分组选择变化时获取设备数量
        document.addEventListener('change', function(e) {
            if (e.target.id === 'groupSelect') {
                const groupName = e.target.value;
                const deviceCountInput = document.getElementById('deviceCount');

                if (groupName) {
                    const formData = new FormData();
                    formData.append('action', 'get_devices_by_group');
                    formData.append('group_name', groupName);

                    fetch('device_usage_limits.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            deviceCountInput.value = data.devices.length + ' 个设备';
                        } else {
                            deviceCountInput.value = '获取失败';
                        }
                    })
                    .catch(error => {
                        deviceCountInput.value = '获取失败';
                        console.error('获取设备数量失败:', error);
                    });
                } else {
                    deviceCountInput.value = '';
                }
            }
        });

        // 单个设备表单提交
        document.getElementById('addLimitForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'add_limit');

            fetch('device_usage_limits.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('错误: ' + data.error);
                }
            })
            .catch(error => {
                alert('请求失败: ' + error.message);
            });
        });

        // 分组批量表单提交
        document.getElementById('addGroupLimitForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'add_group_limit');

            const groupName = formData.get('group_name');
            if (!groupName) {
                alert('请选择设备分组');
                return;
            }

            if (confirm(`确定要为分组「${groupName}」中的所有设备设置使用时间限制吗？`)) {
                fetch('device_usage_limits.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('错误: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('请求失败: ' + error.message);
                });
            }
        });
        });

        // 移除限制
        function removeLimit(deviceId) {
            if (confirm('确定要移除设备 ' + deviceId + ' 的使用时间限制吗？')) {
                const formData = new FormData();
                formData.append('action', 'remove_limit');
                formData.append('device_id', deviceId);
                
                fetch('device_usage_limits.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('错误: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('请求失败: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>