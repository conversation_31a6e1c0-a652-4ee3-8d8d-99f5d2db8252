<?php
// 黑名单调试脚本

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

// 创建连接
$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");

echo "<h2>黑名单调试信息</h2>";

try {
    // 1. 检查表是否存在
    $check_table_sql = "SHOW TABLES LIKE 'device_blacklist'";
    $table_result = $conn->query($check_table_sql);
    
    if ($table_result && $table_result->num_rows > 0) {
        echo "<p>device_blacklist 表存在</p>";
        
        // 2. 检查表结构
        echo "<h3>表结构：</h3>";
        $describe_sql = "DESCRIBE device_blacklist";
        $describe_result = $conn->query($describe_sql);
        
        if ($describe_result) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
            while ($row = $describe_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>{$row['Default']}</td>";
                echo "<td>{$row['Extra']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 3. 检查数据总数
        $total_sql = "SELECT COUNT(*) as total FROM device_blacklist";
        $total_result = $conn->query($total_sql);
        $total_count = $total_result ? $total_result->fetch_assoc()['total'] : 0;
        echo "<p>总记录数：{$total_count}</p>";
        
        if ($total_count > 0) {
            // 4. 显示所有记录的详细信息
            echo "<h3>所有黑名单记录：</h3>";
            $all_sql = "SELECT * FROM device_blacklist ORDER BY blacklist_time DESC";
            $all_result = $conn->query($all_sql);
            
            if ($all_result && $all_result->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>";
                echo "<tr>";
                echo "<th>ID</th><th>设备ID</th><th>原因</th><th>is_active</th>";
                echo "<th>blacklist_time</th><th>blacklist_end_time</th><th>操作人</th>";
                echo "</tr>";
                
                while ($row = $all_result->fetch_assoc()) {
                    $is_active = isset($row['is_active']) ? $row['is_active'] : 'NULL';
                    $end_time = isset($row['blacklist_end_time']) ? $row['blacklist_end_time'] : 'NULL';
                    $now = date('Y-m-d H:i:s');
                    
                    // 判断是否应该显示
                    $should_show = false;
                    if (isset($row['is_active']) && isset($row['blacklist_end_time'])) {
                        $should_show = ($row['is_active'] == 1 && $row['blacklist_end_time'] > $now);
                    }
                    
                    $row_style = $should_show ? "background-color: #d4edda;" : "background-color: #f8d7da;";
                    
                    echo "<tr style='{$row_style}'>";
                    echo "<td>{$row['id']}</td>";
                    echo "<td>{$row['device_id']}</td>";
                    echo "<td>" . substr($row['blacklist_reason'] ?? '', 0, 30) . "...</td>";
                    echo "<td>{$is_active}</td>";
                    echo "<td>{$row['blacklist_time']}</td>";
                    echo "<td>{$end_time}</td>";
                    echo "<td>" . (isset($row['blacklisted_by']) ? $row['blacklisted_by'] : 'system') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "<p><small>绿色背景：应该在黑名单中显示的记录<br>红色背景：不应该显示的记录</small></p>";
            }
            
            // 5. 检查活跃记录
            $active_sql = "SELECT COUNT(*) as active_count FROM device_blacklist WHERE is_active = 1";
            $active_result = $conn->query($active_sql);
            $active_count = $active_result ? $active_result->fetch_assoc()['active_count'] : 0;
            echo "<p>🟢 活跃记录数 (is_active = 1)：{$active_count}</p>";
            
            // 6. 检查有效期内的记录
            $valid_sql = "SELECT COUNT(*) as valid_count FROM device_blacklist WHERE blacklist_end_time > NOW()";
            $valid_result = $conn->query($valid_sql);
            $valid_count = $valid_result ? $valid_result->fetch_assoc()['valid_count'] : 0;
            echo "<p>有效期内记录数 (blacklist_end_time > NOW())：{$valid_count}</p>";
            
            // 7. 检查同时满足两个条件的记录
            $both_sql = "SELECT COUNT(*) as both_count FROM device_blacklist WHERE is_active = 1 AND blacklist_end_time > NOW()";
            $both_result = $conn->query($both_sql);
            $both_count = $both_result ? $both_result->fetch_assoc()['both_count'] : 0;
            echo "<p>应该显示的记录数 (is_active = 1 AND blacklist_end_time > NOW())：{$both_count}</p>";
            
            // 8. 测试实际查询
            echo "<h3>实际查询结果：</h3>";

            // 检查blacklisted_by字段是否存在
            $check_blacklisted_by = "SHOW COLUMNS FROM device_blacklist LIKE 'blacklisted_by'";
            $blacklisted_by_exists = $conn->query($check_blacklisted_by);

            if ($blacklisted_by_exists && $blacklisted_by_exists->num_rows > 0) {
                $test_sql = "SELECT device_id, blacklist_reason, blacklisted_by, blacklist_time, blacklist_end_time, is_active
                            FROM device_blacklist
                            WHERE is_active = 1 AND blacklist_end_time > NOW()
                            ORDER BY blacklist_time DESC";
            } else {
                echo "<p style='color: orange;'>blacklisted_by 字段不存在，使用默认值</p>";
                $test_sql = "SELECT device_id, blacklist_reason, 'system' as blacklisted_by, blacklist_time, blacklist_end_time, is_active
                            FROM device_blacklist
                            WHERE is_active = 1 AND blacklist_end_time > NOW()
                            ORDER BY blacklist_time DESC";
            }
            $test_result = $conn->query($test_sql);
            
            if ($test_result && $test_result->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>设备ID</th><th>拉黑原因</th><th>操作人</th><th>拉黑时间</th><th>结束时间</th><th>活跃状态</th></tr>";
                while ($row = $test_result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>{$row['device_id']}</td>";
                    echo "<td>{$row['blacklist_reason']}</td>";
                    echo "<td>{$row['blacklisted_by']}</td>";
                    echo "<td>{$row['blacklist_time']}</td>";
                    echo "<td>{$row['blacklist_end_time']}</td>";
                    echo "<td>{$row['is_active']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>查询结果为空</p>";
                
                // 检查查询是否有错误
                if ($conn->error) {
                    echo "<p style='color: red;'>SQL错误：{$conn->error}</p>";
                }
            }
            
        } else {
            echo "<p>数据库中没有黑名单记录</p>";
        }
        
    } else {
        echo "<p>device_blacklist 表不存在</p>";
    }
    
    // 9. 检查当前时间
    $current_time_sql = "SELECT NOW() as current_time";
    $current_time_result = $conn->query($current_time_sql);
    if ($current_time_result) {
        $current_time = $current_time_result->fetch_assoc()['current_time'];
        echo "<p>🕐 数据库当前时间：{$current_time}</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误：{$e->getMessage()}</p>";
}

$conn->close();
?>

<style>
table {
    margin: 10px 0;
}
th, td {
    padding: 5px 10px;
    text-align: left;
}
th {
    background-color: #f0f0f0;
}
</style>
