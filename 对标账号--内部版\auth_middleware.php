<?php
// 身份验证中间件
// 用于保护需要登录的页面

require_once 'auth_config.php';

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 检查用户是否已登录
function require_login() {
    global $session_config;
    
    // 检查基本登录状态
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        redirect_to_login();
        return false;
    }
    
    // 检查会话超时
    if (isset($_SESSION['last_activity'])) {
        $inactive_time = time() - $_SESSION['last_activity'];
        if ($inactive_time > $session_config['session_timeout']) {
            session_destroy();
            redirect_to_login('会话已超时，请重新登录');
            return false;
        }
    }
    
    // 检查IP地址变化（安全检查）
    if (isset($_SESSION['client_ip'])) {
        $current_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        if ($_SESSION['client_ip'] !== $current_ip) {
            session_destroy();
            redirect_to_login('检测到IP地址变化，请重新登录');
            return false;
        }
    }
    
    // 更新最后活动时间
    $_SESSION['last_activity'] = time();
    
    return true;
}

// 检查特定权限
function require_permission($permission) {
    if (!require_login()) {
        return false;
    }
    
    if (!check_permission($permission)) {
        show_access_denied();
        return false;
    }
    
    return true;
}

// 重定向到登录页面
function redirect_to_login($message = '') {
    if (!empty($message)) {
        $_SESSION['login_message'] = $message;
    }
    
    // 保存当前页面URL，登录后可以返回
    if (!isset($_SESSION['redirect_after_login'])) {
        $current_url = $_SERVER['REQUEST_URI'] ?? '';
        if (!empty($current_url) && $current_url !== '/login.php') {
            $_SESSION['redirect_after_login'] = $current_url;
        }
    }
    
    header('Location: login.php');
    exit;
}

// 显示访问被拒绝页面
function show_access_denied() {
    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>访问被拒绝</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
            }
            .error-container {
                background: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                text-align: center;
                max-width: 500px;
            }
            .error-icon {
                font-size: 64px;
                color: #e74c3c;
                margin-bottom: 20px;
            }
            .error-title {
                font-size: 24px;
                color: #333;
                margin-bottom: 15px;
            }
            .error-message {
                color: #666;
                margin-bottom: 30px;
                line-height: 1.6;
            }
            .error-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
            }
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                text-decoration: none;
                font-size: 14px;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            .btn-primary {
                background-color: #667eea;
                color: white;
            }
            .btn-primary:hover {
                background-color: #5a6fd8;
            }
            .btn-secondary {
                background-color: #6c757d;
                color: white;
            }
            .btn-secondary:hover {
                background-color: #5a6268;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
                            <div class="error-icon"></div>
            <h1 class="error-title">访问被拒绝</h1>
            <p class="error-message">
                抱歉，您没有权限访问此页面。<br>
                请联系管理员获取相应权限，或使用有权限的账户登录。
            </p>
            <div class="error-actions">
                <a href="javascript:history.back()" class="btn btn-secondary">返回上页</a>
                <a href="monitor.php" class="btn btn-primary">返回首页</a>
                <a href="logout.php" class="btn btn-secondary">重新登录</a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 获取当前用户信息
function get_current_user_info() {
    if (!isset($_SESSION['username'])) {
        return null;
    }

    return [
        'username' => $_SESSION['username'],
        'name' => $_SESSION['user_name'] ?? $_SESSION['username'],
        'role' => $_SESSION['user_role'] ?? 'guest',
        'login_time' => $_SESSION['login_time'] ?? 0,
        'last_activity' => $_SESSION['last_activity'] ?? 0,
        'client_ip' => $_SESSION['client_ip'] ?? '',
        'auto_login' => $_SESSION['auto_login'] ?? false
    ];
}

// 生成用户菜单HTML
function generate_user_menu() {
    $user = get_current_user_info();
    if (!$user) {
        return '';
    }

    $login_time = date('Y-m-d H:i:s', $user['login_time']);
    $auto_login_badge = $user['auto_login'] ? '<span class="auto-login-badge">自动</span>' : '';

    // 检查是否为系统管理员
    $is_admin = ($user['role'] === 'admin');
    $admin_nav = '';

    if ($is_admin) {
        $admin_nav = '<a href="javascript:void(0)" onclick="accessAdminIndex()" class="admin-nav-btn" title="系统管理入口">系统管理</a>';
    }

    return '
    <div class="user-menu">
        <div class="user-info">
            <span class="user-name">' . htmlspecialchars($user['name']) . '</span>
            <span class="user-role">' . htmlspecialchars($user['role']) . '</span>
            ' . $auto_login_badge . '
        </div>
        <div class="user-actions">
            <span class="login-time">登录时间: ' . $login_time . '</span>
            ' . $admin_nav . '
            <a href="logout.php" class="logout-btn">退出登录</a>
        </div>
    </div>';
}

// 生成用户菜单CSS
function generate_user_menu_css() {
    return '
    <style>
    .user-menu {
        position: fixed;
        top: 10px;
        right: 10px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        min-width: 250px;
    }
    
    .user-info {
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    
    .user-name {
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 5px;
    }
    
    .user-role {
        background: #667eea;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        text-transform: uppercase;
    }
    
    .auto-login-badge {
        background: #28a745;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        margin-left: 5px;
    }
    
    .user-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .login-time {
        font-size: 11px;
        color: #666;
    }
    
    .admin-nav-btn {
        background: #28a745;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 12px;
        margin-right: 8px;
        transition: background-color 0.3s;
        cursor: pointer;
        border: none;
        display: inline-block;
    }

    .admin-nav-btn:hover {
        background: #218838;
        color: white;
        text-decoration: none;
    }

    .logout-btn {
        background: #dc3545;
        color: white;
        padding: 5px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 12px;
        transition: background-color 0.3s;
    }

    .logout-btn:hover {
        background: #c82333;
        text-decoration: none;
        color: white;
    }
    
    @media (max-width: 768px) {
        .user-menu {
            position: relative;
            top: auto;
            right: auto;
            margin-bottom: 20px;
            width: 100%;
        }
    }
    </style>';
}

// 检查AJAX请求的身份验证
function require_ajax_auth() {
    if (!require_login()) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => '未登录或会话已过期',
            'redirect' => 'login.php'
        ]);
        exit;
    }
}

// 检查AJAX请求的权限
function require_ajax_permission($permission) {
    require_ajax_auth();
    
    if (!check_permission($permission)) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => '权限不足',
            'required_permission' => $permission
        ]);
        exit;
    }
}

// 记录用户操作日志
function log_user_action($action, $details = '') {
    $user = get_current_user_info();
    if (!$user) {
        return;
    }
    
    $log_file = __DIR__ . '/user_actions.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $log_entry = "[{$timestamp}] User: {$user['username']} ({$user['role']}) | IP: {$ip} | Action: {$action}";
    if (!empty($details)) {
        $log_entry .= " | Details: {$details}";
    }
    $log_entry .= " | UA: {$user_agent}\n";
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}
?>
