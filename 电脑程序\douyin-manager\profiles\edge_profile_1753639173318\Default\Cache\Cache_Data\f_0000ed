"undefined"!=typeof navigator&&function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).lottie=e()}(this,(function(){"use strict";var t="",e=!1,i=-999999,s=function(){return t};function a(t){return document.createElement(t)}function r(t,e){var i,s,a=t.length;for(i=0;i<a;i+=1)for(var r in s=t[i].prototype)Object.prototype.hasOwnProperty.call(s,r)&&(e.prototype[r]=s[r])}function n(t){function e(){}return e.prototype=t,e}var o=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,i=this.audios.length;for(e=0;e<i;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),h=function(){function t(t,e){var i,s=0,a=[];switch(t){case"int16":case"uint8c":i=1;break;default:i=1.1}for(s=0;s<e;s+=1)a.push(i);return a}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,i){return"float32"===e?new Float32Array(i):"int16"===e?new Int16Array(i):"uint8c"===e?new Uint8ClampedArray(i):t(e,i)}:t}();function l(t){return Array.apply(null,{length:t})}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}var f=!0,d=null,m=null,c="",u=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),g=Math.pow,y=Math.sqrt,v=Math.floor,b=(Math.max,Math.min),_={};!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],i=e.length;for(t=0;t<i;t+=1)_[e[t]]=Math[e[t]]}(),_.random=Math.random,_.abs=function(t){if("object"===p(t)&&t.length){var e,i=l(t.length),s=t.length;for(e=0;e<s;e+=1)i[e]=Math.abs(t[e]);return i}return Math.abs(t)};var k=150,P=Math.PI/180,A=.5519;function S(t){!!t}function x(t,e,i,s){this.type=t,this.currentTime=e,this.totalTime=i,this.direction=s<0?-1:1}function w(t,e){this.type=t,this.direction=e<0?-1:1}function D(t,e,i,s){this.type=t,this.currentLoop=i,this.totalLoops=e,this.direction=s<0?-1:1}function C(t,e,i){this.type=t,this.firstFrame=e,this.totalFrames=i}function M(t,e){this.type=t,this.target=e}function T(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function F(t){this.type="configError",this.nativeError=t}var E,I=(E=0,function(){return c+"__lottie_element_"+(E+=1)});function L(t,e,i){var s,a,r,n,o,h,l,p;switch(h=i*(1-e),l=i*(1-(o=6*t-(n=Math.floor(6*t)))*e),p=i*(1-(1-o)*e),n%6){case 0:s=i,a=p,r=h;break;case 1:s=l,a=i,r=h;break;case 2:s=h,a=i,r=p;break;case 3:s=h,a=l,r=i;break;case 4:s=p,a=h,r=i;break;case 5:s=i,a=h,r=l}return[s,a,r]}function V(t,e,i){var s,a=Math.max(t,e,i),r=Math.min(t,e,i),n=a-r,o=0===a?0:n/a,h=a/255;switch(a){case r:s=0;break;case t:s=e-i+n*(e<i?6:0),s/=6*n;break;case e:s=i-t+2*n,s/=6*n;break;case i:s=t-e+4*n,s/=6*n}return[s,o,h]}function R(t,e){var i=V(255*t[0],255*t[1],255*t[2]);return i[1]+=e,i[1]>1?i[1]=1:i[1]<=0&&(i[1]=0),L(i[0],i[1],i[2])}function z(t,e){var i=V(255*t[0],255*t[1],255*t[2]);return i[2]+=e,i[2]>1?i[2]=1:i[2]<0&&(i[2]=0),L(i[0],i[1],i[2])}function O(t,e){var i=V(255*t[0],255*t[1],255*t[2]);return i[0]+=e/360,i[0]>1?i[0]-=1:i[0]<0&&(i[0]+=1),L(i[0],i[1],i[2])}!function(){var t,e,i=[];for(t=0;t<256;t+=1)e=t.toString(16),i[t]=1===e.length?"0"+e:e}();var N=function(){return d},B=function(){return m},q=function(t){k=t},j=function(){return k};function W(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function X(t){return X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},X(t)}var H=function(){var t,i,s=1,a=[],r={onmessage:function(){},postMessage:function(e){t({data:e})}},n={postMessage:function(t){r.onmessage({data:t})}};function o(i){if(window.Worker&&window.Blob&&e){var s=new Blob(["var _workerSelf = self; self.onmessage = ",i.toString()],{type:"text/javascript"}),a=URL.createObjectURL(s);return new Worker(a)}return t=i,r}function h(){i||(i=o((function(t){if(n.dataManager||(n.dataManager=function(){function t(a,r){var n,o,h,l,p,d,m=a.length;for(o=0;o<m;o+=1)if("ks"in(n=a[o])&&!n.completed){if(n.completed=!0,n.hasMask){var c=n.masksProperties;for(l=c.length,h=0;h<l;h+=1)if(c[h].pt.k.i)s(c[h].pt.k);else for(d=c[h].pt.k.length,p=0;p<d;p+=1)c[h].pt.k[p].s&&s(c[h].pt.k[p].s[0]),c[h].pt.k[p].e&&s(c[h].pt.k[p].e[0])}0===n.ty?(n.layers=e(n.refId,r),t(n.layers,r)):4===n.ty?i(n.shapes):5===n.ty&&f(n)}}function e(t,e){var i=function(t,e){for(var i=0,s=e.length;i<s;){if(e[i].id===t)return e[i];i+=1}return null}(t,e);return i?i.layers.__used?JSON.parse(JSON.stringify(i.layers)):(i.layers.__used=!0,i.layers):null}function i(t){var e,a,r;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)s(t[e].ks.k);else for(r=t[e].ks.k.length,a=0;a<r;a+=1)t[e].ks.k[a].s&&s(t[e].ks.k[a].s[0]),t[e].ks.k[a].e&&s(t[e].ks.k[a].e[0]);else"gr"===t[e].ty&&i(t[e].it)}function s(t){var e,i=t.i.length;for(e=0;e<i;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function a(t,e){var i=e?e.split("."):[100,100,100];return t[0]>i[0]||!(i[0]>t[0])&&(t[1]>i[1]||!(i[1]>t[1])&&(t[2]>i[2]||!(i[2]>t[2])&&null))}var r,n=function(){var t=[4,4,14];function e(t){var e,i,s,a=t.length;for(e=0;e<a;e+=1)5===t[e].ty&&(s=void 0,s=(i=t[e]).t.d,i.t.d={k:[{s:s,t:0}]})}return function(i){if(a(t,i.v)&&(e(i.layers),i.assets)){var s,r=i.assets.length;for(s=0;s<r;s+=1)i.assets[s].layers&&e(i.assets[s].layers)}}}(),o=(r=[4,7,99],function(t){if(t.chars&&!a(r,t.v)){var e,s=t.chars.length;for(e=0;e<s;e+=1){var n=t.chars[e];n.data&&n.data.shapes&&(i(n.data.shapes),n.data.ip=0,n.data.op=99999,n.data.st=0,n.data.sr=1,n.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(n.data.shapes.push({ty:"no"}),n.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),h=function(){var t=[5,7,15];function e(t){var e,i,s=t.length;for(e=0;e<s;e+=1)5===t[e].ty&&(i=void 0,"number"==typeof(i=t[e].t.p).a&&(i.a={a:0,k:i.a}),"number"==typeof i.p&&(i.p={a:0,k:i.p}),"number"==typeof i.r&&(i.r={a:0,k:i.r}))}return function(i){if(a(t,i.v)&&(e(i.layers),i.assets)){var s,r=i.assets.length;for(s=0;s<r;s+=1)i.assets[s].layers&&e(i.assets[s].layers)}}}(),l=function(){var t=[4,1,9];function e(t){var i,s,a,r=t.length;for(i=0;i<r;i+=1)if("gr"===t[i].ty)e(t[i].it);else if("fl"===t[i].ty||"st"===t[i].ty)if(t[i].c.k&&t[i].c.k[0].i)for(a=t[i].c.k.length,s=0;s<a;s+=1)t[i].c.k[s].s&&(t[i].c.k[s].s[0]/=255,t[i].c.k[s].s[1]/=255,t[i].c.k[s].s[2]/=255,t[i].c.k[s].s[3]/=255),t[i].c.k[s].e&&(t[i].c.k[s].e[0]/=255,t[i].c.k[s].e[1]/=255,t[i].c.k[s].e[2]/=255,t[i].c.k[s].e[3]/=255);else t[i].c.k[0]/=255,t[i].c.k[1]/=255,t[i].c.k[2]/=255,t[i].c.k[3]/=255}function i(t){var i,s=t.length;for(i=0;i<s;i+=1)4===t[i].ty&&e(t[i].shapes)}return function(e){if(a(t,e.v)&&(i(e.layers),e.assets)){var s,r=e.assets.length;for(s=0;s<r;s+=1)e.assets[s].layers&&i(e.assets[s].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var i,s,a;for(i=t.length-1;i>=0;i-=1)if("sh"===t[i].ty)if(t[i].ks.k.i)t[i].ks.k.c=t[i].closed;else for(a=t[i].ks.k.length,s=0;s<a;s+=1)t[i].ks.k[s].s&&(t[i].ks.k[s].s[0].c=t[i].closed),t[i].ks.k[s].e&&(t[i].ks.k[s].e[0].c=t[i].closed);else"gr"===t[i].ty&&e(t[i].it)}function i(t){var i,s,a,r,n,o,h=t.length;for(s=0;s<h;s+=1){if((i=t[s]).hasMask){var l=i.masksProperties;for(r=l.length,a=0;a<r;a+=1)if(l[a].pt.k.i)l[a].pt.k.c=l[a].cl;else for(o=l[a].pt.k.length,n=0;n<o;n+=1)l[a].pt.k[n].s&&(l[a].pt.k[n].s[0].c=l[a].cl),l[a].pt.k[n].e&&(l[a].pt.k[n].e[0].c=l[a].cl)}4===i.ty&&e(i.shapes)}}return function(e){if(a(t,e.v)&&(i(e.layers),e.assets)){var s,r=e.assets.length;for(s=0;s<r;s+=1)e.assets[s].layers&&i(e.assets[s].layers)}}}();function f(t){0===t.t.a.length&&t.t.p}var d={completeData:function(i){i.__complete||(l(i),n(i),o(i),h(i),p(i),t(i.layers,i.assets),function(i,s){if(i){var a=0,r=i.length;for(a=0;a<r;a+=1)1===i[a].t&&(i[a].data.layers=e(i[a].data.refId,s),t(i[a].data.layers,s))}}(i.chars,i.assets),i.__complete=!0)}};return d.checkColors=l,d.checkChars=o,d.checkPathProperties=h,d.checkShapes=p,d.completeLayers=t,d}()),n.assetLoader||(n.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===X(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,i,s,a){var r,n=new XMLHttpRequest;try{n.responseType="json"}catch(t){}n.onreadystatechange=function(){if(4===n.readyState)if(200===n.status)r=t(n),s(r);else try{r=t(n),s(r)}catch(t){a&&a(t)}};try{n.open(["G","E","T"].join(""),e,!0)}catch(t){n.open(["G","E","T"].join(""),i+"/"+e,!0)}n.send()}}}()),"loadAnimation"===t.data.type)n.assetLoader.load(t.data.path,t.data.fullPath,(function(e){n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){n.postMessage({id:t.data.id,status:"error"})}));else if("complete"===t.data.type){var e=t.data.animation;n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&n.assetLoader.load(t.data.path,t.data.fullPath,(function(e){n.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){n.postMessage({id:t.data.id,status:"error"})}))})),i.onmessage=function(t){var e=t.data,i=e.id,s=a[i];a[i]=null,"success"===e.status?s.onComplete(e.payload):s.onError&&s.onError()})}function l(t,e){var i="processId_"+(s+=1);return a[i]={onComplete:t,onError:e},i}return{loadAnimation:function(t,e,s){h();var a=l(e,s);i.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:a})},loadData:function(t,e,s){h();var a=l(e,s);i.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:a})},completeAnimation:function(t,e,s){h();var a=l(e,s);i.postMessage({type:"complete",animation:t,id:a})}}}(),Y=function(){var t=function(){var t=a("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function s(t,e,i){var s="";if(t.e)s=t.p;else if(e){var a=t.p;-1!==a.indexOf("images/")&&(a=a.split("/")[1]),s=e+a}else s=i,s+=t.u?t.u:"",s+=t.p;return s}function r(t){var e=0,i=setInterval(function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(i)),e+=1}.bind(this),50)}function n(t){var e={assetData:t},i=s(t,this.assetsPath,this.path);return H.loadData(i,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}function o(){this._imageLoaded=e.bind(this),this._footageLoaded=i.bind(this),this.testImageLoaded=r.bind(this),this.createFootageData=n.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return o.prototype={loadAssets:function(t,e){var i;this.imagesLoadedCb=e;var s=t.length;for(i=0;i<s;i+=1)t[i].layers||(t[i].t&&"seq"!==t[i].t?3===t[i].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[i]))):(this.totalImages+=1,this.images.push(this._createImageData(t[i]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,i=this.images.length;e<i;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var i=s(e,this.assetsPath,this.path),r=a("img");r.crossOrigin="anonymous",r.addEventListener("load",this._imageLoaded,!1),r.addEventListener("error",function(){n.img=t,this._imageLoaded()}.bind(this),!1),r.src=i;var n={img:r,assetData:e};return n},createImageData:function(e){var i=s(e,this.assetsPath,this.path),a=W("image");u?this.testImageLoaded(a):a.addEventListener("load",this._imageLoaded,!1),a.addEventListener("error",function(){r.img=t,this._imageLoaded()}.bind(this),!1),a.setAttributeNS("http://www.w3.org/1999/xlink","href",i),this._elementHelper.append?this._elementHelper.append(a):this._elementHelper.appendChild(a);var r={img:a,assetData:e};return r},imageLoaded:e,footageLoaded:i,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},o}();function G(){}G.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var i=this._cbs[t],s=0;s<i.length;s+=1)i[s](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var i=0,s=this._cbs[t].length;i<s;)this._cbs[t][i]===e&&(this._cbs[t].splice(i,1),i-=1,s-=1),i+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var K=function(){function t(t){for(var e,i=t.split("\r\n"),s={},a=0,r=0;r<i.length;r+=1)2===(e=i[r].split(":")).length&&(s[e[0]]=e[1].trim(),a+=1);if(0===a)throw new Error;return s}return function(e){for(var i=[],s=0;s<e.length;s+=1){var a=e[s],r={time:a.tm,duration:a.dr};try{r.payload=JSON.parse(e[s].cm)}catch(i){try{r.payload=t(e[s].cm)}catch(t){r.payload={name:e[s].cm}}}i.push(r)}return i}}(),J=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,i=this.compositions.length;e<i;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),U={};function Z(t){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Z(t)}var Q=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=I(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=f,this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=J(),this.imagePreloader=new Y,this.audioController=o(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new x("drawnFrame",0,0,0),this.expressionsPlugin=N()};r([G],Q),Q.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var i=U[e];this.renderer=new i(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),H.loadAnimation(t.path,this.configAnimation,this.onSetupError))},Q.prototype.onSetupError=function(){this.trigger("data_failed")},Q.prototype.setupAnimation=function(t){H.completeAnimation(t,this.configAnimation)},Q.prototype.setData=function(t,e){e&&"object"!==Z(e)&&(e=JSON.parse(e));var i={wrapper:t,animationData:e},s=t.attributes;i.path=s.getNamedItem("data-animation-path")?s.getNamedItem("data-animation-path").value:s.getNamedItem("data-bm-path")?s.getNamedItem("data-bm-path").value:s.getNamedItem("bm-path")?s.getNamedItem("bm-path").value:"",i.animType=s.getNamedItem("data-anim-type")?s.getNamedItem("data-anim-type").value:s.getNamedItem("data-bm-type")?s.getNamedItem("data-bm-type").value:s.getNamedItem("bm-type")?s.getNamedItem("bm-type").value:s.getNamedItem("data-bm-renderer")?s.getNamedItem("data-bm-renderer").value:s.getNamedItem("bm-renderer")?s.getNamedItem("bm-renderer").value:function(){if(U.canvas)return"canvas";for(var t in U)if(U[t])return t;return""}()||"canvas";var a=s.getNamedItem("data-anim-loop")?s.getNamedItem("data-anim-loop").value:s.getNamedItem("data-bm-loop")?s.getNamedItem("data-bm-loop").value:s.getNamedItem("bm-loop")?s.getNamedItem("bm-loop").value:"";"false"===a?i.loop=!1:"true"===a?i.loop=!0:""!==a&&(i.loop=parseInt(a,10));var r=s.getNamedItem("data-anim-autoplay")?s.getNamedItem("data-anim-autoplay").value:s.getNamedItem("data-bm-autoplay")?s.getNamedItem("data-bm-autoplay").value:!s.getNamedItem("bm-autoplay")||s.getNamedItem("bm-autoplay").value;i.autoplay="false"!==r,i.name=s.getNamedItem("data-name")?s.getNamedItem("data-name").value:s.getNamedItem("data-bm-name")?s.getNamedItem("data-bm-name").value:s.getNamedItem("bm-name")?s.getNamedItem("bm-name").value:"","false"===(s.getNamedItem("data-anim-prerender")?s.getNamedItem("data-anim-prerender").value:s.getNamedItem("data-bm-prerender")?s.getNamedItem("data-bm-prerender").value:s.getNamedItem("bm-prerender")?s.getNamedItem("bm-prerender").value:"")&&(i.prerender=!1),i.path?this.setParams(i):this.trigger("destroy")},Q.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,i,s=this.animationData.layers,a=s.length,r=t.layers,n=r.length;for(i=0;i<n;i+=1)for(e=0;e<a;){if(s[e].id===r[i].id){s[e]=r[i];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(a=t.assets.length,e=0;e<a;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,H.completeAnimation(this.animationData,this.onSegmentComplete)},Q.prototype.onSegmentComplete=function(t){this.animationData=t;var e=N();e&&e.initExpressions(this),this.loadNextSegment()},Q.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var i=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,H.loadData(i,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},Q.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},Q.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},Q.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},Q.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=K(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},Q.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},Q.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=N();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},Q.prototype.resize=function(t,e){var i="number"==typeof t?t:void 0,s="number"==typeof e?e:void 0;this.renderer.updateContainerSize(i,s)},Q.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},Q.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},Q.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},Q.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},Q.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},Q.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},Q.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},Q.prototype.getMarkerData=function(t){for(var e,i=0;i<this.markers.length;i+=1)if((e=this.markers[i]).payload&&e.payload.name===t)return e;return null},Q.prototype.goToAndStop=function(t,e,i){if(!i||this.name===i){var s=Number(t);if(isNaN(s)){var a=this.getMarkerData(t);a&&this.goToAndStop(a.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},Q.prototype.goToAndPlay=function(t,e,i){if(!i||this.name===i){var s=Number(t);if(isNaN(s)){var a=this.getMarkerData(t);a&&(a.duration?this.playSegments([a.time,a.time+a.duration],!0):this.goToAndStop(a.time,!0))}else this.goToAndStop(s,e,i);this.play()}},Q.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,i=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(i=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(i=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),i&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},Q.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},Q.prototype.setSegment=function(t,e){var i=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?i=t:this.currentRawFrame+this.firstFrame>e&&(i=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==i&&this.goToAndStop(i,!0)},Q.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===Z(t[0])){var i,s=t.length;for(i=0;i<s;i+=1)this.segments.push(t[i])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},Q.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},Q.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},Q.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},Q.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},Q.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},Q.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},Q.prototype.setLoop=function(t){this.loop=t},Q.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},Q.prototype.getVolume=function(){return this.audioController.getVolume()},Q.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},Q.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},Q.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},Q.prototype.getPath=function(){return this.path},Q.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var i=t.p;-1!==i.indexOf("images/")&&(i=i.split("/")[1]),e=this.assetsPath+i}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},Q.prototype.getAssetData=function(t){for(var e=0,i=this.assets.length;e<i;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},Q.prototype.hide=function(){this.renderer.hide()},Q.prototype.show=function(){this.renderer.show()},Q.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},Q.prototype.updateDocumentData=function(t,e,i){try{this.renderer.getElementByPath(t).updateDocumentData(e,i)}catch(t){}},Q.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new x(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new D(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new w(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new C(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new M(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new x(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new D(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new w(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new C(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new M(t,this))},Q.prototype.triggerRenderFrameError=function(t){var e=new T(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},Q.prototype.triggerConfigError=function(t){var e=new F(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var $=function(){var t={},e=[],i=0,s=0,r=0,n=!0,o=!1;function h(t){for(var i=0,a=t.target;i<s;)e[i].animation===a&&(e.splice(i,1),i-=1,s-=1,a.isPaused||f()),i+=1}function l(t,i){if(!t)return null;for(var a=0;a<s;){if(e[a].elem===t&&null!==e[a].elem)return e[a].animation;a+=1}var r=new Q;return d(r,t),r.setData(t,i),r}function p(){r+=1,u()}function f(){r-=1}function d(t,i){t.addEventListener("destroy",h),t.addEventListener("_active",p),t.addEventListener("_idle",f),e.push({elem:i,animation:t}),s+=1}function m(t){var a,h=t-i;for(a=0;a<s;a+=1)e[a].animation.advanceTime(h);i=t,r&&!o?window.requestAnimationFrame(m):n=!0}function c(t){i=t,window.requestAnimationFrame(m)}function u(){!o&&r&&n&&(window.requestAnimationFrame(c),n=!1)}return t.registerAnimation=l,t.loadAnimation=function(t){var e=new Q;return d(e,null),e.setParams(t),e},t.setSpeed=function(t,i){var a;for(a=0;a<s;a+=1)e[a].animation.setSpeed(t,i)},t.setDirection=function(t,i){var a;for(a=0;a<s;a+=1)e[a].animation.setDirection(t,i)},t.play=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.play(t)},t.pause=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.pause(t)},t.stop=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.stop(t)},t.togglePause=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.togglePause(t)},t.searchAnimations=function(t,e,i){var s,r=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),n=r.length;for(s=0;s<n;s+=1)i&&r[s].setAttribute("data-bm-type",i),l(r[s],t);if(e&&0===n){i||(i="svg");var o=document.getElementsByTagName("body")[0];o.innerText="";var h=a("div");h.style.width="100%",h.style.height="100%",h.setAttribute("data-bm-type",i),o.appendChild(h),l(h,t)}},t.resize=function(){var t;for(t=0;t<s;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,i,a){var r;for(r=0;r<s;r+=1)e[r].animation.goToAndStop(t,i,a)},t.destroy=function(t){var i;for(i=s-1;i>=0;i-=1)e[i].animation.destroy(t)},t.freeze=function(){o=!0},t.unfreeze=function(){o=!1,u()},t.setVolume=function(t,i){var a;for(a=0;a<s;a+=1)e[a].animation.setVolume(t,i)},t.mute=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.mute(t)},t.unmute=function(t){var i;for(i=0;i<s;i+=1)e[i].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,i=e.length,s=[];for(t=0;t<i;t+=1)s.push(e[t].animation);return s},t}(),tt=function(){var t={getBezierEasing:function(t,i,s,a,r){var n=r||("bez_"+t+"_"+i+"_"+s+"_"+a).replace(/\./g,"p");if(e[n])return e[n];var o=new l([t,i,s,a]);return e[n]=o,o}},e={};var i=.1,s="function"==typeof Float32Array;function a(t,e){return 1-3*e+3*t}function r(t,e){return 3*e-6*t}function n(t){return 3*t}function o(t,e,i){return((a(e,i)*t+r(e,i))*t+n(e))*t}function h(t,e,i){return 3*a(e,i)*t*t+2*r(e,i)*t+n(e)}function l(t){this._p=t,this._mSampleValues=s?new Float32Array(11):new Array(11),this._precomputed=!1,this.get=this.get.bind(this)}return l.prototype={get:function(t){var e=this._p[0],i=this._p[1],s=this._p[2],a=this._p[3];return this._precomputed||this._precompute(),e===i&&s===a?t:0===t?0:1===t?1:o(this._getTForX(t),i,a)},_precompute:function(){var t=this._p[0],e=this._p[1],i=this._p[2],s=this._p[3];this._precomputed=!0,t===e&&i===s||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],s=0;s<11;++s)this._mSampleValues[s]=o(s*i,t,e)},_getTForX:function(t){for(var e=this._p[0],s=this._p[2],a=this._mSampleValues,r=0,n=1;10!==n&&a[n]<=t;++n)r+=i;var l=r+(t-a[--n])/(a[n+1]-a[n])*i,p=h(l,e,s);return p>=.001?function(t,e,i,s){for(var a=0;a<4;++a){var r=h(e,i,s);if(0===r)return e;e-=(o(e,i,s)-t)/r}return e}(t,l,e,s):0===p?l:function(t,e,i,s,a){var r,n,h=0;do{(r=o(n=e+(i-e)/2,s,a)-t)>0?i=n:e=n}while(Math.abs(r)>1e-7&&++h<10);return n}(t,r,r+i,e,s)}},t}(),et={double:function(t){return t.concat(l(t.length))}},it=function(t,e,i){var s=0,a=t,r=l(a);return{newElement:function(){return s?r[s-=1]:e()},release:function(t){s===a&&(r=et.double(r),a*=2),i&&i(t),r[s]=t,s+=1}}},st=it(8,(function(){return{addedLength:0,percents:h("float32",j()),lengths:h("float32",j())}})),at=it(8,(function(){return{lengths:[],totalLength:0}}),(function(t){var e,i=t.lengths.length;for(e=0;e<i;e+=1)st.release(t.lengths[e]);t.lengths.length=0}));var rt=function(){var t=Math;function e(t,e,i,s,a,r){var n=t*s+e*a+i*r-a*s-r*t-i*e;return n>-.001&&n<.001}var i=function(t,e,i,s){var a,r,n,o,h,l,p=j(),f=0,d=[],m=[],c=st.newElement();for(n=i.length,a=0;a<p;a+=1){for(h=a/(p-1),l=0,r=0;r<n;r+=1)o=g(1-h,3)*t[r]+3*g(1-h,2)*h*i[r]+3*(1-h)*g(h,2)*s[r]+g(h,3)*e[r],d[r]=o,null!==m[r]&&(l+=g(d[r]-m[r],2)),m[r]=d[r];l&&(f+=l=y(l)),c.percents[a]=h,c.lengths[a]=f}return c.addedLength=f,c};function s(t){this.segmentLength=0,this.points=new Array(t)}function a(t,e){this.partialLength=t,this.point=e}var r,n=(r={},function(t,i,n,o){var h=(t[0]+"_"+t[1]+"_"+i[0]+"_"+i[1]+"_"+n[0]+"_"+n[1]+"_"+o[0]+"_"+o[1]).replace(/\./g,"p");if(!r[h]){var p,f,d,m,c,u,v,b=j(),_=0,k=null;2===t.length&&(t[0]!==i[0]||t[1]!==i[1])&&e(t[0],t[1],i[0],i[1],t[0]+n[0],t[1]+n[1])&&e(t[0],t[1],i[0],i[1],i[0]+o[0],i[1]+o[1])&&(b=2);var P=new s(b);for(d=n.length,p=0;p<b;p+=1){for(v=l(d),c=p/(b-1),u=0,f=0;f<d;f+=1)m=g(1-c,3)*t[f]+3*g(1-c,2)*c*(t[f]+n[f])+3*(1-c)*g(c,2)*(i[f]+o[f])+g(c,3)*i[f],v[f]=m,null!==k&&(u+=g(v[f]-k[f],2));_+=u=y(u),P.points[p]=new a(u,v),k=v}P.segmentLength=_,r[h]=P}return r[h]});function o(t,e){var i=e.percents,s=e.lengths,a=i.length,r=v((a-1)*t),n=t*e.addedLength,o=0;if(r===a-1||0===r||n===s[r])return i[r];for(var h=s[r]>n?-1:1,l=!0;l;)if(s[r]<=n&&s[r+1]>n?(o=(n-s[r])/(s[r+1]-s[r]),l=!1):r+=h,r<0||r>=a-1){if(r===a-1)return i[r];l=!1}return i[r]+(i[r+1]-i[r])*o}var p=h("float32",8);return{getSegmentsLength:function(t){var e,s=at.newElement(),a=t.c,r=t.v,n=t.o,o=t.i,h=t._length,l=s.lengths,p=0;for(e=0;e<h-1;e+=1)l[e]=i(r[e],r[e+1],n[e],o[e+1]),p+=l[e].addedLength;return a&&h&&(l[e]=i(r[e],r[0],n[e],o[0]),p+=l[e].addedLength),s.totalLength=p,s},getNewSegment:function(e,i,s,a,r,n,h){r<0?r=0:r>1&&(r=1);var l,f=o(r,h),d=o(n=n>1?1:n,h),m=e.length,c=1-f,u=1-d,g=c*c*c,y=f*c*c*3,v=f*f*c*3,b=f*f*f,_=c*c*u,k=f*c*u+c*f*u+c*c*d,P=f*f*u+c*f*d+f*c*d,A=f*f*d,S=c*u*u,x=f*u*u+c*d*u+c*u*d,w=f*d*u+c*d*d+f*u*d,D=f*d*d,C=u*u*u,M=d*u*u+u*d*u+u*u*d,T=d*d*u+u*d*d+d*u*d,F=d*d*d;for(l=0;l<m;l+=1)p[4*l]=t.round(1e3*(g*e[l]+y*s[l]+v*a[l]+b*i[l]))/1e3,p[4*l+1]=t.round(1e3*(_*e[l]+k*s[l]+P*a[l]+A*i[l]))/1e3,p[4*l+2]=t.round(1e3*(S*e[l]+x*s[l]+w*a[l]+D*i[l]))/1e3,p[4*l+3]=t.round(1e3*(C*e[l]+M*s[l]+T*a[l]+F*i[l]))/1e3;return p},getPointInSegment:function(e,i,s,a,r,n){var h=o(r,n),l=1-h;return[t.round(1e3*(l*l*l*e[0]+(h*l*l+l*h*l+l*l*h)*s[0]+(h*h*l+l*h*h+h*l*h)*a[0]+h*h*h*i[0]))/1e3,t.round(1e3*(l*l*l*e[1]+(h*l*l+l*h*l+l*l*h)*s[1]+(h*h*l+l*h*h+h*l*h)*a[1]+h*h*h*i[1]))/1e3]},buildBezierData:n,pointOnLine2D:e,pointOnLine3D:function(i,s,a,r,n,o,h,l,p){if(0===a&&0===o&&0===p)return e(i,s,r,n,h,l);var f,d=t.sqrt(t.pow(r-i,2)+t.pow(n-s,2)+t.pow(o-a,2)),m=t.sqrt(t.pow(h-i,2)+t.pow(l-s,2)+t.pow(p-a,2)),c=t.sqrt(t.pow(h-r,2)+t.pow(l-n,2)+t.pow(p-o,2));return(f=d>m?d>c?d-m-c:c-m-d:c>m?c-m-d:m-d-c)>-1e-4&&f<1e-4}}}(),nt=i,ot=Math.abs;function ht(t,e){var i,s=this.offsetTime;"multidimensional"===this.propType&&(i=h("float32",this.pv.length));for(var a,r,n,o,l,p,f,d,m,c=e.lastIndex,u=c,g=this.keyframes.length-1,y=!0;y;){if(a=this.keyframes[u],r=this.keyframes[u+1],u===g-1&&t>=r.t-s){a.h&&(a=r),c=0;break}if(r.t-s>t){c=u;break}u<g-1?u+=1:(c=0,y=!1)}n=this.keyframesMetadata[u]||{};var v,b,_,k,A,S,x,w,D,C,M=r.t-s,T=a.t-s;if(a.to){n.bezierData||(n.bezierData=rt.buildBezierData(a.s,r.s||a.e,a.to,a.ti));var F=n.bezierData;if(t>=M||t<T){var E=t>=M?F.points.length-1:0;for(l=F.points[E].point.length,o=0;o<l;o+=1)i[o]=F.points[E].point[o]}else{n.__fnct?m=n.__fnct:(m=tt.getBezierEasing(a.o.x,a.o.y,a.i.x,a.i.y,a.n).get,n.__fnct=m),p=m((t-T)/(M-T));var I,L=F.segmentLength*p,V=e.lastFrame<t&&e._lastKeyframeIndex===u?e._lastAddedLength:0;for(d=e.lastFrame<t&&e._lastKeyframeIndex===u?e._lastPoint:0,y=!0,f=F.points.length;y;){if(V+=F.points[d].partialLength,0===L||0===p||d===F.points.length-1){for(l=F.points[d].point.length,o=0;o<l;o+=1)i[o]=F.points[d].point[o];break}if(L>=V&&L<V+F.points[d+1].partialLength){for(I=(L-V)/F.points[d+1].partialLength,l=F.points[d].point.length,o=0;o<l;o+=1)i[o]=F.points[d].point[o]+(F.points[d+1].point[o]-F.points[d].point[o])*I;break}d<f-1?d+=1:y=!1}e._lastPoint=d,e._lastAddedLength=V-F.points[d].partialLength,e._lastKeyframeIndex=u}}else{var R,z,O,N,B;if(g=a.s.length,v=r.s||a.e,this.sh&&1!==a.h)if(t>=M)i[0]=v[0],i[1]=v[1],i[2]=v[2];else if(t<=T)i[0]=a.s[0],i[1]=a.s[1],i[2]=a.s[2];else{var q=lt(a.s),j=lt(v);b=i,_=function(t,e,i){var s,a,r,n,o,h=[],l=t[0],p=t[1],f=t[2],d=t[3],m=e[0],c=e[1],u=e[2],g=e[3];return(a=l*m+p*c+f*u+d*g)<0&&(a=-a,m=-m,c=-c,u=-u,g=-g),1-a>1e-6?(s=Math.acos(a),r=Math.sin(s),n=Math.sin((1-i)*s)/r,o=Math.sin(i*s)/r):(n=1-i,o=i),h[0]=n*l+o*m,h[1]=n*p+o*c,h[2]=n*f+o*u,h[3]=n*d+o*g,h}(q,j,(t-T)/(M-T)),k=_[0],A=_[1],S=_[2],x=_[3],w=Math.atan2(2*A*x-2*k*S,1-2*A*A-2*S*S),D=Math.asin(2*k*A+2*S*x),C=Math.atan2(2*k*x-2*A*S,1-2*k*k-2*S*S),b[0]=w/P,b[1]=D/P,b[2]=C/P}else for(u=0;u<g;u+=1)1!==a.h&&(t>=M?p=1:t<T?p=0:(a.o.x.constructor===Array?(n.__fnct||(n.__fnct=[]),n.__fnct[u]?m=n.__fnct[u]:(R=void 0===a.o.x[u]?a.o.x[0]:a.o.x[u],z=void 0===a.o.y[u]?a.o.y[0]:a.o.y[u],O=void 0===a.i.x[u]?a.i.x[0]:a.i.x[u],N=void 0===a.i.y[u]?a.i.y[0]:a.i.y[u],m=tt.getBezierEasing(R,z,O,N).get,n.__fnct[u]=m)):n.__fnct?m=n.__fnct:(R=a.o.x,z=a.o.y,O=a.i.x,N=a.i.y,m=tt.getBezierEasing(R,z,O,N).get,a.keyframeMetadata=m),p=m((t-T)/(M-T)))),v=r.s||a.e,B=1===a.h?a.s[u]:a.s[u]+(v[u]-a.s[u])*p,"multidimensional"===this.propType?i[u]=B:i=B}return e.lastIndex=c,i}function lt(t){var e=t[0]*P,i=t[1]*P,s=t[2]*P,a=Math.cos(e/2),r=Math.cos(i/2),n=Math.cos(s/2),o=Math.sin(e/2),h=Math.sin(i/2),l=Math.sin(s/2);return[o*h*n+a*r*l,o*r*n+a*h*l,a*h*n-o*r*l,a*r*n-o*h*l]}function pt(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(t===this._caching.lastFrame||this._caching.lastFrame!==nt&&(this._caching.lastFrame>=i&&t>=i||this._caching.lastFrame<e&&t<e))){this._caching.lastFrame>=t&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var s=this.interpolateValue(t,this._caching);this.pv=s}return this._caching.lastFrame=t,this.pv}function ft(t){var e;if("unidimensional"===this.propType)e=t*this.mult,ot(this.v-e)>1e-5&&(this.v=e,this._mdf=!0);else for(var i=0,s=this.v.length;i<s;)e=t[i]*this.mult,ot(this.v[i]-e)>1e-5&&(this.v[i]=e,this._mdf=!0),i+=1}function dt(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t;this.lock=!0,this._mdf=this._isFirstFrame;var e=this.effectsSequence.length,i=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)i=this.effectsSequence[t](i);this.setVValue(i),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function mt(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function ct(t,e,i,s){this.propType="unidimensional",this.mult=i||1,this.data=e,this.v=i?e.k*i:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=s,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=dt,this.setVValue=ft,this.addEffect=mt}function ut(t,e,i,s){var a;this.propType="multidimensional",this.mult=i||1,this.data=e,this._mdf=!1,this.elem=t,this.container=s,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var r=e.k.length;for(this.v=h("float32",r),this.pv=h("float32",r),this.vel=h("float32",r),a=0;a<r;a+=1)this.v[a]=e.k[a]*this.mult,this.pv[a]=e.k[a];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=dt,this.setVValue=ft,this.addEffect=mt}function gt(t,e,i,s){this.propType="unidimensional",this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.frameId=-1,this._caching={lastFrame:nt,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=e,this.mult=i||1,this.elem=t,this.container=s,this.comp=t.comp,this.v=nt,this.pv=nt,this._isFirstFrame=!0,this.getValue=dt,this.setVValue=ft,this.interpolateValue=ht,this.effectsSequence=[pt.bind(this)],this.addEffect=mt}function yt(t,e,i,s){var a;this.propType="multidimensional";var r,n,o,l,p=e.k.length;for(a=0;a<p-1;a+=1)e.k[a].to&&e.k[a].s&&e.k[a+1]&&e.k[a+1].s&&(r=e.k[a].s,n=e.k[a+1].s,o=e.k[a].to,l=e.k[a].ti,(2===r.length&&(r[0]!==n[0]||r[1]!==n[1])&&rt.pointOnLine2D(r[0],r[1],n[0],n[1],r[0]+o[0],r[1]+o[1])&&rt.pointOnLine2D(r[0],r[1],n[0],n[1],n[0]+l[0],n[1]+l[1])||3===r.length&&(r[0]!==n[0]||r[1]!==n[1]||r[2]!==n[2])&&rt.pointOnLine3D(r[0],r[1],r[2],n[0],n[1],n[2],r[0]+o[0],r[1]+o[1],r[2]+o[2])&&rt.pointOnLine3D(r[0],r[1],r[2],n[0],n[1],n[2],n[0]+l[0],n[1]+l[1],n[2]+l[2]))&&(e.k[a].to=null,e.k[a].ti=null),r[0]===n[0]&&r[1]===n[1]&&0===o[0]&&0===o[1]&&0===l[0]&&0===l[1]&&(2===r.length||r[2]===n[2]&&0===o[2]&&0===l[2])&&(e.k[a].to=null,e.k[a].ti=null));this.effectsSequence=[pt.bind(this)],this.data=e,this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=i||1,this.elem=t,this.container=s,this.comp=t.comp,this.getValue=dt,this.setVValue=ft,this.interpolateValue=ht,this.frameId=-1;var f=e.k[0].s.length;for(this.v=h("float32",f),this.pv=h("float32",f),a=0;a<f;a+=1)this.v[a]=nt,this.pv[a]=nt;this._caching={lastFrame:nt,lastIndex:0,value:h("float32",f)},this.addEffect=mt}var vt={getProp:function(t,e,i,s,a){var r;if(e.sid&&(e=t.globalData.slotManager.getProp(e)),e.k.length)if("number"==typeof e.k[0])r=new ut(t,e,s,a);else switch(i){case 0:r=new gt(t,e,s,a);break;case 1:r=new yt(t,e,s,a)}else r=new ct(t,e,s,a);return r.effectsSequence.length&&a.addDynamicProperty(r),r}};function bt(){}bt.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){var t;this._mdf=!1;var e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var _t=it(8,(function(){return h("float32",2)}));function kt(){this.c=!1,this._length=0,this._maxLength=8,this.v=l(this._maxLength),this.o=l(this._maxLength),this.i=l(this._maxLength)}kt.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var i=0;i<e;)this.v[i]=_t.newElement(),this.o[i]=_t.newElement(),this.i[i]=_t.newElement(),i+=1},kt.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},kt.prototype.doubleArrayLength=function(){this.v=this.v.concat(l(this._maxLength)),this.i=this.i.concat(l(this._maxLength)),this.o=this.o.concat(l(this._maxLength)),this._maxLength*=2},kt.prototype.setXYAt=function(t,e,i,s,a){var r;switch(this._length=Math.max(this._length,s+1),this._length>=this._maxLength&&this.doubleArrayLength(),i){case"v":r=this.v;break;case"i":r=this.i;break;case"o":r=this.o;break;default:r=[]}(!r[s]||r[s]&&!a)&&(r[s]=_t.newElement()),r[s][0]=t,r[s][1]=e},kt.prototype.setTripleAt=function(t,e,i,s,a,r,n,o){this.setXYAt(t,e,"v",n,o),this.setXYAt(i,s,"o",n,o),this.setXYAt(a,r,"i",n,o)},kt.prototype.reverse=function(){var t=new kt;t.setPathData(this.c,this._length);var e=this.v,i=this.o,s=this.i,a=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],s[0][0],s[0][1],i[0][0],i[0][1],0,!1),a=1);var r,n=this._length-1,o=this._length;for(r=a;r<o;r+=1)t.setTripleAt(e[n][0],e[n][1],s[n][0],s[n][1],i[n][0],i[n][1],r,!1),n-=1;return t},kt.prototype.length=function(){return this._length};var Pt,At=((Pt=it(4,(function(){return new kt}),(function(t){var e,i=t._length;for(e=0;e<i;e+=1)_t.release(t.v[e]),_t.release(t.i[e]),_t.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1}))).clone=function(t){var e,i=Pt.newElement(),s=void 0===t._length?t.v.length:t._length;for(i.setLength(s),i.c=t.c,e=0;e<s;e+=1)i.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return i},Pt);function St(){this._length=0,this._maxLength=4,this.shapes=l(this._maxLength)}St.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(l(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},St.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)At.release(this.shapes[t]);this._length=0};var xt,wt,Dt,Ct,Mt=(xt={newShapeCollection:function(){return wt?Ct[wt-=1]:new St},release:function(t){var e,i=t._length;for(e=0;e<i;e+=1)At.release(t.shapes[e]);t._length=0,wt===Dt&&(Ct=et.double(Ct),Dt*=2),Ct[wt]=t,wt+=1}},wt=0,Ct=l(Dt=4),xt),Tt=function(){var t=-999999;function e(t,e,i){var s,a,r,n,o,h,l,p,f,d=i.lastIndex,m=this.keyframes;if(t<m[0].t-this.offsetTime)s=m[0].s[0],r=!0,d=0;else if(t>=m[m.length-1].t-this.offsetTime)s=m[m.length-1].s?m[m.length-1].s[0]:m[m.length-2].e[0],r=!0;else{for(var c,u,g,y=d,v=m.length-1,b=!0;b&&(c=m[y],!((u=m[y+1]).t-this.offsetTime>t));)y<v-1?y+=1:b=!1;if(g=this.keyframesMetadata[y]||{},d=y,!(r=1===c.h)){if(t>=u.t-this.offsetTime)p=1;else if(t<c.t-this.offsetTime)p=0;else{var _;g.__fnct?_=g.__fnct:(_=tt.getBezierEasing(c.o.x,c.o.y,c.i.x,c.i.y).get,g.__fnct=_),p=_((t-(c.t-this.offsetTime))/(u.t-this.offsetTime-(c.t-this.offsetTime)))}a=u.s?u.s[0]:c.e[0]}s=c.s[0]}for(h=e._length,l=s.i[0].length,i.lastIndex=d,n=0;n<h;n+=1)for(o=0;o<l;o+=1)f=r?s.i[n][o]:s.i[n][o]+(a.i[n][o]-s.i[n][o])*p,e.i[n][o]=f,f=r?s.o[n][o]:s.o[n][o]+(a.o[n][o]-s.o[n][o])*p,e.o[n][o]=f,f=r?s.v[n][o]:s.v[n][o]+(a.v[n][o]-s.v[n][o])*p,e.v[n][o]=f}function i(){var e=this.comp.renderedFrame-this.offsetTime,i=this.keyframes[0].t-this.offsetTime,s=this.keyframes[this.keyframes.length-1].t-this.offsetTime,a=this._caching.lastFrame;return a!==t&&(a<i&&e<i||a>s&&e>s)||(this._caching.lastIndex=a<e?this._caching.lastIndex:0,this.interpolateShape(e,this.pv,this._caching)),this._caching.lastFrame=e,this.pv}function s(){this.paths=this.localShapeCollection}function a(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var i,s=t._length;for(i=0;i<s;i+=1)if(t.v[i][0]!==e.v[i][0]||t.v[i][1]!==e.v[i][1]||t.o[i][0]!==e.o[i][0]||t.o[i][1]!==e.o[i][1]||t.i[i][0]!==e.i[i][0]||t.i[i][1]!==e.i[i][1])return!1;return!0})(this.v,t)||(this.v=At.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function n(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t,e;this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var i=this.effectsSequence.length;for(e=0;e<i;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function o(t,e,i){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var a=3===i?e.pt.k:e.ks.k;this.v=At.clone(a),this.pv=At.clone(this.v),this.localShapeCollection=Mt.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=s,this.effectsSequence=[]}function h(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function l(e,a,r){this.propType="shape",this.comp=e.comp,this.elem=e,this.container=e,this.offsetTime=e.data.st,this.keyframes=3===r?a.pt.k:a.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var n=this.keyframes[0].s[0].i.length;this.v=At.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,n),this.pv=At.clone(this.v),this.localShapeCollection=Mt.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=s,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[i.bind(this)]}o.prototype.interpolateShape=e,o.prototype.getValue=n,o.prototype.setVValue=a,o.prototype.addEffect=h,l.prototype.getValue=n,l.prototype.interpolateShape=e,l.prototype.setVValue=a,l.prototype.addEffect=h;var p=function(){var t=A;function e(t,e){this.v=At.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=Mt.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=vt.getProp(t,e.p,1,0,this),this.s=vt.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:s,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],i=this.p.v[1],s=this.s.v[0]/2,a=this.s.v[1]/2,r=3!==this.d,n=this.v;n.v[0][0]=e,n.v[0][1]=i-a,n.v[1][0]=r?e+s:e-s,n.v[1][1]=i,n.v[2][0]=e,n.v[2][1]=i+a,n.v[3][0]=r?e-s:e+s,n.v[3][1]=i,n.i[0][0]=r?e-s*t:e+s*t,n.i[0][1]=i-a,n.i[1][0]=r?e+s:e-s,n.i[1][1]=i-a*t,n.i[2][0]=r?e+s*t:e-s*t,n.i[2][1]=i+a,n.i[3][0]=r?e-s:e+s,n.i[3][1]=i+a*t,n.o[0][0]=r?e+s*t:e-s*t,n.o[0][1]=i-a,n.o[1][0]=r?e+s:e-s,n.o[1][1]=i+a*t,n.o[2][0]=r?e-s*t:e+s*t,n.o[2][1]=i+a,n.o[3][0]=r?e-s:e+s,n.o[3][1]=i-a*t}},r([bt],e),e}(),f=function(){function t(t,e){this.v=At.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=vt.getProp(t,e.ir,0,0,this),this.is=vt.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=vt.getProp(t,e.pt,0,0,this),this.p=vt.getProp(t,e.p,1,0,this),this.r=vt.getProp(t,e.r,0,P,this),this.or=vt.getProp(t,e.or,0,0,this),this.os=vt.getProp(t,e.os,0,.01,this),this.localShapeCollection=Mt.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:s,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,i,s,a=2*Math.floor(this.pt.v),r=2*Math.PI/a,n=!0,o=this.or.v,h=this.ir.v,l=this.os.v,p=this.is.v,f=2*Math.PI*o/(2*a),d=2*Math.PI*h/(2*a),m=-Math.PI/2;m+=this.r.v;var c=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<a;t+=1){i=n?l:p,s=n?f:d;var u=(e=n?o:h)*Math.cos(m),g=e*Math.sin(m),y=0===u&&0===g?0:g/Math.sqrt(u*u+g*g),v=0===u&&0===g?0:-u/Math.sqrt(u*u+g*g);u+=+this.p.v[0],g+=+this.p.v[1],this.v.setTripleAt(u,g,u-y*s*i*c,g-v*s*i*c,u+y*s*i*c,g+v*s*i*c,t,!0),n=!n,m+=r*c}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),i=2*Math.PI/e,s=this.or.v,a=this.os.v,r=2*Math.PI*s/(4*e),n=.5*-Math.PI,o=3===this.data.d?-1:1;for(n+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var h=s*Math.cos(n),l=s*Math.sin(n),p=0===h&&0===l?0:l/Math.sqrt(h*h+l*l),f=0===h&&0===l?0:-h/Math.sqrt(h*h+l*l);h+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(h,l,h-p*r*a*o,l-f*r*a*o,h+p*r*a*o,l+f*r*a*o,t,!0),n+=i*o}this.paths.length=0,this.paths[0]=this.v}},r([bt],t),t}(),d=function(){function t(t,e){this.v=At.newElement(),this.v.c=!0,this.localShapeCollection=Mt.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=vt.getProp(t,e.p,1,0,this),this.s=vt.getProp(t,e.s,1,0,this),this.r=vt.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],i=this.s.v[0]/2,s=this.s.v[1]/2,a=b(i,s,this.r.v),r=a*(1-A);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+i,e-s+a,t+i,e-s+a,t+i,e-s+r,0,!0),this.v.setTripleAt(t+i,e+s-a,t+i,e+s-r,t+i,e+s-a,1,!0),0!==a?(this.v.setTripleAt(t+i-a,e+s,t+i-a,e+s,t+i-r,e+s,2,!0),this.v.setTripleAt(t-i+a,e+s,t-i+r,e+s,t-i+a,e+s,3,!0),this.v.setTripleAt(t-i,e+s-a,t-i,e+s-a,t-i,e+s-r,4,!0),this.v.setTripleAt(t-i,e-s+a,t-i,e-s+r,t-i,e-s+a,5,!0),this.v.setTripleAt(t-i+a,e-s,t-i+a,e-s,t-i+r,e-s,6,!0),this.v.setTripleAt(t+i-a,e-s,t+i-r,e-s,t+i-a,e-s,7,!0)):(this.v.setTripleAt(t-i,e+s,t-i+r,e+s,t-i,e+s,2),this.v.setTripleAt(t-i,e-s,t-i,e-s+r,t-i,e-s,3))):(this.v.setTripleAt(t+i,e-s+a,t+i,e-s+r,t+i,e-s+a,0,!0),0!==a?(this.v.setTripleAt(t+i-a,e-s,t+i-a,e-s,t+i-r,e-s,1,!0),this.v.setTripleAt(t-i+a,e-s,t-i+r,e-s,t-i+a,e-s,2,!0),this.v.setTripleAt(t-i,e-s+a,t-i,e-s+a,t-i,e-s+r,3,!0),this.v.setTripleAt(t-i,e+s-a,t-i,e+s-r,t-i,e+s-a,4,!0),this.v.setTripleAt(t-i+a,e+s,t-i+a,e+s,t-i+r,e+s,5,!0),this.v.setTripleAt(t+i-a,e+s,t+i-r,e+s,t+i-a,e+s,6,!0),this.v.setTripleAt(t+i,e+s-a,t+i,e+s-a,t+i,e+s-r,7,!0)):(this.v.setTripleAt(t-i,e-s,t-i+r,e-s,t-i,e-s,1,!0),this.v.setTripleAt(t-i,e+s,t-i,e+s-r,t-i,e+s,2,!0),this.v.setTripleAt(t+i,e+s,t+i-r,e+s,t+i,e+s,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:s},r([bt],t),t}();var m={getShapeProp:function(t,e,i){var s;return 3===i||4===i?s=(3===i?e.pt:e.ks).k.length?new l(t,e,i):new o(t,e,i):5===i?s=new d(t,e):6===i?s=new p(t,e):7===i&&(s=new f(t,e)),s.k&&t.addDynamicProperty(s),s},getConstructorFunction:function(){return o},getKeyframedConstructorFunction:function(){return l}};return m}(),Ft=function(){var t=Math.cos,e=Math.sin,i=Math.tan,s=Math.round;function a(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function r(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(s,-a,0,0,a,s,0,0,0,0,1,0,0,0,0,1)}function n(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(1,0,0,0,0,s,-a,0,0,a,s,0,0,0,0,1)}function o(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(s,0,a,0,0,1,0,0,-a,0,s,0,0,0,0,1)}function l(i){if(0===i)return this;var s=t(i),a=e(i);return this._t(s,-a,0,0,a,s,0,0,0,0,1,0,0,0,0,1)}function p(t,e){return this._t(1,e,t,1,0,0)}function f(t,e){return this.shear(i(t),i(e))}function d(s,a){var r=t(a),n=e(a);return this._t(r,n,0,0,-n,r,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,i(s),1,0,0,0,0,1,0,0,0,0,1)._t(r,-n,0,0,n,r,0,0,0,0,1,0,0,0,0,1)}function m(t,e,i){return i||0===i||(i=1),1===t&&1===e&&1===i?this:this._t(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1)}function c(t,e,i,s,a,r,n,o,h,l,p,f,d,m,c,u){return this.props[0]=t,this.props[1]=e,this.props[2]=i,this.props[3]=s,this.props[4]=a,this.props[5]=r,this.props[6]=n,this.props[7]=o,this.props[8]=h,this.props[9]=l,this.props[10]=p,this.props[11]=f,this.props[12]=d,this.props[13]=m,this.props[14]=c,this.props[15]=u,this}function u(t,e,i){return i=i||0,0!==t||0!==e||0!==i?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,i,1):this}function g(t,e,i,s,a,r,n,o,h,l,p,f,d,m,c,u){var g=this.props;if(1===t&&0===e&&0===i&&0===s&&0===a&&1===r&&0===n&&0===o&&0===h&&0===l&&1===p&&0===f)return g[12]=g[12]*t+g[15]*d,g[13]=g[13]*r+g[15]*m,g[14]=g[14]*p+g[15]*c,g[15]*=u,this._identityCalculated=!1,this;var y=g[0],v=g[1],b=g[2],_=g[3],k=g[4],P=g[5],A=g[6],S=g[7],x=g[8],w=g[9],D=g[10],C=g[11],M=g[12],T=g[13],F=g[14],E=g[15];return g[0]=y*t+v*a+b*h+_*d,g[1]=y*e+v*r+b*l+_*m,g[2]=y*i+v*n+b*p+_*c,g[3]=y*s+v*o+b*f+_*u,g[4]=k*t+P*a+A*h+S*d,g[5]=k*e+P*r+A*l+S*m,g[6]=k*i+P*n+A*p+S*c,g[7]=k*s+P*o+A*f+S*u,g[8]=x*t+w*a+D*h+C*d,g[9]=x*e+w*r+D*l+C*m,g[10]=x*i+w*n+D*p+C*c,g[11]=x*s+w*o+D*f+C*u,g[12]=M*t+T*a+F*h+E*d,g[13]=M*e+T*r+F*l+E*m,g[14]=M*i+T*n+F*p+E*c,g[15]=M*s+T*o+F*f+E*u,this._identityCalculated=!1,this}function y(t){var e=t.props;return this.transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}function v(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function b(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function _(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function k(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function P(t,e,i){return{x:t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}}function A(t,e,i){return t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12]}function S(t,e,i){return t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13]}function x(t,e,i){return t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}function w(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,i=-this.props[1]/t,s=-this.props[4]/t,a=this.props[0]/t,r=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,n=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,o=new Ft;return o.props[0]=e,o.props[1]=i,o.props[4]=s,o.props[5]=a,o.props[12]=r,o.props[13]=n,o}function D(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function C(t){var e,i=t.length,s=[];for(e=0;e<i;e+=1)s[e]=D(t[e]);return s}function M(t,e,i){var s=h("float32",6);if(this.isIdentity())s[0]=t[0],s[1]=t[1],s[2]=e[0],s[3]=e[1],s[4]=i[0],s[5]=i[1];else{var a=this.props[0],r=this.props[1],n=this.props[4],o=this.props[5],l=this.props[12],p=this.props[13];s[0]=t[0]*a+t[1]*n+l,s[1]=t[0]*r+t[1]*o+p,s[2]=e[0]*a+e[1]*n+l,s[3]=e[0]*r+e[1]*o+p,s[4]=i[0]*a+i[1]*n+l,s[5]=i[0]*r+i[1]*o+p}return s}function T(t,e,i){return this.isIdentity()?[t,e,i]:[t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]]}function F(t,e){if(this.isIdentity())return t+","+e;var i=this.props;return Math.round(100*(t*i[0]+e*i[4]+i[12]))/100+","+Math.round(100*(t*i[1]+e*i[5]+i[13]))/100}function E(){for(var t=0,e=this.props,i="matrix3d(";t<16;)i+=s(1e4*e[t])/1e4,i+=15===t?")":",",t+=1;return i}function I(t){return t<1e-6&&t>0||t>-1e-6&&t<0?s(1e4*t)/1e4:t}function L(){var t=this.props;return"matrix("+I(t[0])+","+I(t[1])+","+I(t[4])+","+I(t[5])+","+I(t[12])+","+I(t[13])+")"}return function(){this.reset=a,this.rotate=r,this.rotateX=n,this.rotateY=o,this.rotateZ=l,this.skew=f,this.skewFromAxis=d,this.shear=p,this.scale=m,this.setTransform=c,this.translate=u,this.transform=g,this.multiply=y,this.applyToPoint=P,this.applyToX=A,this.applyToY=S,this.applyToZ=x,this.applyToPointArray=T,this.applyToTriplePoints=M,this.applyToPointStringified=F,this.toCSS=E,this.to2dCSS=L,this.clone=_,this.cloneFromProps=k,this.equals=b,this.inversePoints=C,this.inversePoint=D,this.getInverseMatrix=w,this._t=this.transform,this.isIdentity=v,this._identity=!0,this._identityCalculated=!1,this.props=h("float32",16),this.reset()}}();function Et(t){return Et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Et(t)}var It={};function Lt(){$.searchAnimations()}It.play=$.play,It.pause=$.pause,It.setLocationHref=function(e){t=e},It.togglePause=$.togglePause,It.setSpeed=$.setSpeed,It.setDirection=$.setDirection,It.stop=$.stop,It.searchAnimations=Lt,It.registerAnimation=$.registerAnimation,It.loadAnimation=function(t){return $.loadAnimation(t)},It.setSubframeRendering=function(t){!function(t){f=!!t}(t)},It.resize=$.resize,It.goToAndStop=$.goToAndStop,It.destroy=$.destroy,It.setQuality=function(t){if("string"==typeof t)switch(t){case"high":q(200);break;default:case"medium":q(50);break;case"low":q(10)}else!isNaN(t)&&t>1&&q(t);j()>=50?S(!1):S(!0)},It.inBrowser=function(){return"undefined"!=typeof navigator},It.installPlugin=function(t,e){"expressions"===t&&(d=e)},It.freeze=$.freeze,It.unfreeze=$.unfreeze,It.setVolume=$.setVolume,It.mute=$.mute,It.unmute=$.unmute,It.getRegisteredAnimations=$.getRegisteredAnimations,It.useWebWorker=function(t){e=!!t},It.setIDPrefix=function(t){c=t},It.__getFactory=function(t){switch(t){case"propertyFactory":return vt;case"shapePropertyFactory":return Tt;case"matrix":return Ft;default:return null}},It.version="5.12.2";var Vt="",Rt=document.getElementsByTagName("script"),zt=Rt[Rt.length-1]||{src:""};Vt=zt.src?zt.src.replace(/^[^\?]+\??/,""):"",function(t){for(var e=Vt.split("&"),i=0;i<e.length;i+=1){var s=e[i].split("=");if(decodeURIComponent(s[0])==t)return decodeURIComponent(s[1])}}("renderer");var Ot=setInterval((function(){"complete"===document.readyState&&(clearInterval(Ot),Lt())}),100);try{"object"===("undefined"==typeof exports?"undefined":Et(exports))&&"undefined"!=typeof module||"function"==typeof define&&define.amd||(window.bodymovin=It)}catch(t){}var Nt=function(){var t={},e={};return t.registerModifier=function(t,i){e[t]||(e[t]=i)},t.getModifier=function(t,i,s){return new e[t](i,s)},t}();function Bt(){}function qt(){}function jt(){}Bt.prototype.initModifierProperties=function(){},Bt.prototype.addShapeToModifier=function(){},Bt.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:Mt.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},Bt.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=i,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},Bt.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},r([bt],Bt),r([Bt],qt),qt.prototype.initModifierProperties=function(t,e){this.s=vt.getProp(t,e.s,0,.01,this),this.e=vt.getProp(t,e.e,0,.01,this),this.o=vt.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},qt.prototype.addShapeToModifier=function(t){t.pathsData=[]},qt.prototype.calculateShapeEdges=function(t,e,i,s,a){var r=[];e<=1?r.push({s:t,e:e}):t>=1?r.push({s:t-1,e:e-1}):(r.push({s:t,e:1}),r.push({s:0,e:e-1}));var n,o,h=[],l=r.length;for(n=0;n<l;n+=1){var p,f;if(!((o=r[n]).e*a<s||o.s*a>s+i))p=o.s*a<=s?0:(o.s*a-s)/i,f=o.e*a>=s+i?1:(o.e*a-s)/i,h.push([p,f])}return h.length||h.push([0,0]),h},qt.prototype.releasePathsData=function(t){var e,i=t.length;for(e=0;e<i;e+=1)at.release(t[e]);return t.length=0,t},qt.prototype.processShapes=function(t){var e,i,s,a;if(this._mdf||t){var r=this.o.v%360/360;if(r<0&&(r+=1),(e=this.s.v>1?1+r:this.s.v<0?0+r:this.s.v+r)>(i=this.e.v>1?1+r:this.e.v<0?0+r:this.e.v+r)){var n=e;e=i,i=n}e=1e-4*Math.round(1e4*e),i=1e-4*Math.round(1e4*i),this.sValue=e,this.eValue=i}else e=this.sValue,i=this.eValue;var o,h,l,p,f,d=this.shapes.length,m=0;if(i===e)for(a=0;a<d;a+=1)this.shapes[a].localShapeCollection.releaseShapes(),this.shapes[a].shape._mdf=!0,this.shapes[a].shape.paths=this.shapes[a].localShapeCollection,this._mdf&&(this.shapes[a].pathsData.length=0);else if(1===i&&0===e||0===i&&1===e){if(this._mdf)for(a=0;a<d;a+=1)this.shapes[a].pathsData.length=0,this.shapes[a].shape._mdf=!0}else{var c,u,g=[];for(a=0;a<d;a+=1)if((c=this.shapes[a]).shape._mdf||this._mdf||t||2===this.m){if(h=(s=c.shape.paths)._length,f=0,!c.shape._mdf&&c.pathsData.length)f=c.totalShapeLength;else{for(l=this.releasePathsData(c.pathsData),o=0;o<h;o+=1)p=rt.getSegmentsLength(s.shapes[o]),l.push(p),f+=p.totalLength;c.totalShapeLength=f,c.pathsData=l}m+=f,c.shape._mdf=!0}else c.shape.paths=c.localShapeCollection;var y,v=e,b=i,_=0;for(a=d-1;a>=0;a-=1)if((c=this.shapes[a]).shape._mdf){for((u=c.localShapeCollection).releaseShapes(),2===this.m&&d>1?(y=this.calculateShapeEdges(e,i,c.totalShapeLength,_,m),_+=c.totalShapeLength):y=[[v,b]],h=y.length,o=0;o<h;o+=1){v=y[o][0],b=y[o][1],g.length=0,b<=1?g.push({s:c.totalShapeLength*v,e:c.totalShapeLength*b}):v>=1?g.push({s:c.totalShapeLength*(v-1),e:c.totalShapeLength*(b-1)}):(g.push({s:c.totalShapeLength*v,e:c.totalShapeLength}),g.push({s:0,e:c.totalShapeLength*(b-1)}));var k=this.addShapes(c,g[0]);if(g[0].s!==g[0].e){if(g.length>1)if(c.shape.paths.shapes[c.shape.paths._length-1].c){var P=k.pop();this.addPaths(k,u),k=this.addShapes(c,g[1],P)}else this.addPaths(k,u),k=this.addShapes(c,g[1]);this.addPaths(k,u)}}c.shape.paths=u}}},qt.prototype.addPaths=function(t,e){var i,s=t.length;for(i=0;i<s;i+=1)e.addShape(t[i])},qt.prototype.addSegment=function(t,e,i,s,a,r,n){a.setXYAt(e[0],e[1],"o",r),a.setXYAt(i[0],i[1],"i",r+1),n&&a.setXYAt(t[0],t[1],"v",r),a.setXYAt(s[0],s[1],"v",r+1)},qt.prototype.addSegmentFromArray=function(t,e,i,s){e.setXYAt(t[1],t[5],"o",i),e.setXYAt(t[2],t[6],"i",i+1),s&&e.setXYAt(t[0],t[4],"v",i),e.setXYAt(t[3],t[7],"v",i+1)},qt.prototype.addShapes=function(t,e,i){var s,a,r,n,o,h,l,p,f=t.pathsData,d=t.shape.paths.shapes,m=t.shape.paths._length,c=0,u=[],g=!0;for(i?(o=i._length,p=i._length):(i=At.newElement(),o=0,p=0),u.push(i),s=0;s<m;s+=1){for(h=f[s].lengths,i.c=d[s].c,r=d[s].c?h.length:h.length+1,a=1;a<r;a+=1)if(c+(n=h[a-1]).addedLength<e.s)c+=n.addedLength,i.c=!1;else{if(c>e.e){i.c=!1;break}e.s<=c&&e.e>=c+n.addedLength?(this.addSegment(d[s].v[a-1],d[s].o[a-1],d[s].i[a],d[s].v[a],i,o,g),g=!1):(l=rt.getNewSegment(d[s].v[a-1],d[s].v[a],d[s].o[a-1],d[s].i[a],(e.s-c)/n.addedLength,(e.e-c)/n.addedLength,h[a-1]),this.addSegmentFromArray(l,i,o,g),g=!1,i.c=!1),c+=n.addedLength,o+=1}if(d[s].c&&h.length){if(n=h[a-1],c<=e.e){var y=h[a-1].addedLength;e.s<=c&&e.e>=c+y?(this.addSegment(d[s].v[a-1],d[s].o[a-1],d[s].i[0],d[s].v[0],i,o,g),g=!1):(l=rt.getNewSegment(d[s].v[a-1],d[s].v[0],d[s].o[a-1],d[s].i[0],(e.s-c)/y,(e.e-c)/y,h[a-1]),this.addSegmentFromArray(l,i,o,g),g=!1,i.c=!1)}else i.c=!1;c+=n.addedLength,o+=1}if(i._length&&(i.setXYAt(i.v[p][0],i.v[p][1],"i",p),i.setXYAt(i.v[i._length-1][0],i.v[i._length-1][1],"o",i._length-1)),c>e.e)break;s<m-1&&(i=At.newElement(),g=!0,u.push(i),o=0)}return u},r([Bt],jt),jt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=vt.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},jt.prototype.processPath=function(t,e){var i=e/100,s=[0,0],a=t._length,r=0;for(r=0;r<a;r+=1)s[0]+=t.v[r][0],s[1]+=t.v[r][1];s[0]/=a,s[1]/=a;var n,o,h,l,p,f,d=At.newElement();for(d.c=t.c,r=0;r<a;r+=1)n=t.v[r][0]+(s[0]-t.v[r][0])*i,o=t.v[r][1]+(s[1]-t.v[r][1])*i,h=t.o[r][0]+(s[0]-t.o[r][0])*-i,l=t.o[r][1]+(s[1]-t.o[r][1])*-i,p=t.i[r][0]+(s[0]-t.i[r][0])*-i,f=t.i[r][1]+(s[1]-t.i[r][1])*-i,d.setTripleAt(n,o,h,l,p,f,r);return d},jt.prototype.processShapes=function(t){var e,i,s,a,r,n,o=this.shapes.length,h=this.amount.v;if(0!==h)for(i=0;i<o;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],h));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var Wt=function(){var t=[0,0];function e(t,e,i){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Ft,this.pre=new Ft,this.appliedTransformations=0,this.initDynamicPropertyContainer(i||t),e.p&&e.p.s?(this.px=vt.getProp(t,e.p.x,0,0,this),this.py=vt.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=vt.getProp(t,e.p.z,0,0,this))):this.p=vt.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=vt.getProp(t,e.rx,0,P,this),this.ry=vt.getProp(t,e.ry,0,P,this),this.rz=vt.getProp(t,e.rz,0,P,this),e.or.k[0].ti){var s,a=e.or.k.length;for(s=0;s<a;s+=1)e.or.k[s].to=null,e.or.k[s].ti=null}this.or=vt.getProp(t,e.or,1,P,this),this.or.sh=!0}else this.r=vt.getProp(t,e.r||{k:0},0,P,this);e.sk&&(this.sk=vt.getProp(t,e.sk,0,P,this),this.sa=vt.getProp(t,e.sa,0,P,this)),this.a=vt.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=vt.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=vt.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){var i;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var s,a;if(i=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(s=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/i,0),a=this.p.getValueAtTime(this.p.keyframes[0].t/i,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(s=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/i,0),a=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/i,0)):(s=this.p.pv,a=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/i,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){s=[],a=[];var r=this.px,n=this.py;r._caching.lastFrame+r.offsetTime<=r.keyframes[0].t?(s[0]=r.getValueAtTime((r.keyframes[0].t+.01)/i,0),s[1]=n.getValueAtTime((n.keyframes[0].t+.01)/i,0),a[0]=r.getValueAtTime(r.keyframes[0].t/i,0),a[1]=n.getValueAtTime(n.keyframes[0].t/i,0)):r._caching.lastFrame+r.offsetTime>=r.keyframes[r.keyframes.length-1].t?(s[0]=r.getValueAtTime(r.keyframes[r.keyframes.length-1].t/i,0),s[1]=n.getValueAtTime(n.keyframes[n.keyframes.length-1].t/i,0),a[0]=r.getValueAtTime((r.keyframes[r.keyframes.length-1].t-.01)/i,0),a[1]=n.getValueAtTime((n.keyframes[n.keyframes.length-1].t-.01)/i,0)):(s=[r.pv,n.pv],a[0]=r.getValueAtTime((r._caching.lastFrame+r.offsetTime-.01)/i,r.offsetTime),a[1]=n.getValueAtTime((n._caching.lastFrame+n.offsetTime-.01)/i,n.offsetTime))}else s=a=t;this.v.rotate(-Math.atan2(s[1]-a[1],s[0]-a[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},r([bt],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=bt.prototype.addDynamicProperty,{getTransformProperty:function(t,i,s){return new e(t,i,s)}}}();function Xt(){}function Ht(){}function Yt(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function Gt(t){return Math.abs(t)<=1e-5}function Kt(t,e,i){return t*(1-i)+e*i}function Jt(t,e,i){return[Kt(t[0],e[0],i),Kt(t[1],e[1],i)]}function Ut(t,e,i,s){return[3*e-t-3*i+s,3*t-6*e+3*i,-3*t+3*e,t]}function Zt(t){return new Qt(t,t,t,t,!1)}function Qt(t,e,i,s,a){a&&oe(t,e)&&(e=Jt(t,s,1/3)),a&&oe(i,s)&&(i=Jt(t,s,2/3));var r=Ut(t[0],e[0],i[0],s[0]),n=Ut(t[1],e[1],i[1],s[1]);this.a=[r[0],n[0]],this.b=[r[1],n[1]],this.c=[r[2],n[2]],this.d=[r[3],n[3]],this.points=[t,e,i,s]}function $t(t,e){var i=t.points[0][e],s=t.points[t.points.length-1][e];if(i>s){var a=s;s=i,i=a}for(var r=function(t,e,i){if(0===t)return[];var s=e*e-4*t*i;if(s<0)return[];var a=-e/(2*t);if(0===s)return[a];var r=Math.sqrt(s)/(2*t);return[a-r,a+r]}(3*t.a[e],2*t.b[e],t.c[e]),n=0;n<r.length;n+=1)if(r[n]>0&&r[n]<1){var o=t.point(r[n])[e];o<i?i=o:o>s&&(s=o)}return{min:i,max:s}}function te(t,e,i){var s=t.boundingBox();return{cx:s.cx,cy:s.cy,width:s.width,height:s.height,bez:t,t:(e+i)/2,t1:e,t2:i}}function ee(t){var e=t.bez.split(.5);return[te(e[0],t.t1,t.t),te(e[1],t.t,t.t2)]}function ie(t,e,i,s,a,r){var n,o;if(n=t,o=e,2*Math.abs(n.cx-o.cx)<n.width+o.width&&2*Math.abs(n.cy-o.cy)<n.height+o.height)if(i>=r||t.width<=s&&t.height<=s&&e.width<=s&&e.height<=s)a.push([t.t,e.t]);else{var h=ee(t),l=ee(e);ie(h[0],l[0],i+1,s,a,r),ie(h[0],l[1],i+1,s,a,r),ie(h[1],l[0],i+1,s,a,r),ie(h[1],l[1],i+1,s,a,r)}}function se(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function ae(t,e,i,s){var a=[t[0],t[1],1],r=[e[0],e[1],1],n=[i[0],i[1],1],o=[s[0],s[1],1],h=se(se(a,r),se(n,o));return Gt(h[2])?null:[h[0]/h[2],h[1]/h[2]]}function re(t,e,i){return[t[0]+Math.cos(e)*i,t[1]-Math.sin(e)*i]}function ne(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function oe(t,e){return Yt(t[0],e[0])&&Yt(t[1],e[1])}function he(){}function le(t,e,i,s,a,r,n){var o=i-Math.PI/2,h=i+Math.PI/2,l=e[0]+Math.cos(i)*s*a,p=e[1]-Math.sin(i)*s*a;t.setTripleAt(l,p,l+Math.cos(o)*r,p-Math.sin(o)*r,l+Math.cos(h)*n,p-Math.sin(h)*n,t.length())}function pe(t,e){var i,s,a,r,n=0===e?t.length()-1:e-1,o=(e+1)%t.length(),h=t.v[n],l=t.v[o],p=(i=h,a=[(s=l)[0]-i[0],s[1]-i[1]],r=.5*-Math.PI,[Math.cos(r)*a[0]-Math.sin(r)*a[1],Math.sin(r)*a[0]+Math.cos(r)*a[1]]);return Math.atan2(0,1)-Math.atan2(p[1],p[0])}function fe(t,e,i,s,a,r,n){var o=pe(e,i),h=e.v[i%e._length],l=e.v[0===i?e._length-1:i-1],p=e.v[(i+1)%e._length],f=2===r?Math.sqrt(Math.pow(h[0]-l[0],2)+Math.pow(h[1]-l[1],2)):0,d=2===r?Math.sqrt(Math.pow(h[0]-p[0],2)+Math.pow(h[1]-p[1],2)):0;le(t,e.v[i%e._length],o,n,s,d/(2*(a+1)),f/(2*(a+1)))}function de(t,e,i,s,a,r){for(var n=0;n<s;n+=1){var o=(n+1)/(s+1),h=2===a?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,l=e.normalAngle(o);le(t,e.point(o),l,r,i,h/(2*(s+1)),h/(2*(s+1))),r=-r}return r}function me(t,e,i){var s=Math.atan2(e[0]-t[0],e[1]-t[1]);return[re(t,s,i),re(e,s,i)]}function ce(t,e){var i,s,a,r,n,o,h;i=(h=me(t.points[0],t.points[1],e))[0],s=h[1],a=(h=me(t.points[1],t.points[2],e))[0],r=h[1],n=(h=me(t.points[2],t.points[3],e))[0],o=h[1];var l=ae(i,s,a,r);null===l&&(l=s);var p=ae(n,o,a,r);return null===p&&(p=n),new Qt(i,l,p,o)}function ue(t,e,i,s,a){var r=e.points[3],n=i.points[0];if(3===s)return r;if(oe(r,n))return r;if(2===s){var o=-e.tangentAngle(1),h=-i.tangentAngle(0)+Math.PI,l=ae(r,re(r,o+Math.PI/2,100),n,re(n,o+Math.PI/2,100)),p=l?ne(l,r):ne(r,n)/2,f=re(r,o,2*p*A);return t.setXYAt(f[0],f[1],"o",t.length()-1),f=re(n,h,2*p*A),t.setTripleAt(n[0],n[1],n[0],n[1],f[0],f[1],t.length()),n}var d=ae(oe(r,e.points[2])?e.points[0]:e.points[2],r,n,oe(n,i.points[1])?i.points[3]:i.points[1]);return d&&ne(d,r)<a?(t.setTripleAt(d[0],d[1],d[0],d[1],d[0],d[1],t.length()),d):r}function ge(t,e){var i=t.intersections(e);return i.length&&Yt(i[0][0],1)&&i.shift(),i.length?i[0]:null}function ye(t,e){var i=t.slice(),s=e.slice(),a=ge(t[t.length-1],e[0]);return a&&(i[t.length-1]=t[t.length-1].split(a[0])[0],s[0]=e[0].split(a[1])[1]),t.length>1&&e.length>1&&(a=ge(t[0],e[e.length-1]))?[[t[0].split(a[0])[0]],[e[e.length-1].split(a[1])[1]]]:[i,s]}function ve(t,e){var i,s,a,r,n=t.inflectionPoints();if(0===n.length)return[ce(t,e)];if(1===n.length||Yt(n[1],1))return i=(a=t.split(n[0]))[0],s=a[1],[ce(i,e),ce(s,e)];i=(a=t.split(n[0]))[0];var o=(n[1]-n[0])/(1-n[0]);return r=(a=a[1].split(o))[0],s=a[1],[ce(i,e),ce(r,e),ce(s,e)]}function be(){}function _e(t){for(var e=t.fStyle?t.fStyle.split(" "):[],i="normal",s="normal",a=e.length,r=0;r<a;r+=1)switch(e[r].toLowerCase()){case"italic":s="italic";break;case"bold":i="700";break;case"black":i="900";break;case"medium":i="500";break;case"regular":case"normal":i="400";break;case"light":case"thin":i="200"}return{style:s,weight:t.fWeight||i}}r([Bt],Xt),Xt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=vt.getProp(t,e.c,0,null,this),this.o=vt.getProp(t,e.o,0,null,this),this.tr=Wt.getTransformProperty(t,e.tr,this),this.so=vt.getProp(t,e.tr.so,0,.01,this),this.eo=vt.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Ft,this.rMatrix=new Ft,this.sMatrix=new Ft,this.tMatrix=new Ft,this.matrix=new Ft},Xt.prototype.applyTransforms=function(t,e,i,s,a,r){var n=r?-1:1,o=s.s.v[0]+(1-s.s.v[0])*(1-a),h=s.s.v[1]+(1-s.s.v[1])*(1-a);t.translate(s.p.v[0]*n*a,s.p.v[1]*n*a,s.p.v[2]),e.translate(-s.a.v[0],-s.a.v[1],s.a.v[2]),e.rotate(-s.r.v*n*a),e.translate(s.a.v[0],s.a.v[1],s.a.v[2]),i.translate(-s.a.v[0],-s.a.v[1],s.a.v[2]),i.scale(r?1/o:o,r?1/h:h),i.translate(s.a.v[0],s.a.v[1],s.a.v[2])},Xt.prototype.init=function(t,e,i,s){for(this.elem=t,this.arr=e,this.pos=i,this.elemsData=s,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[i]);i>0;)i-=1,this._elements.unshift(e[i]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},Xt.prototype.resetElements=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},Xt.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},Xt.prototype.changeGroupRender=function(t,e){var i,s=t.length;for(i=0;i<s;i+=1)t[i]._render=e,"gr"===t[i].ty&&this.changeGroupRender(t[i].it,e)},Xt.prototype.processShapes=function(t){var e,i,s,a,r,n=!1;if(this._mdf||t){var o,h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),n=!0}for(r=0,s=0;s<=this._groups.length-1;s+=1){if(o=r<h,this._groups[s]._render=o,this.changeGroupRender(this._groups[s].it,o),!o){var p=this.elemsData[s].it,f=p[p.length-1];0!==f.transform.op.v?(f.transform.op._mdf=!0,f.transform.op.v=0):f.transform.op._mdf=!1}r+=1}this._currentCopies=h;var d=this.o.v,m=d%1,c=d>0?Math.floor(d):Math.ceil(d),u=this.pMatrix.props,g=this.rMatrix.props,y=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var v,b,_=0;if(d>0){for(;_<c;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),_+=1;m&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,m,!1),_+=m)}else if(d<0){for(;_>c;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),_-=1;m&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-m,!0),_-=m)}for(s=1===this.data.m?0:this._currentCopies-1,a=1===this.data.m?1:-1,r=this._currentCopies;r;){if(b=(i=(e=this.elemsData[s].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(s/(this._currentCopies-1)),0!==_){for((0!==s&&1===a||s!==this._currentCopies-1&&-1===a)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7],u[8],u[9],u[10],u[11],u[12],u[13],u[14],u[15]),v=0;v<b;v+=1)i[v]=this.matrix.props[v];this.matrix.reset()}else for(this.matrix.reset(),v=0;v<b;v+=1)i[v]=this.matrix.props[v];_+=1,r-=1,s+=a}}else for(r=this._currentCopies,s=0,a=1;r;)i=(e=this.elemsData[s].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,r-=1,s+=a;return n},Xt.prototype.addShape=function(){},r([Bt],Ht),Ht.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=vt.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},Ht.prototype.processPath=function(t,e){var i,s=At.newElement();s.c=t.c;var a,r,n,o,h,l,p,f,d,m,c,u,g=t._length,y=0;for(i=0;i<g;i+=1)a=t.v[i],n=t.o[i],r=t.i[i],a[0]===n[0]&&a[1]===n[1]&&a[0]===r[0]&&a[1]===r[1]?0!==i&&i!==g-1||t.c?(o=0===i?t.v[g-1]:t.v[i-1],l=(h=Math.sqrt(Math.pow(a[0]-o[0],2)+Math.pow(a[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=c=a[0]+(o[0]-a[0])*l,f=u=a[1]-(a[1]-o[1])*l,d=p-(p-a[0])*A,m=f-(f-a[1])*A,s.setTripleAt(p,f,d,m,c,u,y),y+=1,o=i===g-1?t.v[0]:t.v[i+1],l=(h=Math.sqrt(Math.pow(a[0]-o[0],2)+Math.pow(a[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=d=a[0]+(o[0]-a[0])*l,f=m=a[1]+(o[1]-a[1])*l,c=p-(p-a[0])*A,u=f-(f-a[1])*A,s.setTripleAt(p,f,d,m,c,u,y),y+=1):(s.setTripleAt(a[0],a[1],n[0],n[1],r[0],r[1],y),y+=1):(s.setTripleAt(t.v[i][0],t.v[i][1],t.o[i][0],t.o[i][1],t.i[i][0],t.i[i][1],y),y+=1);return s},Ht.prototype.processShapes=function(t){var e,i,s,a,r,n,o=this.shapes.length,h=this.rd.v;if(0!==h)for(i=0;i<o;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],h));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},Qt.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},Qt.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},Qt.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},Qt.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},Qt.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(Gt(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,i=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(i<0)return[];var s=Math.sqrt(i);return Gt(s)?s>0&&s<1?[e]:[]:[e-s,e+s].filter((function(t){return t>0&&t<1}))},Qt.prototype.split=function(t){if(t<=0)return[Zt(this.points[0]),this];if(t>=1)return[this,Zt(this.points[this.points.length-1])];var e=Jt(this.points[0],this.points[1],t),i=Jt(this.points[1],this.points[2],t),s=Jt(this.points[2],this.points[3],t),a=Jt(e,i,t),r=Jt(i,s,t),n=Jt(a,r,t);return[new Qt(this.points[0],e,a,n,!0),new Qt(n,r,s,this.points[3],!0)]},Qt.prototype.bounds=function(){return{x:$t(this,0),y:$t(this,1)}},Qt.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},Qt.prototype.intersections=function(t,e,i){void 0===e&&(e=2),void 0===i&&(i=7);var s=[];return ie(te(this,0,1),te(t,0,1),0,e,s,i),s},Qt.shapeSegment=function(t,e){var i=(e+1)%t.length();return new Qt(t.v[e],t.o[e],t.i[i],t.v[i],!0)},Qt.shapeSegmentInverted=function(t,e){var i=(e+1)%t.length();return new Qt(t.v[i],t.i[i],t.o[e],t.v[e],!0)},r([Bt],he),he.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=vt.getProp(t,e.s,0,null,this),this.frequency=vt.getProp(t,e.r,0,null,this),this.pointsType=vt.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},he.prototype.processPath=function(t,e,i,s){var a=t._length,r=At.newElement();if(r.c=t.c,t.c||(a-=1),0===a)return r;var n=-1,o=Qt.shapeSegment(t,0);fe(r,t,0,e,i,s,n);for(var h=0;h<a;h+=1)n=de(r,o,e,i,s,-n),o=h!==a-1||t.c?Qt.shapeSegment(t,(h+1)%a):null,fe(r,t,h+1,e,i,s,n);return r},he.prototype.processShapes=function(t){var e,i,s,a,r,n,o=this.shapes.length,h=this.amplitude.v,l=Math.max(0,Math.round(this.frequency.v)),p=this.pointsType.v;if(0!==h)for(i=0;i<o;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],h,l,p));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},r([Bt],be),be.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=vt.getProp(t,e.a,0,null,this),this.miterLimit=vt.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},be.prototype.processPath=function(t,e,i,s){var a=At.newElement();a.c=t.c;var r,n,o,h=t.length();t.c||(h-=1);var l=[];for(r=0;r<h;r+=1)o=Qt.shapeSegment(t,r),l.push(ve(o,e));if(!t.c)for(r=h-1;r>=0;r-=1)o=Qt.shapeSegmentInverted(t,r),l.push(ve(o,e));l=function(t){for(var e,i=1;i<t.length;i+=1)e=ye(t[i-1],t[i]),t[i-1]=e[0],t[i]=e[1];return t.length>1&&(e=ye(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}(l);var p=null,f=null;for(r=0;r<l.length;r+=1){var d=l[r];for(f&&(p=ue(a,f,d[0],i,s)),f=d[d.length-1],n=0;n<d.length;n+=1)o=d[n],p&&oe(o.points[0],p)?a.setXYAt(o.points[1][0],o.points[1][1],"o",a.length()-1):a.setTripleAt(o.points[0][0],o.points[0][1],o.points[1][0],o.points[1][1],o.points[0][0],o.points[0][1],a.length()),a.setTripleAt(o.points[3][0],o.points[3][1],o.points[3][0],o.points[3][1],o.points[2][0],o.points[2][1],a.length()),p=o.points[3]}return l.length&&ue(a,f,l[0][0],i,s),a},be.prototype.processShapes=function(t){var e,i,s,a,r,n,o=this.shapes.length,h=this.amount.v,l=this.miterLimit.v,p=this.lineJoin;if(0!==h)for(i=0;i<o;i+=1){if(n=(r=this.shapes[i]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,s=0;s<a;s+=1)n.addShape(this.processPath(e[s],h,p,l));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var ke=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var i=127988,s=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function r(t,e){var i=a("span");i.setAttribute("aria-hidden",!0),i.style.fontFamily=e;var s=a("span");s.innerText="giItT1WQy@!-/#",i.style.position="absolute",i.style.left="-10000px",i.style.top="-10000px",i.style.fontSize="300px",i.style.fontVariant="normal",i.style.fontStyle="normal",i.style.fontWeight="normal",i.style.letterSpacing="0",i.appendChild(s),document.body.appendChild(i);var r=s.offsetWidth;return s.style.fontFamily=function(t){var e,i=t.split(","),s=i.length,a=[];for(e=0;e<s;e+=1)"sans-serif"!==i[e]&&"monospace"!==i[e]&&a.push(i[e]);return a.join(",")}(t)+", "+e,{node:s,w:r,parent:i}}function n(t,e){var i,s=document.body&&e?"svg":"canvas",a=_e(t);if("svg"===s){var r=W("text");r.style.fontSize="100px",r.setAttribute("font-family",t.fFamily),r.setAttribute("font-style",a.style),r.setAttribute("font-weight",a.weight),r.textContent="1",t.fClass?(r.style.fontFamily="inherit",r.setAttribute("class",t.fClass)):r.style.fontFamily=t.fFamily,e.appendChild(r),i=r}else{var n=new OffscreenCanvas(500,500).getContext("2d");n.font=a.style+" "+a.weight+" 100px "+t.fFamily,i=n}return{measureText:function(t){return"svg"===s?(i.textContent=t,i.getComputedTextLength()):i.measureText(t).width}}}function o(t){var e=0,i=t.charCodeAt(0);if(i>=55296&&i<=56319){var s=t.charCodeAt(1);s>=56320&&s<=57343&&(e=1024*(i-55296)+s-56320+65536)}return e}function h(t){var e=o(t);return e>=127462&&e<=127487}var l=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};l.isModifier=function(t,e){var i=t.toString(16)+e.toString(16);return-1!==s.indexOf(i)},l.isZeroWidthJoiner=function(t){return 8205===t},l.isFlagEmoji=function(t){return h(t.substr(0,2))&&h(t.substr(2,2))},l.isRegionalCode=h,l.isCombinedCharacter=function(t){return-1!==e.indexOf(t)},l.isRegionalFlag=function(t,e){var s=o(t.substr(e,2));if(s!==i)return!1;var a=0;for(e+=2;a<5;){if((s=o(t.substr(e,2)))<917601||s>917626)return!1;a+=1,e+=2}return 917631===o(t.substr(e,2))},l.isVariationSelector=function(t){return 65039===t},l.BLACK_FLAG_CODE_POINT=i;var p={addChars:function(t){if(t){var e;this.chars||(this.chars=[]);var i,s,a=t.length,r=this.chars.length;for(e=0;e<a;e+=1){for(i=0,s=!1;i<r;)this.chars[i].style===t[e].style&&this.chars[i].fFamily===t[e].fFamily&&this.chars[i].ch===t[e].ch&&(s=!0),i+=1;s||(this.chars.push(t[e]),r+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach((function(t){t.helper=n(t),t.cache={}})),void(this.fonts=t.list);var i,s=t.list,o=s.length,h=o;for(i=0;i<o;i+=1){var l,p,f=!0;if(s[i].loaded=!1,s[i].monoCase=r(s[i].fFamily,"monospace"),s[i].sansCase=r(s[i].fFamily,"sans-serif"),s[i].fPath){if("p"===s[i].fOrigin||3===s[i].origin){if((l=document.querySelectorAll('style[f-forigin="p"][f-family="'+s[i].fFamily+'"], style[f-origin="3"][f-family="'+s[i].fFamily+'"]')).length>0&&(f=!1),f){var d=a("style");d.setAttribute("f-forigin",s[i].fOrigin),d.setAttribute("f-origin",s[i].origin),d.setAttribute("f-family",s[i].fFamily),d.type="text/css",d.innerText="@font-face {font-family: "+s[i].fFamily+"; font-style: normal; src: url('"+s[i].fPath+"');}",e.appendChild(d)}}else if("g"===s[i].fOrigin||1===s[i].origin){for(l=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),p=0;p<l.length;p+=1)-1!==l[p].href.indexOf(s[i].fPath)&&(f=!1);if(f){var m=a("link");m.setAttribute("f-forigin",s[i].fOrigin),m.setAttribute("f-origin",s[i].origin),m.type="text/css",m.rel="stylesheet",m.href=s[i].fPath,document.body.appendChild(m)}}else if("t"===s[i].fOrigin||2===s[i].origin){for(l=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),p=0;p<l.length;p+=1)s[i].fPath===l[p].src&&(f=!1);if(f){var c=a("link");c.setAttribute("f-forigin",s[i].fOrigin),c.setAttribute("f-origin",s[i].origin),c.setAttribute("rel","stylesheet"),c.setAttribute("href",s[i].fPath),e.appendChild(c)}}}else s[i].loaded=!0,h-=1;s[i].helper=n(s[i],e),s[i].cache={},this.fonts.push(s[i])}0===h?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,i,s){for(var a=0,r=this.chars.length;a<r;){if(this.chars[a].ch===e&&this.chars[a].style===i&&this.chars[a].fFamily===s)return this.chars[a];a+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,i,s)),t},getFontByName:function(t){for(var e=0,i=this.fonts.length;e<i;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,i){var s=this.getFontByName(e),a=t;if(!s.cache[a]){var r=s.helper;if(" "===t){var n=r.measureText("|"+t+"|"),o=r.measureText("||");s.cache[a]=(n-o)/100}else s.cache[a]=r.measureText(t)/100}return s.cache[a]*i},checkLoadedFonts:function(){var t,e,i,s=this.fonts.length,a=s;for(t=0;t<s;t+=1)this.fonts[t].loaded?a-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,i=this.fonts[t].monoCase.w,e.offsetWidth!==i?(a-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,i=this.fonts[t].sansCase.w,e.offsetWidth!==i&&(a-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==a&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}};return l.prototype=p,l}();function Pe(t){this.animationData=t}function Ae(){}Pe.prototype.getProp=function(t){return this.animationData.slots&&this.animationData.slots[t.sid]?Object.assign(t,this.animationData.slots[t.sid].p):t},Ae.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var Se,xe=(Se={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return Se[t]||""});function we(t,e,i){this.p=vt.getProp(e,t.v,0,0,i)}function De(t,e,i){this.p=vt.getProp(e,t.v,0,0,i)}function Ce(t,e,i){this.p=vt.getProp(e,t.v,1,0,i)}function Me(t,e,i){this.p=vt.getProp(e,t.v,1,0,i)}function Te(t,e,i){this.p=vt.getProp(e,t.v,0,0,i)}function Fe(t,e,i){this.p=vt.getProp(e,t.v,0,0,i)}function Ee(t,e,i){this.p=vt.getProp(e,t.v,0,0,i)}function Ie(){this.p={}}function Le(t,e){var i,s=t.ef||[];this.effectElements=[];var a,r=s.length;for(i=0;i<r;i+=1)a=new Ve(s[i],e),this.effectElements.push(a)}function Ve(t,e){this.init(t,e)}function Re(){}function ze(){}function Oe(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,i)}function Ne(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,i),this._isPlaying=!1,this._canPlay=!1;var s=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(s),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?vt.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=vt.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function Be(){}r([bt],Ve),Ve.prototype.getValue=Ve.prototype.iterateDynamicProperties,Ve.prototype.init=function(t,e){var i;this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var s,a=this.data.ef.length,r=this.data.ef;for(i=0;i<a;i+=1){switch(s=null,r[i].ty){case 0:s=new we(r[i],e,this);break;case 1:s=new De(r[i],e,this);break;case 2:s=new Ce(r[i],e,this);break;case 3:s=new Me(r[i],e,this);break;case 4:case 7:s=new Ee(r[i],e,this);break;case 10:s=new Te(r[i],e,this);break;case 11:s=new Fe(r[i],e,this);break;case 5:s=new Le(r[i],e,this);break;default:s=new Ie(r[i],e,this)}s&&this.effectElements.push(s)}},Re.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t=B();if(t){var e=t("layer"),i=t("effects"),s=t("shape"),a=t("text"),r=t("comp");this.layerInterface=e(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var n=i.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(n),0===this.data.ty||this.data.xt?this.compInterface=r(this):4===this.data.ty?(this.layerInterface.shapeInterface=s(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=a(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var t=xe(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,i){this.globalData=e,this.comp=i,this.data=t,this.layerId=I(),this.data.sr||(this.data.sr=1),this.effectsManager=new Le(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},ze.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var i,s=this.dynamicProperties.length;for(i=0;i<s;i+=1)(e||this._isParent&&"transform"===this.dynamicProperties[i].propType)&&(this.dynamicProperties[i].getValue(),this.dynamicProperties[i]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},Oe.prototype.prepareFrame=function(){},r([Ae,Re,ze],Oe),Oe.prototype.getBaseElement=function(){return null},Oe.prototype.renderFrame=function(){},Oe.prototype.destroy=function(){},Oe.prototype.initExpressions=function(){var t=B();if(t){var e=t("footage");this.layerInterface=e(this)}},Oe.prototype.getFootageData=function(){return this.footageData},Ne.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var i=this._volume*this._volumeMultiplier;this._previousVolume!==i&&(this._previousVolume=i,this.audio.volume(i))},r([Ae,Re,ze],Ne),Ne.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},Ne.prototype.show=function(){},Ne.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},Ne.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},Ne.prototype.resume=function(){this._canPlay=!0},Ne.prototype.setRate=function(t){this.audio.rate(t)},Ne.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},Ne.prototype.getBaseElement=function(){return null},Ne.prototype.destroy=function(){},Ne.prototype.sourceRectAtTime=function(){},Ne.prototype.initExpressions=function(){},Be.prototype.checkLayers=function(t){var e,i,s=this.layers.length;for(this.completeLayers=!0,e=s-1;e>=0;e-=1)this.elements[e]||(i=this.layers[e]).ip-i.st<=t-this.layers[e].st&&i.op-i.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},Be.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:default:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t)}},Be.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},Be.prototype.createAudio=function(t){return new Ne(t,this.globalData,this)},Be.prototype.createFootage=function(t){return new Oe(t,this.globalData,this)},Be.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},Be.prototype.includeLayers=function(t){var e;this.completeLayers=!1;var i,s=t.length,a=this.layers.length;for(e=0;e<s;e+=1)for(i=0;i<a;){if(this.layers[i].id===t[e].id){this.layers[i]=t[e];break}i+=1}},Be.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},Be.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},Be.prototype.buildElementParenting=function(t,e,i){for(var s=this.elements,a=this.layers,r=0,n=a.length;r<n;)a[r].ind==e&&(s[r]&&!0!==s[r]?(i.push(s[r]),s[r].setAsParent(),void 0!==a[r].parent?this.buildElementParenting(t,a[r].parent,i):t.setHierarchy(i)):(this.buildItem(r),this.addPendingElement(t))),r+=1},Be.prototype.addPendingElement=function(t){this.pendingElements.push(t)},Be.prototype.searchExtraCompositions=function(t){var e,i=t.length;for(e=0;e<i;e+=1)if(t[e].xt){var s=this.createComp(t[e]);s.initExpressions(),this.globalData.projectInterface.registerComposition(s)}},Be.prototype.getElementById=function(t){var e,i=this.elements.length;for(e=0;e<i;e+=1)if(this.elements[e].data.ind===t)return this.elements[e];return null},Be.prototype.getElementByPath=function(t){var e,i=t.shift();if("number"==typeof i)e=this.elements[i];else{var s,a=this.elements.length;for(s=0;s<a;s+=1)if(this.elements[s].data.nm===i){e=this.elements[s];break}}return 0===t.length?e:e.getElementByPath(t)},Be.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new ke,this.globalData.slotManager=function(t){return new Pe(t)}(t),this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}};var qe="transformEFfect";function je(){}function We(t,e,i){this.data=t,this.element=e,this.globalData=i,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var a,r,n=this.globalData.defs,o=this.masksProperties?this.masksProperties.length:0;this.viewData=l(o),this.solidPath="";var h,p,f,d,m,c,u=this.masksProperties,g=0,y=[],v=I(),b="clipPath",_="clip-path";for(a=0;a<o;a+=1)if(("a"!==u[a].mode&&"n"!==u[a].mode||u[a].inv||100!==u[a].o.k||u[a].o.x)&&(b="mask",_="mask"),"s"!==u[a].mode&&"i"!==u[a].mode||0!==g?f=null:((f=W("rect")).setAttribute("fill","#ffffff"),f.setAttribute("width",this.element.comp.data.w||0),f.setAttribute("height",this.element.comp.data.h||0),y.push(f)),r=W("path"),"n"===u[a].mode)this.viewData[a]={op:vt.getProp(this.element,u[a].o,0,.01,this.element),prop:Tt.getShapeProp(this.element,u[a],3),elem:r,lastPath:""},n.appendChild(r);else{var k;if(g+=1,r.setAttribute("fill","s"===u[a].mode?"#000000":"#ffffff"),r.setAttribute("clip-rule","nonzero"),0!==u[a].x.k?(b="mask",_="mask",c=vt.getProp(this.element,u[a].x,0,null,this.element),k=I(),(d=W("filter")).setAttribute("id",k),(m=W("feMorphology")).setAttribute("operator","erode"),m.setAttribute("in","SourceGraphic"),m.setAttribute("radius","0"),d.appendChild(m),n.appendChild(d),r.setAttribute("stroke","s"===u[a].mode?"#000000":"#ffffff")):(m=null,c=null),this.storedData[a]={elem:r,x:c,expan:m,lastPath:"",lastOperator:"",filterId:k,lastRadius:0},"i"===u[a].mode){p=y.length;var P=W("g");for(h=0;h<p;h+=1)P.appendChild(y[h]);var A=W("mask");A.setAttribute("mask-type","alpha"),A.setAttribute("id",v+"_"+g),A.appendChild(r),n.appendChild(A),P.setAttribute("mask","url("+s()+"#"+v+"_"+g+")"),y.length=0,y.push(P)}else y.push(r);u[a].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[a]={elem:r,lastPath:"",op:vt.getProp(this.element,u[a].o,0,.01,this.element),prop:Tt.getShapeProp(this.element,u[a],3),invRect:f},this.viewData[a].prop.k||this.drawPath(u[a],this.viewData[a].prop.v,this.viewData[a])}for(this.maskElement=W(b),o=y.length,a=0;a<o;a+=1)this.maskElement.appendChild(y[a]);g>0&&(this.maskElement.setAttribute("id",v),this.element.maskedElement.setAttribute(_,"url("+s()+"#"+v+")"),n.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}je.prototype={initTransform:function(){var t=new Ft;this.finalTransform={mProp:this.data.ks?Wt.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:t,localMat:t,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,i=0,s=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<s;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),i=0;i<s;i+=1)e.multiply(this.hierarchy[i].finalTransform.mProp.v)}this.finalTransform._matMdf&&(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var t=0,e=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;t<e;)this.localTransforms[t]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[t]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),t+=1;if(this.finalTransform._localMatMdf){var i=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(i),t=1;t<e;t+=1){var s=this.localTransforms[t].matrix;i.multiply(s)}i.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){var a=this.finalTransform.localOpacity;for(t=0;t<e;t+=1)a*=.01*this.localTransforms[t].opacity;this.finalTransform.localOpacity=a}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var t=this.renderableEffectsManager.getEffects(qe);if(t.length){this.localTransforms=[],this.finalTransform.localMat=new Ft;var e=0,i=t.length;for(e=0;e<i;e+=1)this.localTransforms.push(t[e])}}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var i,s=!0,a=this.comp;s;)a.finalTransform?(a.data.hasMask&&e.splice(0,0,a.finalTransform),a=a.comp):s=!1;var r,n=e.length;for(i=0;i<n;i+=1)r=e[i].mat.applyToPointArray(0,0,0),t=[t[0]-r[0],t[1]-r[1],0];return t},mHelper:new Ft},We.prototype.getMaskProperty=function(t){return this.viewData[t].prop},We.prototype.renderFrame=function(t){var e,i=this.element.finalTransform.mat,a=this.masksProperties.length;for(e=0;e<a;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",i.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var r=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+s()+"#"+this.storedData[e].filterId+")")),r.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},We.prototype.getMaskelement=function(){return this.maskElement},We.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,t+=" h-"+this.globalData.compSize.w,t+=" v-"+this.globalData.compSize.h+" "},We.prototype.drawPath=function(t,e,i){var s,a,r=" M"+e.v[0][0]+","+e.v[0][1];for(a=e._length,s=1;s<a;s+=1)r+=" C"+e.o[s-1][0]+","+e.o[s-1][1]+" "+e.i[s][0]+","+e.i[s][1]+" "+e.v[s][0]+","+e.v[s][1];if(e.c&&a>1&&(r+=" C"+e.o[s-1][0]+","+e.o[s-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),i.lastPath!==r){var n="";i.elem&&(e.c&&(n=t.inv?this.solidPath+r:r),i.elem.setAttribute("d",n)),i.lastPath=r}},We.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var Xe=function(){var t={};return t.createFilter=function(t,e){var i=W("filter");i.setAttribute("id",t),!0!==e&&(i.setAttribute("filterUnits","objectBoundingBox"),i.setAttribute("x","0%"),i.setAttribute("y","0%"),i.setAttribute("width","100%"),i.setAttribute("height","100%"));return i},t.createAlphaToLuminanceFilter=function(){var t=W("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t},t}(),He=function(){var t={maskType:!0,svgLumaHidden:!0,offscreenCanvas:"undefined"!=typeof OffscreenCanvas};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),/firefox/i.test(navigator.userAgent)&&(t.svgLumaHidden=!1),t}(),Ye={},Ge="filter_result_";function Ke(t){var e,i,a="SourceGraphic",r=t.data.ef?t.data.ef.length:0,n=I(),o=Xe.createFilter(n,!0),h=0;for(this.filters=[],e=0;e<r;e+=1){i=null;var l=t.data.ef[e].ty;if(Ye[l])i=new(0,Ye[l].effect)(o,t.effectsManager.effectElements[e],t,Ge+h,a),a=Ge+h,Ye[l].countsAsEffect&&(h+=1);i&&this.filters.push(i)}h&&(t.globalData.defs.appendChild(o),t.layerElement.setAttribute("filter","url("+s()+"#"+n+")")),this.filters.length&&t.addRenderableComponent(this)}function Je(){}function Ue(){}function Ze(){}function Qe(t,e,i){this.assetData=e.getAssetData(t.refId),this.assetData&&this.assetData.sid&&(this.assetData=e.slotManager.getProp(this.assetData)),this.initElement(t,e,i),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function $e(t,e){this.elem=t,this.pos=e}function ti(){}Ke.prototype.renderFrame=function(t){var e,i=this.filters.length;for(e=0;e<i;e+=1)this.filters[e].renderFrame(t)},Ke.prototype.getEffects=function(t){var e,i=this.filters.length,s=[];for(e=0;e<i;e+=1)this.filters[e].type===t&&s.push(this.filters[e]);return s},Je.prototype={initRendererElement:function(){this.layerElement=W("g")},createContainerElements:function(){this.matteElement=W("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null;if(this.data.td){this.matteMasks={};var e=W("g");e.setAttribute("id",this.layerId),e.appendChild(this.layerElement),t=e,this.globalData.defs.appendChild(e)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var i=W("clipPath"),a=W("path");a.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var r=I();if(i.setAttribute("id",r),i.appendChild(a),this.globalData.defs.appendChild(i),this.checkMasks()){var n=W("g");n.setAttribute("clip-path","url("+s()+"#"+r+")"),n.appendChild(this.layerElement),this.transformedElement=n,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+s()+"#"+r+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new We(this.data,this,this.globalData),this.renderableEffectsManager=new Ke(this),this.searchEffectTransforms()},getMatte:function(t){if(this.matteMasks||(this.matteMasks={}),!this.matteMasks[t]){var e,i,a,r,n=this.layerId+"_"+t;if(1===t||3===t){var o=W("mask");o.setAttribute("id",n),o.setAttribute("mask-type",3===t?"luminance":"alpha"),(a=W("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),o.appendChild(a),this.globalData.defs.appendChild(o),He.maskType||1!==t||(o.setAttribute("mask-type","luminance"),e=I(),i=Xe.createFilter(e),this.globalData.defs.appendChild(i),i.appendChild(Xe.createAlphaToLuminanceFilter()),(r=W("g")).appendChild(a),o.appendChild(r),r.setAttribute("filter","url("+s()+"#"+e+")"))}else if(2===t){var h=W("mask");h.setAttribute("id",n),h.setAttribute("mask-type","alpha");var l=W("g");h.appendChild(l),e=I(),i=Xe.createFilter(e);var p=W("feComponentTransfer");p.setAttribute("in","SourceGraphic"),i.appendChild(p);var f=W("feFuncA");f.setAttribute("type","table"),f.setAttribute("tableValues","1.0 0.0"),p.appendChild(f),this.globalData.defs.appendChild(i);var d=W("rect");d.setAttribute("width",this.comp.data.w),d.setAttribute("height",this.comp.data.h),d.setAttribute("x","0"),d.setAttribute("y","0"),d.setAttribute("fill","#ffffff"),d.setAttribute("opacity","0"),l.setAttribute("filter","url("+s()+"#"+e+")"),l.appendChild(d),(a=W("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),l.appendChild(a),He.maskType||(h.setAttribute("mask-type","luminance"),i.appendChild(Xe.createAlphaToLuminanceFilter()),r=W("g"),l.appendChild(d),r.appendChild(this.layerElement),l.appendChild(r)),this.globalData.defs.appendChild(h)}this.matteMasks[t]=n}return this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+s()+"#"+t+")")}},Ue.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},r([Ae,n({initElement:function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],Ze),r([Re,je,Je,Ue,ze,Ze],Qe),Qe.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=W("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},Qe.prototype.sourceRectAtTime=function(){return this.sourceRect},ti.prototype={addShapeToModifiers:function(t){var e,i=this.shapeModifiers.length;for(e=0;e<i;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,i=0,s=e.length;i<s;){if(e[i].elem===t)return e[i].pos;i+=1}return 0},addProcessedElement:function(t,e){for(var i=this.processedElements,s=i.length;s;)if(i[s-=1].elem===t)return void(i[s].pos=e);i.push(new $e(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var ei={1:"butt",2:"round",3:"square"},ii={1:"miter",2:"round",3:"bevel"};function si(t,e,i){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=i,this.lvl=e,this._isAnimated=!!i.k;for(var s=0,a=t.length;s<a;){if(t[s].mProps.dynamicProperties.length){this._isAnimated=!0;break}s+=1}}function ai(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=W("path"),this.msElem=null}function ri(t,e,i,s){var a;this.elem=t,this.frameId=-1,this.dataProps=l(e.length),this.renderer=i,this.k=!1,this.dashStr="",this.dashArray=h("float32",e.length?e.length-1:0),this.dashoffset=h("float32",1),this.initDynamicPropertyContainer(s);var r,n=e.length||0;for(a=0;a<n;a+=1)r=vt.getProp(t,e[a].v,0,0,this),this.k=r.k||this.k,this.dataProps[a]={n:e[a].n,p:r};this.k||this.getValue(!0),this._isAnimated=this.k}function ni(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=vt.getProp(t,e.o,0,.01,this),this.w=vt.getProp(t,e.w,0,null,this),this.d=new ri(t,e.d||{},"svg",this),this.c=vt.getProp(t,e.c,1,255,this),this.style=i,this._isAnimated=!!this._isAnimated}function oi(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=vt.getProp(t,e.o,0,.01,this),this.c=vt.getProp(t,e.c,1,255,this),this.style=i}function hi(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=i}function li(t,e,i){this.data=e,this.c=h("uint8c",4*e.p);var s=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=h("float32",s),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=s,this.initDynamicPropertyContainer(i),this.prop=vt.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function pi(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,i)}function fi(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=vt.getProp(t,e.w,0,null,this),this.d=new ri(t,e.d||{},"svg",this),this.initGradientData(t,e,i),this._isAnimated=!!this._isAnimated}function di(){this.it=[],this.prevViewData=[],this.gr=W("g")}function mi(t,e,i){this.transform={mProps:t,op:e,container:i},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}si.prototype.setAsAnimated=function(){this._isAnimated=!0},ai.prototype.reset=function(){this.d="",this._mdf=!1},ri.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,i=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<i;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},r([bt],ri),r([bt],ni),r([bt],oi),r([bt],hi),li.prototype.comparePoints=function(t,e){for(var i=0,s=this.o.length/2;i<s;){if(Math.abs(t[4*i]-t[4*e+2*i])>.01)return!1;i+=1}return!0},li.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},li.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,i,s,a=4*this.data.p;for(e=0;e<a;e+=1)i=e%4==0?100:255,s=Math.round(this.prop.v[e]*i),this.c[e]!==s&&(this.c[e]=s,this._cmdf=!t);if(this.o.length)for(a=this.prop.v.length,e=4*this.data.p;e<a;e+=1)i=e%2==0?100:1,s=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==s&&(this.o[e-4*this.data.p]=s,this._omdf=!t);this._mdf=!t}},r([bt],li),pi.prototype.initGradientData=function(t,e,i){this.o=vt.getProp(t,e.o,0,.01,this),this.s=vt.getProp(t,e.s,1,null,this),this.e=vt.getProp(t,e.e,1,null,this),this.h=vt.getProp(t,e.h||{k:0},0,.01,this),this.a=vt.getProp(t,e.a||{k:0},0,P,this),this.g=new li(t,e.g,this),this.style=i,this.stops=[],this.setGradientData(i.pElem,e),this.setGradientOpacity(e,i),this._isAnimated=!!this._isAnimated},pi.prototype.setGradientData=function(t,e){var i=I(),a=W(1===e.t?"linearGradient":"radialGradient");a.setAttribute("id",i),a.setAttribute("spreadMethod","pad"),a.setAttribute("gradientUnits","userSpaceOnUse");var r,n,o,h=[];for(o=4*e.g.p,n=0;n<o;n+=4)r=W("stop"),a.appendChild(r),h.push(r);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+s()+"#"+i+")"),this.gf=a,this.cst=h},pi.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var i,a,r,n=W("mask"),o=W("path");n.appendChild(o);var h=I(),l=I();n.setAttribute("id",l);var p=W(1===t.t?"linearGradient":"radialGradient");p.setAttribute("id",h),p.setAttribute("spreadMethod","pad"),p.setAttribute("gradientUnits","userSpaceOnUse"),r=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var f=this.stops;for(a=4*t.g.p;a<r;a+=2)(i=W("stop")).setAttribute("stop-color","rgb(255,255,255)"),p.appendChild(i),f.push(i);o.setAttribute("gf"===t.ty?"fill":"stroke","url("+s()+"#"+h+")"),"gs"===t.ty&&(o.setAttribute("stroke-linecap",ei[t.lc||2]),o.setAttribute("stroke-linejoin",ii[t.lj||2]),1===t.lj&&o.setAttribute("stroke-miterlimit",t.ml)),this.of=p,this.ms=n,this.ost=f,this.maskId=l,e.msElem=o}},r([bt],pi),r([pi,bt],fi);var ci=function(t,e,i,s){if(0===e)return"";var a,r=t.o,n=t.i,o=t.v,h=" M"+s.applyToPointStringified(o[0][0],o[0][1]);for(a=1;a<e;a+=1)h+=" C"+s.applyToPointStringified(r[a-1][0],r[a-1][1])+" "+s.applyToPointStringified(n[a][0],n[a][1])+" "+s.applyToPointStringified(o[a][0],o[a][1]);return i&&e&&(h+=" C"+s.applyToPointStringified(r[a-1][0],r[a-1][1])+" "+s.applyToPointStringified(n[0][0],n[0][1])+" "+s.applyToPointStringified(o[0][0],o[0][1]),h+="z"),h},ui=function(){var t=new Ft,e=new Ft;function i(t,e,i){(i||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(i||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function s(){}function a(i,s,a){var r,n,o,h,l,p,f,d,m,c,u=s.styles.length,g=s.lvl;for(p=0;p<u;p+=1){if(h=s.sh._mdf||a,s.styles[p].lvl<g){for(d=e.reset(),m=g-s.styles[p].lvl,c=s.transformers.length-1;!h&&m>0;)h=s.transformers[c].mProps._mdf||h,m-=1,c-=1;if(h)for(m=g-s.styles[p].lvl,c=s.transformers.length-1;m>0;)d.multiply(s.transformers[c].mProps.v),m-=1,c-=1}else d=t;if(n=(f=s.sh.paths)._length,h){for(o="",r=0;r<n;r+=1)(l=f.shapes[r])&&l._length&&(o+=ci(l,l._length,l.c,d));s.caches[p]=o}else o=s.caches[p];s.styles[p].d+=!0===i.hd?"":o,s.styles[p]._mdf=h||s.styles[p]._mdf}}function r(t,e,i){var s=e.style;(e.c._mdf||i)&&s.pElem.setAttribute("fill","rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||i)&&s.pElem.setAttribute("fill-opacity",e.o.v)}function n(t,e,i){o(t,e,i),h(t,e,i)}function o(t,e,i){var s,a,r,n,o,h=e.gf,l=e.g._hasOpacity,p=e.s.v,f=e.e.v;if(e.o._mdf||i){var d="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(d,e.o.v)}if(e.s._mdf||i){var m=1===t.t?"x1":"cx",c="x1"===m?"y1":"cy";h.setAttribute(m,p[0]),h.setAttribute(c,p[1]),l&&!e.g._collapsable&&(e.of.setAttribute(m,p[0]),e.of.setAttribute(c,p[1]))}if(e.g._cmdf||i){s=e.cst;var u=e.g.c;for(r=s.length,a=0;a<r;a+=1)(n=s[a]).setAttribute("offset",u[4*a]+"%"),n.setAttribute("stop-color","rgb("+u[4*a+1]+","+u[4*a+2]+","+u[4*a+3]+")")}if(l&&(e.g._omdf||i)){var g=e.g.o;for(r=(s=e.g._collapsable?e.cst:e.ost).length,a=0;a<r;a+=1)n=s[a],e.g._collapsable||n.setAttribute("offset",g[2*a]+"%"),n.setAttribute("stop-opacity",g[2*a+1])}if(1===t.t)(e.e._mdf||i)&&(h.setAttribute("x2",f[0]),h.setAttribute("y2",f[1]),l&&!e.g._collapsable&&(e.of.setAttribute("x2",f[0]),e.of.setAttribute("y2",f[1])));else if((e.s._mdf||e.e._mdf||i)&&(o=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)),h.setAttribute("r",o),l&&!e.g._collapsable&&e.of.setAttribute("r",o)),e.e._mdf||e.h._mdf||e.a._mdf||i){o||(o=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)));var y=Math.atan2(f[1]-p[1],f[0]-p[0]),v=e.h.v;v>=1?v=.99:v<=-1&&(v=-.99);var b=o*v,_=Math.cos(y+e.a.v)*b+p[0],k=Math.sin(y+e.a.v)*b+p[1];h.setAttribute("fx",_),h.setAttribute("fy",k),l&&!e.g._collapsable&&(e.of.setAttribute("fx",_),e.of.setAttribute("fy",k))}}function h(t,e,i){var s=e.style,a=e.d;a&&(a._mdf||i)&&a.dashStr&&(s.pElem.setAttribute("stroke-dasharray",a.dashStr),s.pElem.setAttribute("stroke-dashoffset",a.dashoffset[0])),e.c&&(e.c._mdf||i)&&s.pElem.setAttribute("stroke","rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||i)&&s.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||i)&&(s.pElem.setAttribute("stroke-width",e.w.v),s.msElem&&s.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return r;case"gf":return o;case"gs":return n;case"st":return h;case"sh":case"el":case"rc":case"sr":return a;case"tr":return i;case"no":return s;default:return null}}}}();function gi(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,i),this.prevViewData=[]}function yi(t,e,i,s,a,r){this.o=t,this.sw=e,this.sc=i,this.fc=s,this.m=a,this.p=r,this._mdf={o:!0,sw:!!e,sc:!!i,fc:!!s,m:!0,p:!0}}function vi(t,e){this._frameId=i,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,e.d&&e.d.sid&&(e.d=t.globalData.slotManager.getProp(e.d)),this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}r([Re,je,Je,ti,Ue,ze,Ze],gi),gi.prototype.initSecondaryElement=function(){},gi.prototype.identityMatrix=new Ft,gi.prototype.buildExpressionInterface=function(){},gi.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},gi.prototype.filterUniqueShapes=function(){var t,e,i,s,a=this.shapes.length,r=this.stylesList.length,n=[],o=!1;for(i=0;i<r;i+=1){for(s=this.stylesList[i],o=!1,n.length=0,t=0;t<a;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(s)&&(n.push(e),o=e._isAnimated||o);n.length>1&&o&&this.setShapesAsAnimated(n)}},gi.prototype.setShapesAsAnimated=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e].setAsAnimated()},gi.prototype.createStyleElement=function(t,e){var i,a=new ai(t,e),r=a.pElem;if("st"===t.ty)i=new ni(this,t,a);else if("fl"===t.ty)i=new oi(this,t,a);else if("gf"===t.ty||"gs"===t.ty){i=new("gf"===t.ty?pi:fi)(this,t,a),this.globalData.defs.appendChild(i.gf),i.maskId&&(this.globalData.defs.appendChild(i.ms),this.globalData.defs.appendChild(i.of),r.setAttribute("mask","url("+s()+"#"+i.maskId+")"))}else"no"===t.ty&&(i=new hi(this,t,a));return"st"!==t.ty&&"gs"!==t.ty||(r.setAttribute("stroke-linecap",ei[t.lc||2]),r.setAttribute("stroke-linejoin",ii[t.lj||2]),r.setAttribute("fill-opacity","0"),1===t.lj&&r.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&r.setAttribute("fill-rule","evenodd"),t.ln&&r.setAttribute("id",t.ln),t.cl&&r.setAttribute("class",t.cl),t.bm&&(r.style["mix-blend-mode"]=xe(t.bm)),this.stylesList.push(a),this.addToAnimatedContents(t,i),i},gi.prototype.createGroupElement=function(t){var e=new di;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=xe(t.bm)),e},gi.prototype.createTransformElement=function(t,e){var i=Wt.getTransformProperty(this,t,this),s=new mi(i,i.o,e);return this.addToAnimatedContents(t,s),s},gi.prototype.createShapeElement=function(t,e,i){var s=4;"rc"===t.ty?s=5:"el"===t.ty?s=6:"sr"===t.ty&&(s=7);var a=new si(e,i,Tt.getShapeProp(this,t,s,this));return this.shapes.push(a),this.addShapeToModifiers(a),this.addToAnimatedContents(t,a),a},gi.prototype.addToAnimatedContents=function(t,e){for(var i=0,s=this.animatedContents.length;i<s;){if(this.animatedContents[i].element===e)return;i+=1}this.animatedContents.push({fn:ui.createRenderFunction(t),element:e,data:t})},gi.prototype.setElementStyles=function(t){var e,i=t.styles,s=this.stylesList.length;for(e=0;e<s;e+=1)this.stylesList[e].closed||i.push(this.stylesList[e])},gi.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},gi.prototype.searchShapes=function(t,e,i,s,a,r,n){var o,h,l,p,f,d,m=[].concat(r),c=t.length-1,u=[],g=[];for(o=c;o>=0;o-=1){if((d=this.searchProcessedElement(t[o]))?e[o]=i[d-1]:t[o]._render=n,"fl"===t[o].ty||"st"===t[o].ty||"gf"===t[o].ty||"gs"===t[o].ty||"no"===t[o].ty)d?e[o].style.closed=!1:e[o]=this.createStyleElement(t[o],a),t[o]._render&&e[o].style.pElem.parentNode!==s&&s.appendChild(e[o].style.pElem),u.push(e[o].style);else if("gr"===t[o].ty){if(d)for(l=e[o].it.length,h=0;h<l;h+=1)e[o].prevViewData[h]=e[o].it[h];else e[o]=this.createGroupElement(t[o]);this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,a+1,m,n),t[o]._render&&e[o].gr.parentNode!==s&&s.appendChild(e[o].gr)}else"tr"===t[o].ty?(d||(e[o]=this.createTransformElement(t[o],s)),p=e[o].transform,m.push(p)):"sh"===t[o].ty||"rc"===t[o].ty||"el"===t[o].ty||"sr"===t[o].ty?(d||(e[o]=this.createShapeElement(t[o],m,a)),this.setElementStyles(e[o])):"tm"===t[o].ty||"rd"===t[o].ty||"ms"===t[o].ty||"pb"===t[o].ty||"zz"===t[o].ty||"op"===t[o].ty?(d?(f=e[o]).closed=!1:((f=Nt.getModifier(t[o].ty)).init(this,t[o]),e[o]=f,this.shapeModifiers.push(f)),g.push(f)):"rp"===t[o].ty&&(d?(f=e[o]).closed=!0:(f=Nt.getModifier(t[o].ty),e[o]=f,f.init(this,t,o,e),this.shapeModifiers.push(f),n=!1),g.push(f));this.addProcessedElement(t[o],o+1)}for(c=u.length,o=0;o<c;o+=1)u[o].closed=!0;for(c=g.length,o=0;o<c;o+=1)g[o].closed=!0},gi.prototype.renderInnerContent=function(){var t;this.renderModifiers();var e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},gi.prototype.renderShape=function(){var t,e,i=this.animatedContents.length;for(t=0;t<i;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},gi.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},yi.prototype.update=function(t,e,i,s,a,r){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var n=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,n=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,n=!0),this.sc!==i&&(this.sc=i,this._mdf.sc=!0,n=!0),this.fc!==s&&(this.fc=s,this._mdf.fc=!0,n=!0),this.m!==a&&(this.m=a,this._mdf.m=!0,n=!0),!r.length||this.p[0]===r[0]&&this.p[1]===r[1]&&this.p[4]===r[4]&&this.p[5]===r[5]&&this.p[12]===r[12]&&this.p[13]===r[13]||(this.p=r,this._mdf.p=!0,n=!0),n},vi.prototype.defaultBoxWidth=[0,0],vi.prototype.copyData=function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},vi.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},vi.prototype.searchProperty=function(){return this.searchKeyframes()},vi.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},vi.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},vi.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,i=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{var s;this.lock=!0,this._mdf=!1;var a=this.effectsSequence.length,r=t||this.data.d.k[this.keysIndex].s;for(s=0;s<a;s+=1)r=i!==this.keysIndex?this.effectsSequence[s](r,r.t):this.effectsSequence[s](this.currentData,r.t);e!==r&&this.setCurrentData(r),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},vi.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,i=0,s=t.length;i<=s-1&&!(i===s-1||t[i+1].t>e);)i+=1;return this.keysIndex!==i&&(this.keysIndex=i),this.data.d.k[this.keysIndex].s},vi.prototype.buildFinalText=function(t){for(var e,i,s=[],a=0,r=t.length,n=!1,o=!1,h="";a<r;)n=o,o=!1,e=t.charCodeAt(a),h=t.charAt(a),ke.isCombinedCharacter(e)?n=!0:e>=55296&&e<=56319?ke.isRegionalFlag(t,a)?h=t.substr(a,14):(i=t.charCodeAt(a+1))>=56320&&i<=57343&&(ke.isModifier(e,i)?(h=t.substr(a,2),n=!0):h=ke.isFlagEmoji(t.substr(a,4))?t.substr(a,4):t.substr(a,2)):e>56319?(i=t.charCodeAt(a+1),ke.isVariationSelector(e)&&(n=!0)):ke.isZeroWidthJoiner(e)&&(n=!0,o=!0),n?(s[s.length-1]+=h,n=!1):s.push(h),a+=h.length;return s},vi.prototype.completeTextData=function(t){t.__complete=!0;var e,i,s,a,r,n,o,h=this.elem.globalData.fontManager,l=this.data,p=[],f=0,d=l.m.g,m=0,c=0,u=0,g=[],y=0,v=0,b=h.getFontByName(t.f),_=0,k=_e(b);t.fWeight=k.weight,t.fStyle=k.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),i=t.finalText.length,t.finalLineHeight=t.lh;var P,A=t.tr/1e3*t.finalSize;if(t.sz)for(var S,x,w=!0,D=t.sz[0],C=t.sz[1];w;){S=0,y=0,i=(x=this.buildFinalText(t.t)).length,A=t.tr/1e3*t.finalSize;var M=-1;for(e=0;e<i;e+=1)P=x[e].charCodeAt(0),s=!1," "===x[e]?M=e:13!==P&&3!==P||(y=0,s=!0,S+=t.finalLineHeight||1.2*t.finalSize),h.chars?(o=h.getCharData(x[e],b.fStyle,b.fFamily),_=s?0:o.w*t.finalSize/100):_=h.measureText(x[e],t.f,t.finalSize),y+_>D&&" "!==x[e]?(-1===M?i+=1:e=M,S+=t.finalLineHeight||1.2*t.finalSize,x.splice(e,M===e?1:0,"\r"),M=-1,y=0):(y+=_,y+=A);S+=b.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&C<S?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=x,i=t.finalText.length,w=!1)}y=-A,_=0;var T,F=0;for(e=0;e<i;e+=1)if(s=!1,13===(P=(T=t.finalText[e]).charCodeAt(0))||3===P?(F=0,g.push(y),v=y>v?y:v,y=-2*A,a="",s=!0,u+=1):a=T,h.chars?(o=h.getCharData(T,b.fStyle,h.getFontByName(t.f).fFamily),_=s?0:o.w*t.finalSize/100):_=h.measureText(a,t.f,t.finalSize)," "===T?F+=_+A:(y+=_+A+F,F=0),p.push({l:_,an:_,add:m,n:s,anIndexes:[],val:a,line:u,animatorJustifyOffset:0}),2==d){if(m+=_,""===a||" "===a||e===i-1){for(""!==a&&" "!==a||(m-=_);c<=e;)p[c].an=m,p[c].ind=f,p[c].extra=_,c+=1;f+=1,m=0}}else if(3==d){if(m+=_,""===a||e===i-1){for(""===a&&(m-=_);c<=e;)p[c].an=m,p[c].ind=f,p[c].extra=_,c+=1;m=0,f+=1}}else p[f].ind=f,p[f].extra=0,f+=1;if(t.l=p,v=y>v?y:v,g.push(y),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=v,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=g;var E,I,L,V,R=l.a;n=R.length;var z=[];for(r=0;r<n;r+=1){for((E=R[r]).a.sc&&(t.strokeColorAnim=!0),E.a.sw&&(t.strokeWidthAnim=!0),(E.a.fc||E.a.fh||E.a.fs||E.a.fb)&&(t.fillColorAnim=!0),V=0,L=E.s.b,e=0;e<i;e+=1)(I=p[e]).anIndexes[r]=V,(1==L&&""!==I.val||2==L&&""!==I.val&&" "!==I.val||3==L&&(I.n||" "==I.val||e==i-1)||4==L&&(I.n||e==i-1))&&(1===E.s.rn&&z.push(V),V+=1);l.a[r].s.totalChars=V;var O,N=-1;if(1===E.s.rn)for(e=0;e<i;e+=1)N!=(I=p[e]).anIndexes[r]&&(N=I.anIndexes[r],O=z.splice(Math.floor(Math.random()*z.length),1)[0]),I.anIndexes[r]=O}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=b.ascent*t.finalSize/100},vi.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var i=this.copyData({},this.data.d.k[e].s);i=this.copyData(i,t),this.data.d.k[e].s=i,this.recalculate(e),this.setCurrentData(i),this.elem.addDynamicProperty(this)},vi.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},vi.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},vi.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var bi=function(){var t=Math.max,e=Math.min,i=Math.floor;function s(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=vt.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?vt.getProp(t,e.e,0,0,this):{v:100},this.o=vt.getProp(t,e.o||{k:0},0,0,this),this.xe=vt.getProp(t,e.xe||{k:0},0,0,this),this.ne=vt.getProp(t,e.ne||{k:0},0,0,this),this.sm=vt.getProp(t,e.sm||{k:100},0,0,this),this.a=vt.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return s.prototype={getMult:function(s){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var a=0,r=0,n=1,o=1;this.ne.v>0?a=this.ne.v/100:r=-this.ne.v/100,this.xe.v>0?n=1-this.xe.v/100:o=1+this.xe.v/100;var h=tt.getBezierEasing(a,r,n,o).get,l=0,p=this.finalS,f=this.finalE,d=this.data.sh;if(2===d)l=h(l=f===p?s>=f?1:0:t(0,e(.5/(f-p)+(s-p)/(f-p),1)));else if(3===d)l=h(l=f===p?s>=f?0:1:1-t(0,e(.5/(f-p)+(s-p)/(f-p),1)));else if(4===d)f===p?l=0:(l=t(0,e(.5/(f-p)+(s-p)/(f-p),1)))<.5?l*=2:l=1-2*(l-.5),l=h(l);else if(5===d){if(f===p)l=0;else{var m=f-p,c=-m/2+(s=e(t(0,s+.5-p),f-p)),u=m/2;l=Math.sqrt(1-c*c/(u*u))}l=h(l)}else 6===d?(f===p?l=0:(s=e(t(0,s+.5-p),f-p),l=(1+Math.cos(Math.PI+2*Math.PI*s/(f-p)))/2),l=h(l)):(s>=i(p)&&(l=t(0,e(s-p<0?e(f,1)-(p-s):f-s,1))),l=h(l));if(100!==this.sm.v){var g=.01*this.sm.v;0===g&&(g=1e-8);var y=.5-.5*g;l<y?l=0:(l=(l-y)/g)>1&&(l=1)}return l*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,i=this.o.v/e,s=this.s.v/e+i,a=this.e.v/e+i;if(s>a){var r=s;s=a,a=r}this.finalS=s,this.finalE=a}},r([bt],s),{getTextSelectorProp:function(t,e,i){return new s(t,e,i)}}}();function _i(t,e,i){var s={propType:!1},a=vt.getProp,r=e.a;this.a={r:r.r?a(t,r.r,0,P,i):s,rx:r.rx?a(t,r.rx,0,P,i):s,ry:r.ry?a(t,r.ry,0,P,i):s,sk:r.sk?a(t,r.sk,0,P,i):s,sa:r.sa?a(t,r.sa,0,P,i):s,s:r.s?a(t,r.s,1,.01,i):s,a:r.a?a(t,r.a,1,0,i):s,o:r.o?a(t,r.o,0,.01,i):s,p:r.p?a(t,r.p,1,0,i):s,sw:r.sw?a(t,r.sw,0,0,i):s,sc:r.sc?a(t,r.sc,1,0,i):s,fc:r.fc?a(t,r.fc,1,0,i):s,fh:r.fh?a(t,r.fh,0,0,i):s,fs:r.fs?a(t,r.fs,0,.01,i):s,fb:r.fb?a(t,r.fb,0,.01,i):s,t:r.t?a(t,r.t,0,0,i):s},this.s=bi.getTextSelectorProp(t,e.s,i),this.s.t=e.s.t}function ki(t,e,i){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=i,this._animatorsData=l(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(i)}function Pi(){}ki.prototype.searchProperties=function(){var t,e,i=this._textData.a.length,s=vt.getProp;for(t=0;t<i;t+=1)e=this._textData.a[t],this._animatorsData[t]=new _i(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:s(this._elem,this._textData.p.a,0,0,this),f:s(this._elem,this._textData.p.f,0,0,this),l:s(this._elem,this._textData.p.l,0,0,this),r:s(this._elem,this._textData.p.r,0,0,this),p:s(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=s(this._elem,this._textData.m.a,1,0,this)},ki.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var i,s,a,r,n,o,h,l,p,f,d,m,c,u,g,y,v,b,_,k=this._moreOptions.alignment.v,P=this._animatorsData,A=this._textData,S=this.mHelper,x=this._renderType,w=this.renderedLetters.length,D=t.l;if(this._hasMaskedPath){if(_=this._pathData.m,!this._pathData.n||this._pathData._mdf){var C,M=_.v;for(this._pathData.r.v&&(M=M.reverse()),n={tLength:0,segments:[]},r=M._length-1,y=0,a=0;a<r;a+=1)C=rt.buildBezierData(M.v[a],M.v[a+1],[M.o[a][0]-M.v[a][0],M.o[a][1]-M.v[a][1]],[M.i[a+1][0]-M.v[a+1][0],M.i[a+1][1]-M.v[a+1][1]]),n.tLength+=C.segmentLength,n.segments.push(C),y+=C.segmentLength;a=r,_.v.c&&(C=rt.buildBezierData(M.v[a],M.v[0],[M.o[a][0]-M.v[a][0],M.o[a][1]-M.v[a][1]],[M.i[0][0]-M.v[0][0],M.i[0][1]-M.v[0][1]]),n.tLength+=C.segmentLength,n.segments.push(C),y+=C.segmentLength),this._pathData.pi=n}if(n=this._pathData.pi,o=this._pathData.f.v,d=0,f=1,l=0,p=!0,u=n.segments,o<0&&_.v.c)for(n.tLength<Math.abs(o)&&(o=-Math.abs(o)%n.tLength),f=(c=u[d=u.length-1].points).length-1;o<0;)o+=c[f].partialLength,(f-=1)<0&&(f=(c=u[d-=1].points).length-1);m=(c=u[d].points)[f-1],g=(h=c[f]).partialLength}r=D.length,i=0,s=0;var T,F,E,I,L,V=1.2*t.finalSize*.714,N=!0;E=P.length;var B,q,j,W,X,H,Y,G,K,J,U,Z,Q=-1,$=o,tt=d,et=f,it=-1,st="",at=this.defaultPropsArray;if(2===t.j||1===t.j){var nt=0,ot=0,ht=2===t.j?-.5:-1,lt=0,pt=!0;for(a=0;a<r;a+=1)if(D[a].n){for(nt&&(nt+=ot);lt<a;)D[lt].animatorJustifyOffset=nt,lt+=1;nt=0,pt=!0}else{for(F=0;F<E;F+=1)(T=P[F].a).t.propType&&(pt&&2===t.j&&(ot+=T.t.v*ht),(L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars)).length?nt+=T.t.v*L[0]*ht:nt+=T.t.v*L*ht);pt=!1}for(nt&&(nt+=ot);lt<a;)D[lt].animatorJustifyOffset=nt,lt+=1}for(a=0;a<r;a+=1){if(S.reset(),W=1,D[a].n)i=0,s+=t.yOffset,s+=N?1:0,o=$,N=!1,this._hasMaskedPath&&(f=et,m=(c=u[d=tt].points)[f-1],g=(h=c[f]).partialLength,l=0),st="",U="",K="",Z="",at=this.defaultPropsArray;else{if(this._hasMaskedPath){if(it!==D[a].line){switch(t.j){case 1:o+=y-t.lineWidths[D[a].line];break;case 2:o+=(y-t.lineWidths[D[a].line])/2}it=D[a].line}Q!==D[a].ind&&(D[Q]&&(o+=D[Q].extra),o+=D[a].an/2,Q=D[a].ind),o+=k[0]*D[a].an*.005;var ft=0;for(F=0;F<E;F+=1)(T=P[F].a).p.propType&&((L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars)).length?ft+=T.p.v[0]*L[0]:ft+=T.p.v[0]*L),T.a.propType&&((L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars)).length?ft+=T.a.v[0]*L[0]:ft+=T.a.v[0]*L);for(p=!0,this._pathData.a.v&&(o=.5*D[0].an+(y-this._pathData.f.v-.5*D[0].an-.5*D[D.length-1].an)*Q/(r-1),o+=this._pathData.f.v);p;)l+g>=o+ft||!c?(v=(o+ft-l)/h.partialLength,q=m.point[0]+(h.point[0]-m.point[0])*v,j=m.point[1]+(h.point[1]-m.point[1])*v,S.translate(-k[0]*D[a].an*.005,-k[1]*V*.01),p=!1):c&&(l+=h.partialLength,(f+=1)>=c.length&&(f=0,u[d+=1]?c=u[d].points:_.v.c?(f=0,c=u[d=0].points):(l-=h.partialLength,c=null)),c&&(m=h,g=(h=c[f]).partialLength));B=D[a].an/2-D[a].add,S.translate(-B,0,0)}else B=D[a].an/2-D[a].add,S.translate(-B,0,0),S.translate(-k[0]*D[a].an*.005,-k[1]*V*.01,0);for(F=0;F<E;F+=1)(T=P[F].a).t.propType&&(L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars),0===i&&0===t.j||(this._hasMaskedPath?L.length?o+=T.t.v*L[0]:o+=T.t.v*L:L.length?i+=T.t.v*L[0]:i+=T.t.v*L));for(t.strokeWidthAnim&&(H=t.sw||0),t.strokeColorAnim&&(X=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(Y=[t.fc[0],t.fc[1],t.fc[2]]),F=0;F<E;F+=1)(T=P[F].a).a.propType&&((L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars)).length?S.translate(-T.a.v[0]*L[0],-T.a.v[1]*L[1],T.a.v[2]*L[2]):S.translate(-T.a.v[0]*L,-T.a.v[1]*L,T.a.v[2]*L));for(F=0;F<E;F+=1)(T=P[F].a).s.propType&&((L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars)).length?S.scale(1+(T.s.v[0]-1)*L[0],1+(T.s.v[1]-1)*L[1],1):S.scale(1+(T.s.v[0]-1)*L,1+(T.s.v[1]-1)*L,1));for(F=0;F<E;F+=1){if(T=P[F].a,L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars),T.sk.propType&&(L.length?S.skewFromAxis(-T.sk.v*L[0],T.sa.v*L[1]):S.skewFromAxis(-T.sk.v*L,T.sa.v*L)),T.r.propType&&(L.length?S.rotateZ(-T.r.v*L[2]):S.rotateZ(-T.r.v*L)),T.ry.propType&&(L.length?S.rotateY(T.ry.v*L[1]):S.rotateY(T.ry.v*L)),T.rx.propType&&(L.length?S.rotateX(T.rx.v*L[0]):S.rotateX(T.rx.v*L)),T.o.propType&&(L.length?W+=(T.o.v*L[0]-W)*L[0]:W+=(T.o.v*L-W)*L),t.strokeWidthAnim&&T.sw.propType&&(L.length?H+=T.sw.v*L[0]:H+=T.sw.v*L),t.strokeColorAnim&&T.sc.propType)for(G=0;G<3;G+=1)L.length?X[G]+=(T.sc.v[G]-X[G])*L[0]:X[G]+=(T.sc.v[G]-X[G])*L;if(t.fillColorAnim&&t.fc){if(T.fc.propType)for(G=0;G<3;G+=1)L.length?Y[G]+=(T.fc.v[G]-Y[G])*L[0]:Y[G]+=(T.fc.v[G]-Y[G])*L;T.fh.propType&&(Y=L.length?O(Y,T.fh.v*L[0]):O(Y,T.fh.v*L)),T.fs.propType&&(Y=L.length?R(Y,T.fs.v*L[0]):R(Y,T.fs.v*L)),T.fb.propType&&(Y=L.length?z(Y,T.fb.v*L[0]):z(Y,T.fb.v*L))}}for(F=0;F<E;F+=1)(T=P[F].a).p.propType&&(L=P[F].s.getMult(D[a].anIndexes[F],A.a[F].s.totalChars),this._hasMaskedPath?L.length?S.translate(0,T.p.v[1]*L[0],-T.p.v[2]*L[1]):S.translate(0,T.p.v[1]*L,-T.p.v[2]*L):L.length?S.translate(T.p.v[0]*L[0],T.p.v[1]*L[1],-T.p.v[2]*L[2]):S.translate(T.p.v[0]*L,T.p.v[1]*L,-T.p.v[2]*L));if(t.strokeWidthAnim&&(K=H<0?0:H),t.strokeColorAnim&&(J="rgb("+Math.round(255*X[0])+","+Math.round(255*X[1])+","+Math.round(255*X[2])+")"),t.fillColorAnim&&t.fc&&(U="rgb("+Math.round(255*Y[0])+","+Math.round(255*Y[1])+","+Math.round(255*Y[2])+")"),this._hasMaskedPath){if(S.translate(0,-t.ls),S.translate(0,k[1]*V*.01+s,0),this._pathData.p.v){b=(h.point[1]-m.point[1])/(h.point[0]-m.point[0]);var dt=180*Math.atan(b)/Math.PI;h.point[0]<m.point[0]&&(dt+=180),S.rotate(-dt*Math.PI/180)}S.translate(q,j,0),o-=k[0]*D[a].an*.005,D[a+1]&&Q!==D[a+1].ind&&(o+=D[a].an/2,o+=.001*t.tr*t.finalSize)}else{switch(S.translate(i,s,0),t.ps&&S.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:S.translate(D[a].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[D[a].line]),0,0);break;case 2:S.translate(D[a].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[D[a].line])/2,0,0)}S.translate(0,-t.ls),S.translate(B,0,0),S.translate(k[0]*D[a].an*.005,k[1]*V*.01,0),i+=D[a].l+.001*t.tr*t.finalSize}"html"===x?st=S.toCSS():"svg"===x?st=S.to2dCSS():at=[S.props[0],S.props[1],S.props[2],S.props[3],S.props[4],S.props[5],S.props[6],S.props[7],S.props[8],S.props[9],S.props[10],S.props[11],S.props[12],S.props[13],S.props[14],S.props[15]],Z=W}w<=a?(I=new yi(Z,K,J,U,st,at),this.renderedLetters.push(I),w+=1,this.lettersChangedFlag=!0):(I=this.renderedLetters[a],this.lettersChangedFlag=I.update(Z,K,J,U,st,at)||this.lettersChangedFlag)}}},ki.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},ki.prototype.mHelper=new Ft,ki.prototype.defaultPropsArray=[],r([bt],ki),Pi.prototype.initElement=function(t,e,i){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,i),this.textProperty=new vi(this,t.t,this.dynamicProperties),this.textAnimator=new ki(t.t,this.renderType,this),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},Pi.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)},Pi.prototype.createPathShape=function(t,e){var i,s,a=e.length,r="";for(i=0;i<a;i+=1)"sh"===e[i].ty&&(s=e[i].ks.k,r+=ci(s,s.i.length,!0,t));return r},Pi.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},Pi.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},Pi.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},Pi.prototype.applyTextPropertiesToMatrix=function(t,e,i,s,a){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i])/2,0,0)}e.translate(s,a,0)},Pi.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},Pi.prototype.emptyProp=new yi,Pi.prototype.destroy=function(){},Pi.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var Ai,Si={shapes:[]};function xi(t,e,i){this.textSpans=[],this.renderType="svg",this.initElement(t,e,i)}function wi(t,e,i){this.initElement(t,e,i)}function Di(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initFrame(),this.initTransform(t,e,i),this.initHierarchy()}function Ci(){}function Mi(){}function Ti(t,e,i){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?l(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?vt.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function Fi(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=W("svg");var i="";if(e&&e.title){var s=W("title"),a=I();s.setAttribute("id",a),s.textContent=e.title,this.svgElement.appendChild(s),i+=a}if(e&&e.description){var r=W("desc"),n=I();r.setAttribute("id",n),r.textContent=e.description,this.svgElement.appendChild(r),i+=" "+n}i&&this.svgElement.setAttribute("aria-labelledby",i);var o=W("defs");this.svgElement.appendChild(o);var h=W("g");this.svgElement.appendChild(h),this.layerElement=h,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:o,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}return r([Re,je,Je,Ue,ze,Ze,Pi],xi),xi.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=W("text"))},xi.prototype.buildTextContents=function(t){for(var e=0,i=t.length,s=[],a="";e<i;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(s.push(a),a=""):a+=t[e],e+=1;return s.push(a),s},xi.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var i=t.shapes[0];if(i.it){var s=i.it[i.it.length-1];s.s&&(s.s.k[0]=e,s.s.k[1]=e)}}return t},xi.prototype.buildNewText=function(){var t,e;this.addDynamicProperty(this);var i=this.textProperty.currentData;this.renderedLetters=l(i?i.l.length:0),i.fc?this.layerElement.setAttribute("fill",this.buildColor(i.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),i.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(i.sc)),this.layerElement.setAttribute("stroke-width",i.sw)),this.layerElement.setAttribute("font-size",i.finalSize);var s=this.globalData.fontManager.getFontByName(i.f);if(s.fClass)this.layerElement.setAttribute("class",s.fClass);else{this.layerElement.setAttribute("font-family",s.fFamily);var a=i.fWeight,r=i.fStyle;this.layerElement.setAttribute("font-style",r),this.layerElement.setAttribute("font-weight",a)}this.layerElement.setAttribute("aria-label",i.t);var n,o=i.l||[],h=!!this.globalData.fontManager.chars;e=o.length;var p=this.mHelper,f=this.data.singleShape,d=0,m=0,c=!0,u=.001*i.tr*i.finalSize;if(!f||h||i.sz){var g,y=this.textSpans.length;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!h||!f||0===t){if(n=y>t?this.textSpans[t].span:W(h?"g":"text"),y<=t){if(n.setAttribute("stroke-linecap","butt"),n.setAttribute("stroke-linejoin","round"),n.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=n,h){var v=W("g");n.appendChild(v),this.textSpans[t].childSpan=v}this.textSpans[t].span=n,this.layerElement.appendChild(n)}n.style.display="inherit"}if(p.reset(),f&&(o[t].n&&(d=-u,m+=i.yOffset,m+=c?1:0,c=!1),this.applyTextPropertiesToMatrix(i,p,o[t].line,d,m),d+=o[t].l||0,d+=u),h){var b;if(1===(g=this.globalData.fontManager.getCharData(i.finalText[t],s.fStyle,this.globalData.fontManager.getFontByName(i.f).fFamily)).t)b=new Ti(g.data,this.globalData,this);else{var _=Si;g.data&&g.data.shapes&&(_=this.buildShapeData(g.data,i.finalSize)),b=new gi(_,this.globalData,this)}if(this.textSpans[t].glyph){var k=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(k.layerElement),k.destroy()}this.textSpans[t].glyph=b,b._debug=!0,b.prepareFrame(0),b.renderFrame(),this.textSpans[t].childSpan.appendChild(b.layerElement),1===g.t&&this.textSpans[t].childSpan.setAttribute("transform","scale("+i.finalSize/100+","+i.finalSize/100+")")}else f&&n.setAttribute("transform","translate("+p.props[12]+","+p.props[13]+")"),n.textContent=o[t].val,n.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}f&&n&&n.setAttribute("d","")}else{var P=this.textContainer,A="start";switch(i.j){case 1:A="end";break;case 2:A="middle";break;default:A="start"}P.setAttribute("text-anchor",A),P.setAttribute("letter-spacing",u);var S=this.buildTextContents(i.finalText);for(e=S.length,m=i.ps?i.ps[1]+i.ascent:0,t=0;t<e;t+=1)(n=this.textSpans[t].span||W("tspan")).textContent=S[t],n.setAttribute("x",0),n.setAttribute("y",m),n.style.display="inherit",P.appendChild(n),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=n,m+=i.finalLineHeight;this.layerElement.appendChild(P)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},xi.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},xi.prototype.getValue=function(){var t,e,i=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<i;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},xi.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){var t,e;this._sizeChanged=!0;var i,s,a,r=this.textAnimator.renderedLetters,n=this.textProperty.currentData.l;for(e=n.length,t=0;t<e;t+=1)n[t].n||(i=r[t],s=this.textSpans[t].span,(a=this.textSpans[t].glyph)&&a.renderFrame(),i._mdf.m&&s.setAttribute("transform",i.m),i._mdf.o&&s.setAttribute("opacity",i.o),i._mdf.sw&&s.setAttribute("stroke-width",i.sw),i._mdf.sc&&s.setAttribute("stroke",i.sc),i._mdf.fc&&s.setAttribute("fill",i.fc))}},r([Qe],wi),wi.prototype.createContent=function(){var t=W("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},Di.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},Di.prototype.renderFrame=function(){},Di.prototype.getBaseElement=function(){return null},Di.prototype.destroy=function(){},Di.prototype.sourceRectAtTime=function(){},Di.prototype.hide=function(){},r([Re,je,Ue,ze],Di),r([Be],Ci),Ci.prototype.createNull=function(t){return new Di(t,this.globalData,this)},Ci.prototype.createShape=function(t){return new gi(t,this.globalData,this)},Ci.prototype.createText=function(t){return new xi(t,this.globalData,this)},Ci.prototype.createImage=function(t){return new Qe(t,this.globalData,this)},Ci.prototype.createSolid=function(t){return new wi(t,this.globalData,this)},Ci.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var i=W("clipPath"),a=W("rect");a.setAttribute("width",t.w),a.setAttribute("height",t.h),a.setAttribute("x",0),a.setAttribute("y",0);var r=I();i.setAttribute("id",r),i.appendChild(a),this.layerElement.setAttribute("clip-path","url("+s()+"#"+r+")"),e.appendChild(i),this.layers=t.layers,this.elements=l(t.layers.length)},Ci.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},Ci.prototype.updateContainerSize=function(){},Ci.prototype.findIndexByInd=function(t){var e=0,i=this.layers.length;for(e=0;e<i;e+=1)if(this.layers[e].ind===t)return e;return-1},Ci.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var i=this.createItem(this.layers[t]);if(e[t]=i,N()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(i),i.initExpressions()),this.appendElementInPos(i,t),this.layers[t].tt){var s="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(-1===s)return;if(this.elements[s]&&!0!==this.elements[s]){var a=e[s].getMatte(this.layers[t].tt);i.setMatte(a)}else this.buildItem(s),this.addPendingElement(i)}}},Ci.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,i=this.elements.length;e<i;){if(this.elements[e]===t){var s="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,a=this.elements[s].getMatte(this.layers[e].tt);t.setMatte(a);break}e+=1}}},Ci.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){var e;null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var i=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=i-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<i;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},Ci.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){for(var s,a=0;a<e;)this.elements[a]&&!0!==this.elements[a]&&this.elements[a].getBaseElement()&&(s=this.elements[a].getBaseElement()),a+=1;s?this.layerElement.insertBefore(i,s):this.layerElement.appendChild(i)}},Ci.prototype.hide=function(){this.layerElement.style.display="none"},Ci.prototype.show=function(){this.layerElement.style.display="block"},r([Re,je,Ue,ze,Ze],Mi),Mi.prototype.initElement=function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},Mi.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var i,s=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),i=s-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&(this.elements[i].prepareFrame(this.renderedFrame-this.layers[i].st),this.elements[i]._mdf&&(this._mdf=!0))}},Mi.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},Mi.prototype.setElements=function(t){this.elements=t},Mi.prototype.getElements=function(){return this.elements},Mi.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},Mi.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},r([Ci,Mi,Je],Ti),Ti.prototype.createComp=function(t){return new Ti(t,this.globalData,this)},r([Ci],Fi),Fi.prototype.createComp=function(t){return new Ti(t,this.globalData,this)},Ai=Fi,U["svg"]=Ai,Nt.registerModifier("tm",qt),Nt.registerModifier("pb",jt),Nt.registerModifier("rp",Xt),Nt.registerModifier("rd",Ht),Nt.registerModifier("zz",he),Nt.registerModifier("op",be),It}));
