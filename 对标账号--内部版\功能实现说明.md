# 设备使用时间限制功能实现说明

## 功能概述

本次实现了两个主要功能：

1. **服务器端自动检查过期设备功能** - 通过定时任务自动检查并拉黑过期设备
2. **设备分组批量设置功能** - 在管理界面中支持按设备分组批量设置使用时间限制

## 1. 服务器端过期设备检查功能

### 核心文件
- `check_expired_devices_cron.php` - 定时检查脚本（已修复MySQL 5.0兼容性）
- `setup_cron_task.bat` - Windows定时任务设置脚本
- `manual_check_expired.php` - 手动触发检查的Web界面
- `test_expired_devices.php` - 功能测试脚本

### 主要特性
- ✅ **MySQL 5.0完全兼容** - 修复了TIMESTAMP字段冲突问题
- ✅ **自动检查过期设备** - 每小时自动运行检查
- ✅ **智能拉黑处理** - 自动将过期设备加入黑名单
- ✅ **完善的日志记录** - 详细记录执行过程和结果
- ✅ **事务安全** - 使用数据库事务确保数据一致性
- ✅ **重复检查保护** - 避免重复拉黑已在黑名单中的设备

### 使用方法

#### 设置定时任务
1. 以管理员身份运行 `setup_cron_task.bat`
2. 脚本会自动创建每小时执行一次的Windows定时任务
3. 任务名称：`设备使用时间限制检查`

#### 手动执行检查
1. 访问 `manual_check_expired.php` 页面
2. 点击"立即执行检查"按钮
3. 查看执行结果和日志

#### 查看日志
- `expired_devices_check.log` - 正常执行日志
- `expired_devices_error.log` - 错误日志

## 2. 设备分组批量设置功能

### 核心文件
- `device_usage_limits.php` - 设备使用时间限制管理页面（已增强）

### 主要特性
- ✅ **选项卡界面** - 单个设备和分组批量两种模式
- ✅ **分组选择** - 自动加载现有设备分组
- ✅ **设备数量显示** - 选择分组后显示包含的设备数量
- ✅ **批量设置** - 一键为整个分组设置使用时间限制
- ✅ **智能分组获取** - 从device_groups表和实际设备数据中获取分组
- ✅ **操作确认** - 批量操作前需要用户确认

### 界面功能

#### 单个设备模式
- 输入设备ID和型号
- 设置限制类型（天/月/年）和数值
- 配置自动拉黑选项和拉黑原因

#### 分组批量模式
- 从下拉列表选择设备分组
- 自动显示分组中的设备数量
- 批量应用相同的时间限制设置
- 显示批量操作结果统计

## 3. MySQL 5.0兼容性修复

### 修复内容
- ✅ 修复了多个TIMESTAMP字段使用CURRENT_TIMESTAMP的问题
- ✅ 将部分字段改为DATETIME类型
- ✅ 使用NOW()函数替代CURRENT_TIMESTAMP
- ✅ 统一字符集为utf8（兼容MySQL 5.0）

### 涉及文件
- `设备使用时间限制表创建.sql`
- `device_usage_limits.php`
- `check_expired_devices_cron.php`

## 4. 新增辅助工具

### 测试工具
- `test_expired_devices.php` - 全面的功能测试脚本
  - 检查数据表结构
  - 统计活跃限制和过期设备
  - 检查设备分组状态
  - 验证黑名单功能
  - 检查日志文件

### 管理工具
- `manual_check_expired.php` - Web界面手动触发检查
  - 实时执行检查脚本
  - 显示执行输出
  - 查看最近日志
  - 系统状态监控

## 5. 使用流程

### 初始设置
1. 在phpMyAdmin中执行 `设备使用时间限制表创建.sql`
2. 运行 `setup_cron_task.bat` 设置定时任务
3. 访问 `device_usage_limits.php` 开始管理设备限制

### 日常使用
1. **单个设备设置**：在"单个设备"选项卡中输入设备信息
2. **批量设置**：在"按分组批量"选项卡中选择分组进行批量设置
3. **监控检查**：定时任务会自动执行，也可手动触发检查
4. **查看日志**：通过日志文件或Web界面监控系统运行状态

## 6. 注意事项

- 确保MySQL版本兼容性（已适配MySQL 5.0）
- 定时任务需要管理员权限设置
- 建议定期检查日志文件
- 批量操作前请确认分组中的设备列表
- 过期设备会被永久拉黑（100年），请谨慎设置时间限制

## 7. 故障排除

### 常见问题
1. **TIMESTAMP错误** - 已修复，使用新的SQL文件重新创建表
2. **定时任务不执行** - 检查Windows任务计划程序中的任务状态
3. **分组不显示** - 确保device_groups表中有数据或设备表中有分组信息
4. **权限错误** - 确保用户有相应的管理权限

### 调试方法
1. 运行 `test_expired_devices.php` 进行全面检查
2. 查看 `expired_devices_error.log` 错误日志
3. 使用 `manual_check_expired.php` 手动测试检查功能
4. 检查数据库表结构是否正确创建

---

**实现完成时间**: 2025-07-30
**兼容性**: MySQL 5.0+, PHP 7.0+, Windows Server
**状态**: 已完成并测试
