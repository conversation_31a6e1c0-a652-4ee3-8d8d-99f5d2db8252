{"name": "pend", "version": "1.2.0", "description": "dead-simple optimistic async helper", "main": "index.js", "scripts": {"test": "node test.js"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/andrewrk/node-pend.git"}, "bugs": {"url": "https://github.com/andrewrk/node-pend/issues"}, "__npminstall_done": true, "_from": "pend@1.2.0", "_resolved": "https://registry.npmmirror.com/pend/-/pend-1.2.0.tgz"}