<?php
// 身份验证保护
require_once 'auth_middleware.php';

// 检查登录状态
require_login();

// 记录页面访问
log_user_action('view_monitor', 'Accessed monitor page');

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

// 创建连接
$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");

// 处理清空日志请求（只处理AJAX清空日志请求）
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) &&
    $_POST['action'] === 'clear_startup_logs') {

    // 检查清空日志权限
    require_ajax_permission('clear_logs');
    header('Content-Type: application/json; charset=utf-8');

    try {
        // 只保留清空启动日志的功能
        if ($_POST['action'] === 'clear_startup_logs') {
            // 清空启动日志
            $delete_sql = "DELETE FROM startup_logs";
            $result = $conn->query($delete_sql);

            if ($result) {
                $deleted_count = $conn->affected_rows;
                echo json_encode([
                    'success' => true,
                    'message' => '启动日志已清空',
                    'deleted_count' => $deleted_count
                ]);
            } else {
                throw new Exception("清空启动日志失败: " . $conn->error);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// 处理获取分组列表的API请求
if (isset($_GET['action']) && $_GET['action'] == 'get_groups') {
    header('Content-Type: application/json');

    try {
        // 首先确保device_groups表存在
        $create_groups_table = "CREATE TABLE IF NOT EXISTS device_groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            group_name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL,
            INDEX idx_group_name (group_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$conn->query($create_groups_table)) {
            throw new Exception("创建分组表失败: " . $conn->error);
        }

        $sql = "SELECT group_name, description, created_at FROM device_groups ORDER BY group_name";
        $result = $conn->query($sql);

        $groups = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $groups[] = $row;
            }
        }

        echo json_encode([
            'success' => true,
            'groups' => $groups
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }

    exit;
}

// 处理获取单个分组信息的API请求
if (isset($_GET['action']) && $_GET['action'] == 'get_group_info') {
    header('Content-Type: application/json');

    $group_name = $_GET['group_name'] ?? '';

    if (empty($group_name)) {
        echo json_encode([
            'success' => false,
            'error' => '分组名称不能为空'
        ]);
        exit;
    }

    try {
        // 首先确保device_groups表存在并有正确的结构
        $create_groups_table = "CREATE TABLE IF NOT EXISTS device_groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            group_name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL,
            INDEX idx_group_name (group_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$conn->query($create_groups_table)) {
            throw new Exception("创建分组表失败: " . $conn->error);
        }

        // 检查并添加updated_at字段（如果不存在）
        $check_updated_at = "SHOW COLUMNS FROM device_groups LIKE 'updated_at'";
        $result_check = $conn->query($check_updated_at);
        if ($result_check->num_rows == 0) {
            $add_updated_at = "ALTER TABLE device_groups ADD COLUMN updated_at DATETIME DEFAULT NULL";
            $conn->query($add_updated_at);

            // 创建触发器来自动管理updated_at字段
            $create_trigger = "
                CREATE TRIGGER IF NOT EXISTS device_groups_update_timestamp
                BEFORE UPDATE ON device_groups
                FOR EACH ROW
                SET NEW.updated_at = CURRENT_TIMESTAMP
            ";
            $conn->query($create_trigger);
        }

        $sql = "SELECT group_name, description, created_at, updated_at FROM device_groups WHERE group_name = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $group_name);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $group = $result->fetch_assoc();
            echo json_encode([
                'success' => true,
                'group' => $group
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => '分组不存在'
            ]);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }

    exit;
}

// 处理获取黑名单列表的API请求
if (isset($_GET['action']) && $_GET['action'] == 'get_blacklist') {
    header('Content-Type: application/json');

    try {
        // 确保device_blacklist表存在并具有完整的字段结构
        $create_blacklist_table = "CREATE TABLE IF NOT EXISTS device_blacklist (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_id VARCHAR(100) NOT NULL UNIQUE,
            device_model VARCHAR(255),
            blacklist_reason TEXT,
            trigger_count INT DEFAULT 0,
            blacklist_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            blacklist_end_time TIMESTAMP,
            blacklist_duration_hours INT DEFAULT 24,
            is_active TINYINT(1) DEFAULT 1,
            blacklisted_by VARCHAR(100) DEFAULT 'system',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_device_id (device_id),
            INDEX idx_is_active (is_active),
            INDEX idx_blacklist_end_time (blacklist_end_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $conn->query($create_blacklist_table);

        // 检查并添加缺失的字段（兼容旧数据库）
        $columns_to_add = [
            'device_model' => "ALTER TABLE device_blacklist ADD COLUMN device_model VARCHAR(255) AFTER device_id",
            'trigger_count' => "ALTER TABLE device_blacklist ADD COLUMN trigger_count INT DEFAULT 0 AFTER blacklist_reason",
            'blacklist_start_time' => "ALTER TABLE device_blacklist ADD COLUMN blacklist_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER trigger_count",
            'blacklist_end_time' => "ALTER TABLE device_blacklist ADD COLUMN blacklist_end_time TIMESTAMP AFTER blacklist_start_time",
            'blacklist_duration_hours' => "ALTER TABLE device_blacklist ADD COLUMN blacklist_duration_hours INT DEFAULT 24 AFTER blacklist_end_time",
            'is_active' => "ALTER TABLE device_blacklist ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER blacklist_duration_hours",
            'blacklisted_by' => "ALTER TABLE device_blacklist ADD COLUMN blacklisted_by VARCHAR(100) DEFAULT 'system' AFTER is_active",
            'updated_at' => "ALTER TABLE device_blacklist ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER blacklisted_by"
        ];

        // 检查每个字段是否存在，如果不存在则添加
        foreach ($columns_to_add as $column_name => $alter_query) {
            $check_column_sql = "SHOW COLUMNS FROM device_blacklist LIKE '{$column_name}'";
            $check_result = $conn->query($check_column_sql);

            if ($check_result && $check_result->num_rows == 0) {
                // 字段不存在，添加它
                $add_result = $conn->query($alter_query);
                if ($add_result) {
                    error_log("黑名单调试：成功添加字段 {$column_name}");
                } else {
                    error_log("黑名单调试：添加字段 {$column_name} 失败: " . $conn->error);
                }
            } else {
                error_log("黑名单调试：字段 {$column_name} 已存在");
            }
        }

        // 为旧记录设置默认值（如果为NULL）
        $update_old_records = "UPDATE device_blacklist
                              SET blacklist_end_time = CASE
                                    WHEN blacklist_end_time IS NULL THEN
                                        CASE
                                            WHEN blacklist_start_time IS NOT NULL THEN DATE_ADD(blacklist_start_time, INTERVAL 24 HOUR)
                                            ELSE DATE_ADD(NOW(), INTERVAL 24 HOUR)
                                        END
                                    ELSE blacklist_end_time
                                  END,
                                  is_active = CASE WHEN is_active IS NULL THEN 1 ELSE is_active END
                              WHERE blacklist_end_time IS NULL OR is_active IS NULL";
        $update_result = $conn->query($update_old_records);

        // 调试：记录更新了多少条记录
        $updated_rows = $conn->affected_rows;
        error_log("黑名单调试：更新了 {$updated_rows} 条旧记录");

        // 调试：先查询所有记录
        $debug_all_sql = "SELECT COUNT(*) as total_count FROM device_blacklist";
        $debug_all_result = $conn->query($debug_all_sql);
        $total_count = $debug_all_result ? $debug_all_result->fetch_assoc()['total_count'] : 0;
        error_log("黑名单调试：数据库中总共有 {$total_count} 条黑名单记录");

        // 调试：查询活跃记录数量
        $debug_active_sql = "SELECT COUNT(*) as active_count FROM device_blacklist WHERE is_active = 1";
        $debug_active_result = $conn->query($debug_active_sql);
        $active_count = $debug_active_result ? $debug_active_result->fetch_assoc()['active_count'] : 0;
        error_log("黑名单调试：活跃记录有 {$active_count} 条");

        // 调试：查询活跃的记录数量
        $debug_valid_sql = "SELECT COUNT(*) as valid_count FROM device_blacklist WHERE is_active = 1";
        $debug_valid_result = $conn->query($debug_valid_sql);
        $valid_count = $debug_valid_result ? $debug_valid_result->fetch_assoc()['valid_count'] : 0;
        error_log("黑名单调试：活跃的记录有 {$valid_count} 条");

        $sql = "SELECT device_id, blacklist_reason, blacklisted_by, blacklist_start_time as blacklist_time, blacklist_end_time, is_active
                FROM device_blacklist
                WHERE is_active = 1
                ORDER BY blacklist_start_time DESC";
        $result = $conn->query($sql);

        $blacklist = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $blacklist[] = $row;
                // 调试：记录每条返回的记录
                error_log("黑名单调试：返回记录 - 设备ID: {$row['device_id']}, 活跃状态: {$row['is_active']}, 结束时间: {$row['blacklist_end_time']}");
            }
        }

        error_log("黑名单调试：最终返回 " . count($blacklist) . " 条记录");
        echo json_encode($blacklist);

    } catch (Exception $e) {
        echo json_encode([
            'error' => $e->getMessage()
        ]);
    }

    exit;
}

// 处理获取在线设备列表的API请求
if (isset($_GET['action']) && $_GET['action'] == 'get_online_devices') {
    header('Content-Type: application/json');

    try {
        $group_filter = $_GET['group'] ?? '';

        // 构建查询条件
        $where_conditions = ["h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)"];
        $params = [];
        $types = '';

        if (!empty($group_filter)) {
            $where_conditions[] = "d.device_group = ?";
            $params[] = $group_filter;
            $types .= 's';
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // 查询在线设备
        $sql = "SELECT h.device_id, h.device_model, h.android_version, h.running_status,
                       h.last_heartbeat, d.device_group
                FROM device_heartbeat h
                LEFT JOIN devices d ON h.device_id = d.device_id
                $where_clause
                ORDER BY h.last_heartbeat DESC";

        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();

        $devices = [];
        while ($row = $result->fetch_assoc()) {
            $devices[] = [
                'device_id' => $row['device_id'],
                'device_model' => $row['device_model'],
                'android_version' => $row['android_version'],
                'device_group' => $row['device_group'],
                'running_status' => $row['running_status'],
                'last_heartbeat' => $row['last_heartbeat']
            ];
        }

        echo json_encode([
            'success' => true,
            'devices' => $devices,
            'total' => count($devices)
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }

    exit;
}

// 处理检查过期设备的API请求
if (isset($_GET['action']) && $_GET['action'] == 'check_expired_devices') {
    header('Content-Type: application/json');
    
    // 检查权限
    if (!check_permission('manage_blacklist')) {
        echo json_encode(['success' => false, 'error' => '权限不足']);
        exit;
    }
    
    try {
        // 确保device_usage_limits表存在
        $create_usage_limits_table = "CREATE TABLE IF NOT EXISTS device_usage_limits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_id VARCHAR(100) NOT NULL,
            device_model VARCHAR(255),
            limit_type ENUM('day','month','year') NOT NULL DEFAULT 'day',
            limit_value INT NOT NULL DEFAULT 1,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time TIMESTAMP NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            auto_blacklist TINYINT(1) DEFAULT 1,
            blacklist_reason VARCHAR(255) DEFAULT '设备使用时间到期自动拉黑',
            created_by VARCHAR(100) DEFAULT 'system',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_device_limit (device_id),
            INDEX idx_device_id (device_id),
            INDEX idx_limit_type (limit_type),
            INDEX idx_is_active (is_active),
            INDEX idx_end_time (end_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->query($create_usage_limits_table);
        
        // 查找过期设备
        $expired_sql = "SELECT device_id, device_model, blacklist_reason 
                       FROM device_usage_limits 
                       WHERE is_active = 1 AND auto_blacklist = 1 AND end_time <= NOW()";
        
        $result = $conn->query($expired_sql);
        $expired_count = 0;
        $processed_devices = [];
        
        if ($result && $result->num_rows > 0) {
            $conn->begin_transaction();
            
            try {
                while ($row = $result->fetch_assoc()) {
                    $device_id = $row['device_id'];
                    $device_model = $row['device_model'] ?? '未知设备';
                    $reason = $row['blacklist_reason'];
                    
                    // 检查是否已在黑名单中
                    $check_blacklist = "SELECT COUNT(*) as count FROM device_blacklist WHERE device_id = ? AND is_active = 1";
                    $check_stmt = $conn->prepare($check_blacklist);
                    $check_stmt->bind_param("s", $device_id);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();
                    $is_blacklisted = $check_result->fetch_assoc()['count'] > 0;
                    $check_stmt->close();
                    
                    if (!$is_blacklisted) {
                        // 添加到黑名单
                        $blacklist_sql = "INSERT INTO device_blacklist 
                                         (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, blacklisted_by, updated_at)
                                         VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, 'usage-limit-system', NOW())";
                        
                        $blacklist_stmt = $conn->prepare($blacklist_sql);
                        $blacklist_stmt->bind_param("sss", $device_id, $device_model, $reason);
                        $blacklist_stmt->execute();
                        $blacklist_stmt->close();
                        
                        $expired_count++;
                        $processed_devices[] = $device_id;
                    }
                    
                    // 将使用时间限制设为非活跃
                    $deactivate_sql = "UPDATE device_usage_limits SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE device_id = ?";
                    $deactivate_stmt = $conn->prepare($deactivate_sql);
                    $deactivate_stmt->bind_param("s", $device_id);
                    $deactivate_stmt->execute();
                    $deactivate_stmt->close();
                }
                
                $conn->commit();
                log_user_action('device_usage_expired_check', "Processed {$expired_count} expired devices: " . implode(', ', $processed_devices));
                
                echo json_encode([
                    'success' => true, 
                    'message' => "已处理 {$expired_count} 个过期设备", 
                    'count' => $expired_count,
                    'devices' => $processed_devices
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                throw $e;
            }
        } else {
            echo json_encode(['success' => true, 'message' => '没有发现过期设备', 'count' => 0]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    
    exit;
}

// 处理获取设备详情的API请求
if (isset($_GET['action']) && $_GET['action'] == 'get_device_details') {
    header('Content-Type: application/json');

    $device_id = $_GET['device_id'] ?? '';

    if (empty($device_id)) {
        echo json_encode([
            'success' => false,
            'error' => '设备ID不能为空'
        ]);
        exit;
    }

    try {
        $sql = "SELECT d.*,
                       CASE WHEN b.device_id IS NOT NULL AND b.is_active = 1 THEN 1 ELSE 0 END as is_blacklisted,
                       b.blacklist_reason, b.blacklisted_by, b.blacklist_start_time as blacklist_time
                FROM devices d
                LEFT JOIN device_blacklist b ON d.device_id = b.device_id AND b.is_active = 1
                WHERE d.device_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $device_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $device = $result->fetch_assoc();
            echo json_encode([
                'success' => true,
                'device' => $device
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => '设备不存在'
            ]);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }

    exit;
}

// 自动拉黑未分组新设备的函数
function autoBlacklistUngroupedDevices($conn) {
    try {
        // 确保devices表有first_seen字段
        $check_column_sql = "SHOW COLUMNS FROM devices LIKE 'first_seen'";
        $column_result = $conn->query($check_column_sql);

        if ($column_result->num_rows == 0) {
            // 添加first_seen字段
            $add_column_sql = "ALTER TABLE devices ADD COLUMN first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次发现时间'";
            $conn->query($add_column_sql);

            // 为现有设备设置first_seen时间（使用last_active或当前时间）
            $update_existing_sql = "UPDATE devices SET first_seen = COALESCE(last_active, NOW()) WHERE first_seen IS NULL";
            $conn->query($update_existing_sql);
        }

        // 查找10分钟前注册且未分组的设备
        $find_ungrouped_sql = "
            SELECT device_id, first_seen,
                   TIMESTAMPDIFF(MINUTE, first_seen, NOW()) as minutes_since_registration
            FROM devices
            WHERE (device_group IS NULL OR device_group = '' OR device_group = '未分组')
            AND first_seen <= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            AND device_id NOT IN (SELECT device_id FROM device_blacklist WHERE is_active = 1)
        ";

        $ungrouped_result = $conn->query($find_ungrouped_sql);

        if ($ungrouped_result && $ungrouped_result->num_rows > 0) {
            $blacklisted_count = 0;

            // device_blacklist表应该已经存在（通过create_device_blacklist_simple.sql创建）

            while ($row = $ungrouped_result->fetch_assoc()) {
                $device_id = $row['device_id'];
                $minutes_since = $row['minutes_since_registration'];
                $first_seen = $row['first_seen'];

                // 添加到黑名单，包含更详细的原因
                $reason = "自动拉黑：注册{$minutes_since}分钟未分组 (注册时间: {$first_seen})";
                $device_model = "自动拉黑设备";

                // 使用新的表结构插入黑名单
                $blacklist_sql = "INSERT INTO device_blacklist (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, updated_at)
                                 VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, NOW())
                                 ON DUPLICATE KEY UPDATE
                                 blacklist_reason = VALUES(blacklist_reason),
                                 blacklist_start_time = VALUES(blacklist_start_time),
                                 blacklist_end_time = VALUES(blacklist_end_time),
                                 is_active = 1,
                                 updated_at = VALUES(updated_at)";

                $stmt = $conn->prepare($blacklist_sql);
                if ($stmt) {
                    $stmt->bind_param("sss", $device_id, $device_model, $reason);
                    if ($stmt->execute()) {
                        $blacklisted_count++;
                        error_log("自动拉黑设备: {$device_id} (注册{$minutes_since}分钟未分组)");
                    }
                    $stmt->close();
                }
            }

            if ($blacklisted_count > 0) {
                error_log("自动拉黑系统：已拉黑 {$blacklisted_count} 个未分组设备");
            }
        }

    } catch (Exception $e) {
        error_log("自动拉黑系统错误: " . $e->getMessage());
    }
}

// 在页面加载时执行自动拉黑检查
autoBlacklistUngroupedDevices($conn);

// 处理手动触发自动拉黑的请求
if (isset($_POST['trigger_auto_blacklist']) || isset($_GET['action']) && $_GET['action'] == 'trigger_auto_blacklist') {
    // 检查管理黑名单权限
    require_ajax_permission('manage_blacklist');
    header('Content-Type: application/json');

    try {
        // 执行自动拉黑前先统计（基于黑名单原因判断是否为自动拉黑）
        $before_count_sql = "SELECT COUNT(*) as count FROM device_blacklist WHERE blacklist_reason LIKE '%自动拉黑%' AND is_active = 1";
        $before_result = $conn->query($before_count_sql);
        $before_count = $before_result ? $before_result->fetch_assoc()['count'] : 0;

        // 统计待处理设备数
        $check_count_sql = "SELECT COUNT(*) as count FROM devices
                           WHERE (device_group IS NULL OR device_group = '' OR device_group = '未分组')
                           AND first_seen <= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                           AND device_id NOT IN (SELECT device_id FROM device_blacklist)";
        $check_result = $conn->query($check_count_sql);
        $checked_count = $check_result ? $check_result->fetch_assoc()['count'] : 0;

        // 执行自动拉黑
        autoBlacklistUngroupedDevices($conn);

        // 统计执行后的数量（基于黑名单原因判断是否为自动拉黑）
        $after_count_sql = "SELECT COUNT(*) as count FROM device_blacklist WHERE blacklist_reason LIKE '%自动拉黑%' AND is_active = 1";
        $after_result = $conn->query($after_count_sql);
        $after_count = $after_result ? $after_result->fetch_assoc()['count'] : 0;

        $blacklisted_count = $after_count - $before_count;

        echo json_encode([
            'success' => true,
            'checked_count' => $checked_count,
            'blacklisted_count' => $blacklisted_count,
            'message' => "自动拉黑执行完成，检查了 {$checked_count} 个设备，拉黑了 {$blacklisted_count} 个设备"
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }

    exit;
}

// 处理心跳详情请求
if (isset($_GET['action']) && $_GET['action'] == 'get_heartbeat_details') {
    header('Content-Type: application/json; charset=utf-8');

    $device_id = $_GET['device_id'] ?? '';

    if (empty($device_id)) {
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 检查表是否存在
        $check_table_sql = "SHOW TABLES LIKE 'device_heartbeat'";
        $table_check = $conn->query($check_table_sql);

        if ($table_check->num_rows == 0) {
            echo json_encode(['success' => false, 'error' => 'device_heartbeat 表不存在，请先初始化数据库']);
            exit;
        }

        $heartbeat_detail_sql = "SELECT * FROM device_heartbeat WHERE device_id = ? LIMIT 1";
        $stmt = $conn->prepare($heartbeat_detail_sql);
        $stmt->bind_param("s", $device_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $data = $result->fetch_assoc();
            echo json_encode(['success' => true, 'data' => $data]);
        } else {
            echo json_encode(['success' => false, 'error' => '未找到该设备的心跳记录']);
        }

        $stmt->close();
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '查询失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理截图上传请求
if (isset($_POST['action']) && $_POST['action'] == 'upload_screenshot') {
    header('Content-Type: application/json; charset=utf-8');

    $device_id = $_POST['device_id'] ?? '';
    $screenshot_data = $_POST['screenshot_data'] ?? '';

    if (empty($device_id)) {
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    if (empty($screenshot_data)) {
        echo json_encode(['success' => false, 'error' => '截图数据不能为空']);
        exit;
    }

    try {
        // 确保截图表存在
        $create_screenshot_table = "CREATE TABLE IF NOT EXISTS device_screenshots (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_id VARCHAR(100) NOT NULL,
            screenshot_data LONGTEXT NOT NULL COMMENT 'Base64编码的截图数据',
            upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            file_size INT DEFAULT NULL COMMENT '文件大小(字节)',
            INDEX idx_device_id (device_id),
            INDEX idx_upload_time (upload_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$conn->query($create_screenshot_table)) {
            throw new Exception("创建截图表失败: " . $conn->error);
        }

        // 计算文件大小
        $file_size = strlen($screenshot_data);

        // 插入或更新截图数据（每个设备只保留最新的截图）
        $insert_sql = "INSERT INTO device_screenshots (device_id, screenshot_data, file_size, upload_time)
                      VALUES (?, ?, ?, NOW())
                      ON DUPLICATE KEY UPDATE
                      screenshot_data = VALUES(screenshot_data),
                      file_size = VALUES(file_size),
                      upload_time = NOW()";

        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssi", $device_id, $screenshot_data, $file_size);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => '截图上传成功',
                'file_size' => $file_size
            ]);
        } else {
            throw new Exception("插入截图数据失败: " . $stmt->error);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '上传失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理截图请求（当点击查看桌面时）
if (isset($_POST['action']) && $_POST['action'] == 'request_screenshot') {
    // 检查查看截图权限
    require_ajax_permission('view_screenshots');
    header('Content-Type: application/json');

    $device_id = $_POST['device_id'] ?? '';
    $requester_ip = $_SERVER['REMOTE_ADDR'] ?? '';

    if (empty($device_id)) {
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 确保截图请求表存在
        $create_request_table = "CREATE TABLE IF NOT EXISTS `screenshot_requests` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` varchar(100) NOT NULL,
            `request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `expire_time` timestamp NULL DEFAULT NULL,
            `status` enum('active','expired') DEFAULT 'active',
            `requester_ip` varchar(45) DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_device_request` (`device_id`),
            KEY `idx_device_status` (`device_id`, `status`),
            KEY `idx_expire_time` (`expire_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if (!$conn->query($create_request_table)) {
            throw new Exception("创建截图请求表失败: " . $conn->error);
        }

        // 检查截图总数，如果超过50张则清理所有截图
        $count_sql = "SELECT COUNT(*) as total FROM device_screenshots";
        $count_result = $conn->query($count_sql);
        $total_screenshots = 0;

        if ($count_result) {
            $count_row = $count_result->fetch_assoc();
            $total_screenshots = $count_row['total'];
        }

        // 如果截图总数超过50，清理所有截图
        if ($total_screenshots > 50) {
            $cleanup_sql = "DELETE FROM device_screenshots";
            if ($conn->query($cleanup_sql)) {
                $cleaned_count = $conn->affected_rows;
                error_log("截图数量超过50张，已清理 {$cleaned_count} 张截图");
            }
        }

        // 插入或更新截图请求
        $request_sql = "INSERT INTO `screenshot_requests` (`device_id`, `requester_ip`, `request_time`, `expire_time`)
                       VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 5 MINUTE))
                       ON DUPLICATE KEY UPDATE
                       `request_time` = NOW(),
                       `expire_time` = DATE_ADD(NOW(), INTERVAL 5 MINUTE),
                       `status` = 'active',
                       `requester_ip` = VALUES(`requester_ip`)";

        $stmt = $conn->prepare($request_sql);
        $stmt->bind_param("ss", $device_id, $requester_ip);

        if ($stmt->execute()) {
            $response_data = [
                'success' => true,
                'message' => '截图请求已发送',
                'device_id' => $device_id,
                'expire_time' => date('Y-m-d H:i:s', strtotime('+5 minutes')),
                'total_screenshots_before' => $total_screenshots
            ];

            // 如果进行了清理，添加清理信息
            if ($total_screenshots > 50) {
                $response_data['cleanup_performed'] = true;
                $response_data['cleaned_count'] = $cleaned_count ?? 0;
                $response_data['message'] .= "，已清理旧截图";
            }

            echo json_encode($response_data);
        } else {
            throw new Exception("发送截图请求失败: " . $stmt->error);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '请求失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理停止截图请求
if (isset($_POST['action']) && $_POST['action'] == 'stop_screenshot') {
    // 检查查看截图权限
    require_ajax_permission('view_screenshots');
    header('Content-Type: application/json');

    $device_id = $_POST['device_id'] ?? '';

    if (empty($device_id)) {
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 将该设备的截图请求设置为过期
        $stop_sql = "UPDATE `screenshot_requests`
                    SET `status` = 'expired', `expire_time` = NOW()
                    WHERE `device_id` = ? AND `status` = 'active'";

        $stmt = $conn->prepare($stop_sql);
        $stmt->bind_param("s", $device_id);

        if ($stmt->execute()) {
            $affected_rows = $stmt->affected_rows;
            echo json_encode([
                'success' => true,
                'message' => '停止截图请求已发送',
                'device_id' => $device_id,
                'affected_rows' => $affected_rows
            ]);
        } else {
            throw new Exception("停止截图请求失败: " . $stmt->error);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '停止请求失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理获取历史截图列表请求
if (isset($_GET['action']) && $_GET['action'] == 'get_screenshot_history') {
    $device_id = $_GET['device_id'] ?? '';
    $limit = intval($_GET['limit'] ?? 20); // 默认获取20张
    $offset = intval($_GET['offset'] ?? 0); // 偏移量，用于分页

    if (empty($device_id)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 检查截图表是否存在
        $check_table_sql = "SHOW TABLES LIKE 'device_screenshots'";
        $table_check = $conn->query($check_table_sql);

        if ($table_check->num_rows == 0) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => '截图表不存在']);
            exit;
        }

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM device_screenshots WHERE device_id = ?";
        $count_stmt = $conn->prepare($count_sql);
        $count_stmt->bind_param("s", $device_id);
        $count_stmt->execute();
        $count_result = $count_stmt->get_result();
        $total_count = $count_result->fetch_assoc()['total'];
        $count_stmt->close();

        // 获取历史截图列表（包含缩略图数据用于预览）
        $history_sql = "SELECT id, upload_time, file_size,
                       TIMESTAMPDIFF(SECOND, upload_time, NOW()) as age_seconds,
                       LEFT(screenshot_data, 1000) as thumbnail_data
                       FROM device_screenshots
                       WHERE device_id = ?
                       ORDER BY upload_time DESC
                       LIMIT ? OFFSET ?";

        $stmt = $conn->prepare($history_sql);
        $stmt->bind_param("sii", $device_id, $limit, $offset);
        $stmt->execute();
        $result = $stmt->get_result();

        $screenshots = [];
        while ($row = $result->fetch_assoc()) {
            $screenshots[] = [
                'id' => $row['id'],
                'upload_time' => $row['upload_time'],
                'file_size' => $row['file_size'],
                'age_seconds' => $row['age_seconds'],
                'formatted_time' => date('Y-m-d H:i:s', strtotime($row['upload_time'])),
                'has_data' => !empty($row['thumbnail_data'])
            ];
        }

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'screenshots' => $screenshots,
            'total_count' => $total_count,
            'current_page' => floor($offset / $limit) + 1,
            'total_pages' => ceil($total_count / $limit),
            'device_id' => $device_id
        ]);

        $stmt->close();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '获取历史截图失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理获取单张历史截图请求
if (isset($_GET['action']) && $_GET['action'] == 'get_screenshot_by_id') {
    $screenshot_id = $_GET['id'] ?? '';

    if (empty($screenshot_id)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '截图ID不能为空']);
        exit;
    }

    try {
        // 记录请求日志
        error_log("获取截图详情请求: ID={$screenshot_id}");

        // 获取指定ID的截图
        $screenshot_sql = "SELECT screenshot_data, upload_time, file_size, device_id FROM device_screenshots WHERE id = ?";
        $stmt = $conn->prepare($screenshot_sql);
        $stmt->bind_param("i", $screenshot_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();

            // 检查截图数据是否存在
            if (empty($row['screenshot_data'])) {
                error_log("截图数据为空: ID={$screenshot_id}");
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => '截图数据为空']);
                $stmt->close();
                exit;
            }

            $upload_time = strtotime($row['upload_time']);
            $current_time = time();
            $age_seconds = $current_time - $upload_time;
            $data_size = strlen($row['screenshot_data']);

            error_log("截图数据获取成功: ID={$screenshot_id}, 数据大小={$data_size}字节");

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'screenshot_data' => $row['screenshot_data'],
                'upload_time' => $row['upload_time'],
                'file_size' => $row['file_size'],
                'device_id' => $row['device_id'],
                'age_seconds' => $age_seconds,
                'is_historical' => true,
                'data_size' => $data_size
            ]);
        } else {
            error_log("未找到截图: ID={$screenshot_id}");
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => '未找到指定的截图']);
        }

        $stmt->close();

    } catch (Exception $e) {
        error_log("获取截图失败: ID={$screenshot_id}, 错误: " . $e->getMessage());
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '获取截图失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理检查设备拉黑状态请求
if (isset($_GET['action']) && $_GET['action'] == 'check_blacklist_status') {
    $device_id = $_GET['device_id'] ?? '';

    if (empty($device_id)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 检查设备是否在活跃黑名单中
        $blacklist_sql = "SELECT * FROM device_blacklist WHERE device_id = ? AND is_active = 1";
        $stmt = $conn->prepare($blacklist_sql);
        $stmt->bind_param("s", $device_id);
        $stmt->execute();
        $result = $stmt->get_result();

        $is_blacklisted = $result->num_rows > 0;
        $blacklist_info = null;

        if ($is_blacklisted) {
            $blacklist_info = $result->fetch_assoc();
        }

        $stmt->close();

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'is_blacklisted' => $is_blacklisted,
            'blacklist_info' => $blacklist_info,
            'device_id' => $device_id,
            'check_time' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '检查拉黑状态失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理获取分组心跳统计请求
if (isset($_GET['action']) && $_GET['action'] == 'get_group_heartbeat_stats') {
    header('Content-Type: application/json; charset=utf-8');

    try {
        // 检查心跳表是否存在
        $check_table_sql = "SHOW TABLES LIKE 'device_heartbeat'";
        $table_check = $conn->query($check_table_sql);

        if ($table_check->num_rows == 0) {
            echo json_encode(['success' => false, 'error' => 'device_heartbeat 表不存在']);
            exit;
        }

        // 获取分组心跳统计
        $group_heartbeat_sql = "
            SELECT
                COALESCE(d.device_group, '未分组') as group_name,
                COUNT(h.device_id) as total_devices,
                COUNT(CASE WHEN h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) THEN 1 END) as online_devices,
                COUNT(CASE WHEN h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 2 MINUTE) AND h.last_heartbeat < DATE_SUB(NOW(), INTERVAL 1 MINUTE) THEN 1 END) as warning_devices,
                COUNT(CASE WHEN h.last_heartbeat < DATE_SUB(NOW(), INTERVAL 2 MINUTE) THEN 1 END) as offline_devices,
                COUNT(CASE WHEN h.running_status = 'running' THEN 1 END) as running_devices,
                MAX(h.last_heartbeat) as latest_heartbeat
            FROM device_heartbeat h
            LEFT JOIN devices d ON h.device_id = d.device_id
            GROUP BY COALESCE(d.device_group, '未分组')
            ORDER BY group_name
        ";

        $result = $conn->query($group_heartbeat_sql);
        $group_stats = [];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $group_stats[] = [
                    'group_name' => $row['group_name'],
                    'total_devices' => (int)$row['total_devices'],
                    'online_devices' => (int)$row['online_devices'],
                    'warning_devices' => (int)$row['warning_devices'],
                    'offline_devices' => (int)$row['offline_devices'],
                    'running_devices' => (int)$row['running_devices'],
                    'latest_heartbeat' => $row['latest_heartbeat'],
                    'formatted_time' => $row['latest_heartbeat'] ? date('Y-m-d H:i:s', strtotime($row['latest_heartbeat'])) : '无数据'
                ];
            }
        }

        // 计算总体统计
        $total_stats = [
            'total_devices' => array_sum(array_column($group_stats, 'total_devices')),
            'online_devices' => array_sum(array_column($group_stats, 'online_devices')),
            'warning_devices' => array_sum(array_column($group_stats, 'warning_devices')),
            'offline_devices' => array_sum(array_column($group_stats, 'offline_devices')),
            'running_devices' => array_sum(array_column($group_stats, 'running_devices'))
        ];

        echo json_encode([
            'success' => true,
            'group_stats' => $group_stats,
            'total_stats' => $total_stats
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '获取分组心跳统计失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理获取所有设备截图概览请求
if (isset($_GET['action']) && $_GET['action'] == 'get_all_screenshots') {
    header('Content-Type: application/json; charset=utf-8');

    try {
        // 检查截图表是否存在
        $check_table_sql = "SHOW TABLES LIKE 'device_screenshots'";
        $table_check = $conn->query($check_table_sql);

        if ($table_check->num_rows == 0) {
            echo json_encode(['success' => false, 'error' => '截图表不存在']);
            exit;
        }

        // 获取所有设备的最新截图
        $screenshots_sql = "
            SELECT ds.device_id, ds.upload_time, ds.file_size,
                   TIMESTAMPDIFF(SECOND, ds.upload_time, NOW()) as age_seconds,
                   d.device_model, d.device_group,
                   CASE WHEN b.device_id IS NOT NULL THEN 1 ELSE 0 END as is_blacklisted
            FROM device_screenshots ds
            LEFT JOIN devices d ON ds.device_id = d.device_id
            LEFT JOIN device_blacklist b ON ds.device_id = b.device_id AND b.is_active = 1
            WHERE ds.upload_time = (
                SELECT MAX(upload_time)
                FROM device_screenshots ds2
                WHERE ds2.device_id = ds.device_id
            )
            ORDER BY ds.upload_time DESC
        ";

        $result = $conn->query($screenshots_sql);
        $screenshots = [];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $screenshots[] = [
                    'device_id' => $row['device_id'],
                    'upload_time' => $row['upload_time'],
                    'file_size' => $row['file_size'],
                    'age_seconds' => $row['age_seconds'],
                    'device_model' => $row['device_model'],
                    'device_group' => $row['device_group'] ?: '未分组',
                    'is_blacklisted' => (bool)$row['is_blacklisted'],
                    'formatted_time' => date('Y-m-d H:i:s', strtotime($row['upload_time'])),
                    'status' => $row['age_seconds'] <= 120 ? 'online' : ($row['age_seconds'] <= 600 ? 'recent' : 'offline')
                ];
            }
        }

        // 统计信息
        $total_devices = count($screenshots);
        $online_devices = count(array_filter($screenshots, function($s) { return $s['status'] === 'online'; }));
        $recent_devices = count(array_filter($screenshots, function($s) { return $s['status'] === 'recent'; }));
        $offline_devices = count(array_filter($screenshots, function($s) { return $s['status'] === 'offline'; }));

        echo json_encode([
            'success' => true,
            'screenshots' => $screenshots,
            'stats' => [
                'total_devices' => $total_devices,
                'online_devices' => $online_devices,
                'recent_devices' => $recent_devices,
                'offline_devices' => $offline_devices
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '获取截图概览失败: ' . $e->getMessage()]);
    }

    exit;
}









// 处理检查截图请求（客户端调用）
if (isset($_GET['action']) && $_GET['action'] == 'check_screenshot_request') {
    header('Content-Type: application/json');

    $device_id = $_GET['device_id'] ?? '';

    if (empty($device_id)) {
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 检查是否有活跃的截图请求
        $check_sql = "SELECT id, request_time, expire_time
                     FROM `screenshot_requests`
                     WHERE `device_id` = ? AND `status` = 'active' AND `expire_time` > NOW()
                     LIMIT 1";

        $stmt = $conn->prepare($check_sql);
        $stmt->bind_param("s", $device_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            echo json_encode([
                'success' => true,
                'has_request' => true,
                'request_time' => $row['request_time'],
                'expire_time' => $row['expire_time']
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'has_request' => false
            ]);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '检查请求失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理获取截图请求
if (isset($_GET['action']) && $_GET['action'] == 'get_screenshot') {
    $device_id = $_GET['device_id'] ?? '';
    $mode = $_GET['mode'] ?? 'latest'; // latest=获取最近截图, refresh=等待刷新后截图

    if (empty($device_id)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '设备ID不能为空']);
        exit;
    }

    try {
        // 检查截图表是否存在
        $check_table_sql = "SHOW TABLES LIKE 'device_screenshots'";
        $table_check = $conn->query($check_table_sql);

        if ($table_check->num_rows == 0) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => '截图表不存在']);
            exit;
        }

        if ($mode === 'latest') {
            // 最近截图模式：直接获取数据库中最近的截图（查看桌面时使用）
            $screenshot_sql = "SELECT screenshot_data, upload_time, file_size FROM device_screenshots
                              WHERE device_id = ?
                              ORDER BY upload_time DESC LIMIT 1";
            $stmt = $conn->prepare($screenshot_sql);
            $stmt->bind_param("s", $device_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();

                // 检查截图年龄
                $upload_time = strtotime($row['upload_time']);
                $current_time = time();
                $age_seconds = $current_time - $upload_time;

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'screenshot_data' => $row['screenshot_data'],
                    'upload_time' => $row['upload_time'],
                    'file_size' => $row['file_size'],
                    'age_seconds' => $age_seconds,
                    'is_fresh' => $age_seconds <= 10,
                    'mode' => 'latest',
                    'is_historical' => $age_seconds > 60 // 超过1分钟认为是历史截图
                ]);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => '未找到该设备的截图']);
            }

            $stmt->close();
            exit;
        }

        if ($mode === 'refresh') {
            // 刷新模式：等待刷新后的最新截图
            // 首先检查是否有活跃的截图请求
            $request_check_sql = "SELECT request_time FROM screenshot_requests WHERE device_id = ? AND status = 'active' AND expire_time > NOW() LIMIT 1";
            $request_stmt = $conn->prepare($request_check_sql);
            $request_stmt->bind_param("s", $device_id);
            $request_stmt->execute();
            $request_result = $request_stmt->get_result();

            if ($request_result->num_rows == 0) {
                // 如果没有活跃请求，也返回最近的截图，但标记为无活跃请求
                $request_stmt->close();

                $screenshot_sql = "SELECT screenshot_data, upload_time, file_size FROM device_screenshots
                                  WHERE device_id = ?
                                  ORDER BY upload_time DESC LIMIT 1";
                $stmt = $conn->prepare($screenshot_sql);
                $stmt->bind_param("s", $device_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $row = $result->fetch_assoc();
                    $upload_time = strtotime($row['upload_time']);
                    $current_time = time();
                    $age_seconds = $current_time - $upload_time;

                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'screenshot_data' => $row['screenshot_data'],
                        'upload_time' => $row['upload_time'],
                        'file_size' => $row['file_size'],
                        'age_seconds' => $age_seconds,
                        'is_fresh' => false,
                        'mode' => 'refresh',
                        'no_active_request' => true,
                        'message' => '设备未在线，显示历史截图'
                    ]);
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'error' => '未找到该设备的截图']);
                }

                $stmt->close();
                exit;
            }

            $request_row = $request_result->fetch_assoc();
            $request_time = $request_row['request_time'];
            $request_stmt->close();

            // 获取请求时间之后的最新截图
            $screenshot_sql = "SELECT screenshot_data, upload_time, file_size FROM device_screenshots
                              WHERE device_id = ? AND upload_time >= ?
                              ORDER BY upload_time DESC LIMIT 1";
            $stmt = $conn->prepare($screenshot_sql);
            $stmt->bind_param("ss", $device_id, $request_time);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $row = $result->fetch_assoc();

                // 检查截图是否过期（超过10秒认为过期）
                $upload_time = strtotime($row['upload_time']);
                $current_time = time();
                $age_seconds = $current_time - $upload_time;

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'screenshot_data' => $row['screenshot_data'],
                    'upload_time' => $row['upload_time'],
                    'file_size' => $row['file_size'],
                    'age_seconds' => $age_seconds,
                    'is_fresh' => $age_seconds <= 10,
                    'mode' => 'refresh',
                    'request_time' => $request_time
                ]);
            } else {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => '等待设备上传新截图...',
                    'waiting' => true,
                    'mode' => 'refresh',
                    'request_time' => $request_time
                ]);
            }

            $stmt->close();
        }

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => '获取截图失败: ' . $e->getMessage()]);
    }

    exit;
}

// 处理解除拉黑操作
if (isset($_POST['action']) && $_POST['action'] == 'unblacklist') {
    // 检查管理黑名单权限
    require_permission('manage_blacklist');
    log_user_action('unblacklist_device', "Attempted to unblacklist device: " . ($_POST['device_id'] ?? ''));

    $device_id = $_POST['device_id'] ?? '';

    if (!empty($device_id)) {
        try {
            // 开始事务
            $conn->begin_transaction();

            // 从黑名单中移除设备（设置为非活跃状态）
            $remove_sql = "UPDATE device_blacklist SET is_active = 0, updated_at = NOW() WHERE device_id = ? AND is_active = 1";
            $remove_stmt = $conn->prepare($remove_sql);

            if ($remove_stmt === false) {
                throw new Exception("SQL准备失败: " . $conn->error);
            }

            $remove_stmt->bind_param("s", $device_id);

            if ($remove_stmt->execute()) {
                // 检查设备是否未分组
                $check_group_sql = "SELECT device_group FROM devices WHERE device_id = ?";
                $check_stmt = $conn->prepare($check_group_sql);
                $check_stmt->bind_param("s", $device_id);
                $check_stmt->execute();
                $group_result = $check_stmt->get_result();

                if ($group_result && $group_result->num_rows > 0) {
                    $group_row = $group_result->fetch_assoc();
                    $device_group = $group_row['device_group'];

                    // 如果设备未分组或分组为空，重置first_seen时间开始新的10分钟倒计时
                    if (empty($device_group) || $device_group === '未分组') {
                        $reset_time_sql = "UPDATE devices SET first_seen = NOW() WHERE device_id = ?";
                        $reset_stmt = $conn->prepare($reset_time_sql);
                        $reset_stmt->bind_param("s", $device_id);
                        $reset_stmt->execute();
                        $reset_stmt->close();

                        $message = "设备 {$device_id} 已解除拉黑，由于设备未分组，已重新开始10分钟倒计时";
                    } else {
                        $message = "设备 {$device_id} 已解除拉黑";
                    }
                } else {
                    $message = "设备 {$device_id} 已解除拉黑，但无法检查分组状态";
                }

                $check_stmt->close();

                // 提交事务
                $conn->commit();

            } else {
                throw new Exception("从黑名单移除设备失败");
            }

            $remove_stmt->close();

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "解除拉黑失败: " . $e->getMessage();
        }
    } else {
        $error = "设备ID不能为空";
    }
}

// 处理单个设备拉黑操作
if (isset($_POST['action']) && $_POST['action'] == 'blacklist') {
    // 检查管理黑名单权限
    require_permission('manage_blacklist');
    log_user_action('blacklist_device', "Attempted to blacklist device: " . ($_POST['device_id'] ?? ''));

    $device_id = $_POST['device_id'] ?? '';
    $reason = $_POST['reason'] ?? '管理员操作';
    $admin = $_POST['admin'] ?? '管理员';

    if (!empty($device_id)) {
        try {
            // 检查设备是否已在活跃黑名单中
            $check_sql = "SELECT device_id, blacklist_end_time FROM device_blacklist WHERE device_id = ? AND is_active = 1";
            $check_stmt = $conn->prepare($check_sql);

            if ($check_stmt === false) {
                $error = "SQL准备失败: " . $conn->error;
            } else {
                $check_stmt->bind_param("s", $device_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    $error = "设备 {$device_id} 已在黑名单中";
                } else {
                    // 设备不在黑名单中，可以添加到黑名单
                    $blacklist_sql = "INSERT INTO device_blacklist (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, updated_at)
                                     VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, NOW())";
                    $blacklist_stmt = $conn->prepare($blacklist_sql);

                    if ($blacklist_stmt === false) {
                        $error = "SQL准备失败: " . $conn->error;
                    } else {
                        // 获取设备型号（如果有的话）
                        $device_model = "手动拉黑设备";
                        $full_reason = "管理员手动拉黑 - " . $reason . " (操作员: " . $admin . ")";

                        $blacklist_stmt->bind_param("sss", $device_id, $device_model, $full_reason);

                        if ($blacklist_stmt->execute()) {
                            $message = "设备 {$device_id} 已成功加入黑名单（24小时）";
                        } else {
                            $error = "拉黑设备 {$device_id} 失败: " . $blacklist_stmt->error;
                        }

                        $blacklist_stmt->close();
                    }
                }
            }

            if ($check_stmt) {
                $check_stmt->close();
            }

        } catch (Exception $e) {
            $error = "拉黑操作失败: " . $e->getMessage();
        }
    } else {
        $error = "设备ID不能为空";
    }
}

// 处理批量分组操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_group') {
    // 检查管理分组权限
    require_permission('manage_groups');
    log_user_action('batch_group_devices', "Attempted batch group operation");

    $device_ids = $_POST['device_ids'] ?? '';
    $group_name = $_POST['group_name'] ?? '';

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;

        // 开始事务
        $conn->begin_transaction();

        try {
            // 如果分组名为空，设置为NULL
            $group_value = empty($group_name) ? NULL : $group_name;

            // 如果是新分组且不为空，先确保分组存在于device_groups表中
            if (!empty($group_name)) {
                // 创建分组记录表（如果不存在）
                $create_groups_table = "CREATE TABLE IF NOT EXISTS device_groups (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    group_name VARCHAR(100) NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                if (!$conn->query($create_groups_table)) {
                    throw new Exception("创建分组表失败: " . $conn->error);
                }

                $check_group_sql = "SELECT COUNT(*) as count FROM device_groups WHERE group_name = ?";
                $check_stmt = $conn->prepare($check_group_sql);
                $check_stmt->bind_param("s", $group_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $group_exists = $check_result->fetch_assoc()['count'] > 0;
                $check_stmt->close();

                if (!$group_exists) {
                    // 创建新分组
                    $create_group_sql = "INSERT INTO device_groups (group_name) VALUES (?)";
                    $create_stmt = $conn->prepare($create_group_sql);
                    $create_stmt->bind_param("s", $group_name);
                    if (!$create_stmt->execute()) {
                        throw new Exception("创建分组失败: " . $create_stmt->error);
                    }
                    $create_stmt->close();
                }
            }

            // 修复方法1：使用循环逐个更新设备分组
            foreach ($ids as $device_id) {
                $device_id = trim($device_id);
                if (empty($device_id)) continue;

                $update_sql = "UPDATE devices SET device_group = ? WHERE device_id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("ss", $group_value, $device_id);

                if ($stmt->execute() && $stmt->affected_rows > 0) {
                    $success_count++;

                    // 同步更新粉丝记录表中的设备分组
                    $update_fans_sql = "UPDATE device_fans_records SET device_group = ? WHERE device_id = ?";
                    $fans_stmt = $conn->prepare($update_fans_sql);
                    if ($fans_stmt) {
                        $fans_stmt->bind_param("ss", $group_value, $device_id);
                        $fans_stmt->execute();
                        $fans_stmt->close();
                    }
                }
                $stmt->close();
            }

            // 提交事务
            $conn->commit();

            if ($success_count > 0) {
                $group_display = empty($group_name) ? '无分组' : $group_name;
                $message = "已成功将 {$success_count} 个设备设置为「{$group_display}」";
            } else {
                $error = "批量分组操作失败，请检查设备ID是否正确或设备是否已在该分组中";
            }

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "批量分组操作失败: " . $e->getMessage();
            error_log("批量分组异常: " . $e->getMessage());
        }
    } else {
        $error = "请选择要分组的设备";
    }
}

// 处理批量拉黑操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_blacklist') {
    // 检查管理黑名单权限
    require_permission('manage_blacklist');
    log_user_action('batch_blacklist_devices', "Attempted batch blacklist operation");

    $device_ids = $_POST['device_ids'] ?? '';
    $reason = $_POST['reason'] ?? '批量管理员操作';
    $admin = $_POST['admin'] ?? '管理员';

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;

        foreach ($ids as $device_id) {
            $device_id = trim($device_id);
            if (empty($device_id)) continue;

            // 添加每个设备到黑名单（使用新的表结构）
            $device_model = "批量拉黑设备";
            $full_reason = "批量管理员操作 - " . $reason . " (操作员: " . $admin . ")";

            $blacklist_sql = "INSERT INTO device_blacklist (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, updated_at)
                             VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, NOW())
                             ON DUPLICATE KEY UPDATE
                             blacklist_reason = VALUES(blacklist_reason),
                             blacklist_start_time = VALUES(blacklist_start_time),
                             blacklist_end_time = VALUES(blacklist_end_time),
                             is_active = 1,
                             updated_at = VALUES(updated_at)";

            $stmt = $conn->prepare($blacklist_sql);
            $stmt->bind_param("sss", $device_id, $device_model, $full_reason);

            if ($stmt->execute()) {
                $success_count++;
            }

            $stmt->close();
        }

        $message = "已成功拉黑 {$success_count} 个设备";
    }
}

// 处理批量解除拉黑操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_remove_blacklist') {
    // 检查管理黑名单权限
    require_permission('manage_blacklist');
    log_user_action('batch_remove_blacklist_devices', "Attempted batch remove blacklist operation");

    $device_ids = $_POST['device_ids'] ?? '';

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;
        $failed_devices = [];

        foreach ($ids as $device_id) {
            $device_id = trim($device_id);
            if (empty($device_id)) continue;

            // 检查设备是否在活跃黑名单中
            $check_sql = "SELECT id FROM device_blacklist WHERE device_id = ? AND is_active = 1";
            $check_stmt = $conn->prepare($check_sql);

            if ($check_stmt) {
                $check_stmt->bind_param("s", $device_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    // 设备在黑名单中，执行解除操作
                    $remove_sql = "UPDATE device_blacklist SET is_active = 0, updated_at = NOW() WHERE device_id = ? AND is_active = 1";
                    $remove_stmt = $conn->prepare($remove_sql);

                    if ($remove_stmt) {
                        $remove_stmt->bind_param("s", $device_id);
                        if ($remove_stmt->execute() && $remove_stmt->affected_rows > 0) {
                            $success_count++;
                        } else {
                            $failed_devices[] = $device_id;
                        }
                        $remove_stmt->close();
                    } else {
                        $failed_devices[] = $device_id;
                    }
                } else {
                    // 设备不在活跃黑名单中
                    $failed_devices[] = $device_id . "(不在黑名单中)";
                }

                $check_stmt->close();
            } else {
                $failed_devices[] = $device_id . "(查询失败)";
            }
        }

        if ($success_count > 0) {
            $message = "已成功解除 {$success_count} 个设备的拉黑状态";
            if (!empty($failed_devices)) {
                $message .= "，失败 " . count($failed_devices) . " 个";
            }
        } else {
            $error = "批量解除拉黑失败：" . (empty($failed_devices) ? "未找到可解除的设备" : "所有设备操作失败");
        }
    } else {
        $error = "未选择要解除拉黑的设备";
    }
}

// 处理删除分组操作
if (isset($_POST['action']) && $_POST['action'] == 'delete_group') {
    // 检查管理分组权限
    require_permission('manage_groups');
    log_user_action('delete_group', "Attempted to delete group: " . ($_POST['group_name'] ?? ''));

    $group_name = $_POST['group_name'] ?? '';

    if (!empty($group_name)) {
        // 开始事务
        $conn->begin_transaction();

        try {
            // 更新所有该分组下的设备为无分组
            $update_devices_sql = "UPDATE devices SET device_group = NULL WHERE device_group = ?";
            $stmt = $conn->prepare($update_devices_sql);
            $stmt->bind_param("s", $group_name);
            $stmt->execute();
            $affected_rows = $stmt->affected_rows;
            $stmt->close();

            // 同步更新粉丝记录表中的设备分组
            $update_fans_sql = "UPDATE device_fans_records SET device_group = NULL WHERE device_group = ?";
            $fans_stmt = $conn->prepare($update_fans_sql);
            if ($fans_stmt) {
                $fans_stmt->bind_param("s", $group_name);
                $fans_stmt->execute();
                $fans_stmt->close();
            }

            // 从 device_groups 表中删除分组记录
            $delete_from_groups_sql = "DELETE FROM device_groups WHERE group_name = ?";
            $stmt = $conn->prepare($delete_from_groups_sql);
            $stmt->bind_param("s", $group_name);
            $stmt->execute();
            $stmt->close();

            // 提交事务
            $conn->commit();
            $message = "已删除分组「{$group_name}」，共有 {$affected_rows} 个设备被移出该分组";

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "删除分组失败: " . $e->getMessage();
            error_log("删除分组异常: " . $e->getMessage());
        }
    } else {
        $error = "请选择要删除的分组";
    }

    // 操作完成后重定向
    $redirect_url = "monitor.php";
    $params = [];
    if (!empty($_GET['search'])) $params[] = "search=" . urlencode($_GET['search']);
    if (!empty($_GET['group'])) $params[] = "group=" . urlencode($_GET['group']);
    if (isset($_GET['blacklist'])) $params[] = "blacklist=" . urlencode($_GET['blacklist']);
    if (!empty($_GET['page'])) $params[] = "page=" . urlencode($_GET['page']);

    if (!empty($params)) {
        $redirect_url .= "?" . implode("&", $params);
    }

    header("Location: $redirect_url");
    exit;
}

// 处理单个设备分组操作
if (isset($_POST['action']) && $_POST['action'] == 'assign_group') {
    // 检查管理分组权限
    require_permission('manage_groups');
    log_user_action('assign_device_group', "Attempted to assign device to group: " . ($_POST['device_id'] ?? ''));

    $device_id = $_POST['device_id'] ?? '';
    $group_name = $_POST['group_name'] ?? '';

    if (!empty($device_id)) {
        // 开始事务
        $conn->begin_transaction();

        try {
            // 如果分组名为空，设置为NULL
            $group_value = empty($group_name) ? NULL : $group_name;

            // 如果是新分组且不为空，先确保分组存在于device_groups表中
            if (!empty($group_name)) {
                // 创建分组记录表（如果不存在）
                $create_groups_table = "CREATE TABLE IF NOT EXISTS device_groups (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    group_name VARCHAR(100) NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                if (!$conn->query($create_groups_table)) {
                    throw new Exception("创建分组表失败: " . $conn->error);
                }

                $check_group_sql = "SELECT COUNT(*) as count FROM device_groups WHERE group_name = ?";
                $check_stmt = $conn->prepare($check_group_sql);
                $check_stmt->bind_param("s", $group_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $group_exists = $check_result->fetch_assoc()['count'] > 0;
                $check_stmt->close();

                if (!$group_exists) {
                    // 创建新分组
                    $create_group_sql = "INSERT INTO device_groups (group_name) VALUES (?)";
                    $create_stmt = $conn->prepare($create_group_sql);
                    $create_stmt->bind_param("s", $group_name);
                    if (!$create_stmt->execute()) {
                        throw new Exception("创建分组失败: " . $create_stmt->error);
                    }
                    $create_stmt->close();
                }
            }

            // 更新设备的分组
            $group_sql = "UPDATE devices SET device_group = ? WHERE device_id = ?";
            $stmt = $conn->prepare($group_sql);
            $stmt->bind_param("ss", $group_value, $device_id);

            if ($stmt->execute() && $stmt->affected_rows > 0) {
                $group_display = empty($group_name) ? '无分组' : $group_name;
                $message = "设备已设置为「{$group_display}」";

                // 同步更新粉丝记录表中的设备分组
                $update_fans_sql = "UPDATE device_fans_records SET device_group = ? WHERE device_id = ?";
                $fans_stmt = $conn->prepare($update_fans_sql);
                if ($fans_stmt) {
                    $fans_stmt->bind_param("ss", $group_value, $device_id);
                    $fans_stmt->execute();
                    $fans_stmt->close();
                }
            } else {
                $error = "更新设备分组失败，请检查设备ID是否存在";
            }
            $stmt->close();

            // 提交事务
            $conn->commit();

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "设备分组失败: " . $e->getMessage();
        }
    } else {
        $error = "设备ID不能为空";
    }

    // 操作完成后重定向，避免重复提交
    $redirect_url = "monitor.php";
    if (!empty($_GET['search'])) $redirect_url .= "?search=" . urlencode($_GET['search']);
    if (!empty($_GET['group'])) $redirect_url .= (strpos($redirect_url, '?') ? '&' : '?') . "group=" . urlencode($_GET['group']);
    if (isset($_GET['blacklist'])) $redirect_url .= (strpos($redirect_url, '?') ? '&' : '?') . "blacklist=" . urlencode($_GET['blacklist']);
    if (!empty($_GET['page'])) $redirect_url .= (strpos($redirect_url, '?') ? '&' : '?') . "page=" . urlencode($_GET['page']);

    header("Location: $redirect_url");
    exit;
}

// 处理创建分组操作
if (isset($_POST['action']) && $_POST['action'] == 'create_group') {
    // 检查管理分组权限
    require_permission('manage_groups');
    log_user_action('create_group', "Attempted to create group: " . ($_POST['group_name'] ?? ''));

    $group_name = trim($_POST['group_name'] ?? '');
    $description = trim($_POST['description'] ?? '');

    if (!empty($group_name)) {
        // 验证分组名称
        if (strlen($group_name) > 100) {
            $error = "分组名称不能超过100个字符";
        } elseif (preg_match('/[<>:"\/\\\\|?*]/', $group_name)) {
            $error = "分组名称不能包含特殊字符：< > : \" / \\ | ? *";
        } else {
            // 开始事务
            $conn->begin_transaction();

            try {
                // 创建分组记录表（如果不存在）
                $create_groups_table = "CREATE TABLE IF NOT EXISTS device_groups (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    group_name VARCHAR(100) NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT,
                    updated_at DATETIME DEFAULT NULL,
                    INDEX idx_group_name (group_name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                if (!$conn->query($create_groups_table)) {
                    throw new Exception("创建分组表失败: " . $conn->error);
                }

                // 检查分组是否已存在
                $check_sql = "SELECT COUNT(*) as count FROM device_groups WHERE group_name = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("s", $group_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $group_exists = $check_result->fetch_assoc()['count'] > 0;
                $check_stmt->close();

                if ($group_exists) {
                    throw new Exception("分组「{$group_name}」已存在，无需重复创建");
                }

                // 插入新分组（手动设置updated_at）
                $insert_group_sql = "INSERT INTO device_groups (group_name, description, updated_at) VALUES (?, ?, NOW())";
                $stmt = $conn->prepare($insert_group_sql);
                $stmt->bind_param("ss", $group_name, $description);

                if (!$stmt->execute()) {
                    throw new Exception("创建分组失败: " . $stmt->error);
                }
                $stmt->close();

                // 提交事务
                $conn->commit();

                $desc_text = !empty($description) ? "，描述：{$description}" : "";
                $message = "已成功创建分组「{$group_name}」{$desc_text}";

            } catch (Exception $e) {
                // 回滚事务
                $conn->rollback();
                $error = $e->getMessage();
                error_log("创建分组异常: " . $e->getMessage());
            }
        }
    } else {
        $error = "分组名称不能为空";
    }

    // 操作完成后重定向，保持当前页面状态
    $redirect_url = "monitor.php";
    $params = [];
    if (!empty($_GET['search'])) $params[] = "search=" . urlencode($_GET['search']);
    if (!empty($_GET['group'])) $params[] = "group=" . urlencode($_GET['group']);
    if (isset($_GET['blacklist'])) $params[] = "blacklist=" . urlencode($_GET['blacklist']);
    if (!empty($_GET['page'])) $params[] = "page=" . urlencode($_GET['page']);

    if (!empty($params)) {
        $redirect_url .= "?" . implode("&", $params);
    }

    header("Location: $redirect_url");
    exit;
}

// 处理编辑分组操作
if (isset($_POST['action']) && $_POST['action'] == 'edit_group') {
    // 检查管理分组权限
    require_permission('manage_groups');
    log_user_action('edit_group', "Attempted to edit group: " . ($_POST['original_group_name'] ?? ''));

    $original_group_name = trim($_POST['original_group_name'] ?? '');
    $new_group_name = trim($_POST['group_name'] ?? '');
    $new_description = trim($_POST['group_description'] ?? '');

    if (!empty($original_group_name) && !empty($new_group_name)) {
        // 验证新分组名称
        if (strlen($new_group_name) > 50) {
            $error = "分组名称不能超过50个字符";
        } elseif (preg_match('/[<>:"\/\\\\|?*]/', $new_group_name)) {
            $error = "分组名称不能包含特殊字符：< > : \" / \\ | ? *";
        } else {
            // 开始事务
            $conn->begin_transaction();

            try {
                // 检查新分组名称是否已存在（如果名称有变化）
                if ($original_group_name !== $new_group_name) {
                    $check_sql = "SELECT COUNT(*) as count FROM device_groups WHERE group_name = ?";
                    $check_stmt = $conn->prepare($check_sql);
                    $check_stmt->bind_param("s", $new_group_name);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();
                    $exists = $check_result->fetch_assoc()['count'] > 0;
                    $check_stmt->close();

                    if ($exists) {
                        throw new Exception("分组名称 '{$new_group_name}' 已存在");
                    }
                }

                // 更新分组信息（手动设置updated_at）
                $update_group_sql = "UPDATE device_groups SET group_name = ?, description = ?, updated_at = NOW() WHERE group_name = ?";
                $update_stmt = $conn->prepare($update_group_sql);
                $update_stmt->bind_param("sss", $new_group_name, $new_description, $original_group_name);

                if (!$update_stmt->execute()) {
                    throw new Exception("更新分组信息失败");
                }
                $update_stmt->close();

                // 如果分组名称有变化，更新设备表中的分组引用
                if ($original_group_name !== $new_group_name) {
                    $update_devices_sql = "UPDATE devices SET device_group = ? WHERE device_group = ?";
                    $update_devices_stmt = $conn->prepare($update_devices_sql);
                    $update_devices_stmt->bind_param("ss", $new_group_name, $original_group_name);

                    if (!$update_devices_stmt->execute()) {
                        throw new Exception("更新设备分组引用失败");
                    }
                    $update_devices_stmt->close();

                    // 同步更新粉丝记录表中的设备分组
                    $update_fans_sql = "UPDATE device_fans_records SET device_group = ? WHERE device_group = ?";
                    $fans_stmt = $conn->prepare($update_fans_sql);
                    if ($fans_stmt) {
                        $fans_stmt->bind_param("ss", $new_group_name, $original_group_name);
                        $fans_stmt->execute();
                        $fans_stmt->close();
                    }
                }

                // 提交事务
                $conn->commit();
                $message = "分组 '{$original_group_name}' 已成功更新为 '{$new_group_name}'";

            } catch (Exception $e) {
                // 回滚事务
                $conn->rollback();
                $error = "编辑分组失败: " . $e->getMessage();
            }
        }
    } else {
        $error = "原分组名称和新分组名称都不能为空";
    }

    // 操作完成后重定向
    $redirect_url = "monitor.php";
    $params = [];
    if (!empty($_GET['search'])) $params[] = "search=" . urlencode($_GET['search']);
    if (!empty($_GET['group'])) $params[] = "group=" . urlencode($_GET['group']);
    if (isset($_GET['blacklist'])) $params[] = "blacklist=" . urlencode($_GET['blacklist']);
    if (!empty($_GET['page'])) $params[] = "page=" . urlencode($_GET['page']);

    if (!empty($params)) {
        $redirect_url .= "?" . implode("&", $params);
    }

    header("Location: $redirect_url");
    exit;
}

// 处理批量删除操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_delete') {
    // 检查管理设备权限
    require_permission('manage_devices');
    log_user_action('batch_delete_devices', "Attempted batch delete operation");

    $device_ids = $_POST['device_ids'] ?? '';

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;

        // 开始事务
        $conn->begin_transaction();

        try {
            foreach ($ids as $device_id) {
                $device_id = trim($device_id);
                if (empty($device_id)) continue;

                // 从黑名单中删除
                $delete_blacklist_sql = "DELETE FROM device_blacklist WHERE device_id = ?";
                $stmt = $conn->prepare($delete_blacklist_sql);
                $stmt->bind_param("s", $device_id);
                $stmt->execute();
                $stmt->close();

                // 从设备表中删除
                $delete_device_sql = "DELETE FROM devices WHERE device_id = ?";
                $stmt = $conn->prepare($delete_device_sql);
                $stmt->bind_param("s", $device_id);

                if ($stmt->execute() && $stmt->affected_rows > 0) {
                    $success_count++;
                }
                $stmt->close();
            }

            // 提交事务
            $conn->commit();
            $message = "已成功删除 {$success_count} 个设备的所有信息";

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "批量删除失败: " . $e->getMessage();
        }
    } else {
        $error = "请选择要删除的设备";
    }
}

// 处理心跳监控批量删除设备操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_delete_heartbeat_devices') {
    // 检查管理设备权限
    require_permission('manage_devices');
    log_user_action('batch_delete_heartbeat_devices', "Attempted batch delete heartbeat devices");

    $device_ids = trim($_POST['device_ids'] ?? '');

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;

        // 开始事务
        $conn->begin_transaction();

        try {
            foreach ($ids as $device_id) {
                $device_id = trim($device_id);
                if (empty($device_id)) continue;

                // 从心跳表中删除
                $delete_heartbeat_sql = "DELETE FROM device_heartbeat WHERE device_id = ?";
                $stmt = $conn->prepare($delete_heartbeat_sql);
                $stmt->bind_param("s", $device_id);
                $stmt->execute();
                $stmt->close();

                // 从黑名单中删除
                $delete_blacklist_sql = "DELETE FROM device_blacklist WHERE device_id = ?";
                $stmt = $conn->prepare($delete_blacklist_sql);
                $stmt->bind_param("s", $device_id);
                $stmt->execute();
                $stmt->close();

                // 从设备表中删除
                $delete_device_sql = "DELETE FROM devices WHERE device_id = ?";
                $stmt = $conn->prepare($delete_device_sql);
                $stmt->bind_param("s", $device_id);

                if ($stmt->execute()) {
                    $success_count++;
                }
                $stmt->close();
            }

            // 提交事务
            $conn->commit();
            $message = "已成功删除 {$success_count} 个设备的所有信息（包括心跳记录）";

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "批量删除失败: " . $e->getMessage();
        }
    } else {
        $error = "未选择要删除的设备";
    }
}

// 处理心跳监控批量拉黑设备操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_blacklist_heartbeat_devices') {
    // 检查管理黑名单权限
    require_permission('manage_blacklist');
    log_user_action('batch_blacklist_heartbeat_devices', "Attempted batch blacklist heartbeat devices");

    $device_ids = trim($_POST['device_ids'] ?? '');
    $blacklist_reason = trim($_POST['blacklist_reason'] ?? '手动拉黑');

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;

        // 开始事务
        $conn->begin_transaction();

        try {
            foreach ($ids as $device_id) {
                $device_id = trim($device_id);
                if (empty($device_id)) continue;

                // 检查设备是否已在活跃黑名单中
                $check_sql = "SELECT COUNT(*) as count FROM device_blacklist WHERE device_id = ? AND is_active = 1";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("s", $device_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                $is_blacklisted = $check_result->fetch_assoc()['count'] > 0;
                $check_stmt->close();

                if (!$is_blacklisted) {
                    // 添加到黑名单（使用新的表结构）
                    $device_model = "心跳监控拉黑设备";
                    $full_reason = "心跳监控批量操作 - " . $blacklist_reason;

                    $blacklist_sql = "INSERT INTO device_blacklist (device_id, device_model, blacklist_reason, trigger_count, blacklist_start_time, blacklist_end_time, blacklist_duration_hours, is_active, updated_at)
                                     VALUES (?, ?, ?, 0, NOW(), DATE_ADD(NOW(), INTERVAL 100 YEAR), 876000, 1, NOW())
                                     ON DUPLICATE KEY UPDATE
                                     blacklist_reason = VALUES(blacklist_reason),
                                     blacklist_start_time = VALUES(blacklist_start_time),
                                     blacklist_end_time = VALUES(blacklist_end_time),
                                     is_active = 1,
                                     updated_at = VALUES(updated_at)";
                    $stmt = $conn->prepare($blacklist_sql);
                    $stmt->bind_param("sss", $device_id, $device_model, $full_reason);

                    if ($stmt->execute()) {
                        $success_count++;
                    }
                    $stmt->close();
                }
            }

            // 提交事务
            $conn->commit();
            $message = "已成功拉黑 {$success_count} 个设备";

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "批量拉黑失败: " . $e->getMessage();
        }
    } else {
        $error = "未选择要拉黑的设备";
    }
}

// 处理心跳监控批量解除拉黑操作
if (isset($_POST['action']) && $_POST['action'] == 'batch_remove_blacklist_heartbeat_devices') {
    // 检查管理黑名单权限
    require_permission('manage_blacklist');
    log_user_action('batch_remove_blacklist_heartbeat_devices', "Attempted batch remove blacklist heartbeat devices");

    $device_ids = trim($_POST['device_ids'] ?? '');

    if (!empty($device_ids)) {
        $ids = explode(',', $device_ids);
        $success_count = 0;
        $failed_devices = [];

        // 开始事务
        $conn->begin_transaction();

        try {
            foreach ($ids as $device_id) {
                $device_id = trim($device_id);
                if (empty($device_id)) continue;

                // 检查设备是否在活跃黑名单中
                $check_sql = "SELECT id FROM device_blacklist WHERE device_id = ? AND is_active = 1";
                $check_stmt = $conn->prepare($check_sql);

                if ($check_stmt) {
                    $check_stmt->bind_param("s", $device_id);
                    $check_stmt->execute();
                    $check_result = $check_stmt->get_result();

                    if ($check_result->num_rows > 0) {
                        // 设备在黑名单中，执行解除操作
                        $remove_sql = "UPDATE device_blacklist SET is_active = 0, updated_at = NOW() WHERE device_id = ? AND is_active = 1";
                        $remove_stmt = $conn->prepare($remove_sql);

                        if ($remove_stmt) {
                            $remove_stmt->bind_param("s", $device_id);
                            if ($remove_stmt->execute() && $remove_stmt->affected_rows > 0) {
                                $success_count++;
                            } else {
                                $failed_devices[] = $device_id;
                            }
                            $remove_stmt->close();
                        } else {
                            $failed_devices[] = $device_id;
                        }
                    } else {
                        // 设备不在活跃黑名单中
                        $failed_devices[] = $device_id . "(不在黑名单中)";
                    }

                    $check_stmt->close();
                } else {
                    $failed_devices[] = $device_id . "(查询失败)";
                }
            }

            // 提交事务
            $conn->commit();

            if ($success_count > 0) {
                $message = "已成功解除 {$success_count} 个设备的拉黑状态";
                if (!empty($failed_devices)) {
                    $message .= "，失败 " . count($failed_devices) . " 个";
                }
            } else {
                $error = "批量解除拉黑失败：" . (empty($failed_devices) ? "未找到可解除的设备" : "所有设备操作失败");
            }

        } catch (Exception $e) {
            // 回滚事务
            $conn->rollback();
            $error = "批量解除拉黑失败: " . $e->getMessage();
        }
    } else {
        $error = "未选择要解除拉黑的设备";
    }
}

// 处理获取粉丝记录分组请求
if (isset($_GET['action']) && $_GET['action'] == 'get_fans_groups') {
    try {
        // 确保粉丝记录表存在
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `device_fans_records` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` varchar(191) NOT NULL COMMENT '设备ID',
            `device_model` varchar(191) DEFAULT NULL COMMENT '设备型号',
            `android_version` varchar(50) DEFAULT NULL COMMENT 'Android版本',
            `device_group` varchar(191) DEFAULT NULL COMMENT '设备分组',
            `fans_count` int(11) NOT NULL COMMENT '粉丝数量',
            `record_date` date NOT NULL COMMENT '记录日期',
            `record_time` datetime NOT NULL COMMENT '记录时间',
            `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_device_date` (`device_id`, `record_date`) COMMENT '每个设备每天只能有一条记录',
            KEY `idx_device_id` (`device_id`),
            KEY `idx_record_date` (`record_date`),
            KEY `idx_device_group` (`device_group`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备粉丝数量记录表'";

        $conn->query($create_table_sql);

        // 获取所有分组
        $groups_sql = "SELECT DISTINCT device_group FROM device_fans_records WHERE device_group IS NOT NULL AND device_group != '' ORDER BY device_group";
        $groups_result = $conn->query($groups_sql);

        $groups = [];
        if ($groups_result && $groups_result->num_rows > 0) {
            while ($row = $groups_result->fetch_assoc()) {
                $groups[] = $row['device_group'];
            }
        }

        echo json_encode(['success' => true, 'groups' => $groups]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// 处理获取粉丝记录请求
if (isset($_GET['action']) && $_GET['action'] == 'get_fans_records') {
    try {
        // 确保粉丝记录表存在
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `device_fans_records` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` varchar(191) NOT NULL COMMENT '设备ID',
            `device_model` varchar(191) DEFAULT NULL COMMENT '设备型号',
            `android_version` varchar(50) DEFAULT NULL COMMENT 'Android版本',
            `device_group` varchar(191) DEFAULT NULL COMMENT '设备分组',
            `fans_count` int(11) NOT NULL COMMENT '粉丝数量',
            `record_date` date NOT NULL COMMENT '记录日期',
            `record_time` datetime NOT NULL COMMENT '记录时间',
            `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_device_date` (`device_id`, `record_date`) COMMENT '每个设备每天只能有一条记录',
            KEY `idx_device_id` (`device_id`),
            KEY `idx_record_date` (`record_date`),
            KEY `idx_device_group` (`device_group`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备粉丝数量记录表'";

        if (!$conn->query($create_table_sql)) {
            throw new Exception("创建粉丝记录表失败: " . $conn->error);
        }

        // 获取筛选参数
        $device_filter = $_GET['device_filter'] ?? '';
        $group_filter = $_GET['group_filter'] ?? '';
        $date_start = $_GET['date_start'] ?? '';
        $date_end = $_GET['date_end'] ?? '';

        // 构建查询条件
        $where_conditions = [];
        $params = [];
        $types = '';

        if (!empty($device_filter)) {
            $where_conditions[] = "(device_id LIKE ? OR device_model LIKE ?)";
            $params[] = "%$device_filter%";
            $params[] = "%$device_filter%";
            $types .= 'ss';
        }

        if (!empty($group_filter)) {
            $where_conditions[] = "device_group = ?";
            $params[] = $group_filter;
            $types .= 's';
        }

        if (!empty($date_start)) {
            $where_conditions[] = "record_date >= ?";
            $params[] = $date_start;
            $types .= 's';
        }

        if (!empty($date_end)) {
            $where_conditions[] = "record_date <= ?";
            $params[] = $date_end;
            $types .= 's';
        }

        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }

        // 获取统计数据（移除平均粉丝数）
        $stats_sql = "SELECT
                        COUNT(DISTINCT device_id) as total_devices,
                        MAX(fans_count) as max_fans
                      FROM device_fans_records $where_clause";

        $stats_stmt = $conn->prepare($stats_sql);
        if (!$stats_stmt) {
            throw new Exception("准备统计SQL语句失败: " . $conn->error);
        }

        if (!empty($params)) {
            $stats_stmt->bind_param($types, ...$params);
        }

        if (!$stats_stmt->execute()) {
            throw new Exception("执行统计查询失败: " . $stats_stmt->error);
        }

        $stats_result = $stats_stmt->get_result();
        $stats = $stats_result->fetch_assoc();
        $stats_stmt->close();

        // 获取每个设备的最新粉丝记录和昨日粉丝记录
        $records_sql = "SELECT
                           dfr_today.*,
                           COALESCE(dfr_yesterday.fans_count, 0) as yesterday_fans_count
                       FROM (
                           SELECT dfr1.*
                           FROM device_fans_records dfr1
                           INNER JOIN (
                               SELECT device_id, MAX(record_date) as max_date, MAX(record_time) as max_time
                               FROM device_fans_records dfr2
                               $where_clause
                               GROUP BY device_id
                           ) dfr_max ON dfr1.device_id = dfr_max.device_id
                                     AND dfr1.record_date = dfr_max.max_date
                                     AND dfr1.record_time = dfr_max.max_time
                       ) dfr_today
                       LEFT JOIN device_fans_records dfr_yesterday
                           ON dfr_today.device_id = dfr_yesterday.device_id
                           AND dfr_yesterday.record_date = DATE_SUB(dfr_today.record_date, INTERVAL 1 DAY)
                       ORDER BY dfr_today.record_date DESC, dfr_today.record_time DESC
                       LIMIT 1000";

        $records_stmt = $conn->prepare($records_sql);
        if (!$records_stmt) {
            throw new Exception("准备记录SQL语句失败: " . $conn->error);
        }

        if (!empty($params)) {
            $records_stmt->bind_param($types, ...$params);
        }

        if (!$records_stmt->execute()) {
            throw new Exception("执行记录查询失败: " . $records_stmt->error);
        }

        $records_result = $records_stmt->get_result();

        $records = [];
        if ($records_result && $records_result->num_rows > 0) {
            while ($row = $records_result->fetch_assoc()) {
                $today_fans = intval($row['fans_count']);
                $yesterday_fans = intval($row['yesterday_fans_count']);
                $fans_change = $today_fans - $yesterday_fans;

                $records[] = [
                    'device_id' => $row['device_id'],
                    'device_model' => $row['device_model'],
                    'device_group' => $row['device_group'],
                    'fans_count' => $today_fans,
                    'yesterday_fans_count' => $yesterday_fans,
                    'fans_change' => $fans_change,
                    'record_date' => $row['record_date'],
                    'record_time' => $row['record_time']
                ];
            }
        }
        $records_stmt->close();

        echo json_encode([
            'success' => true,
            'stats' => [
                'total_devices' => intval($stats['total_devices']),
                'max_fans' => intval($stats['max_fans'] ?? 0)
            ],
            'records' => $records
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// 处理获取设备历史粉丝记录请求
if (isset($_GET['action']) && $_GET['action'] == 'get_device_fans_history') {
    try {
        $device_id = $_GET['device_id'] ?? '';

        if (empty($device_id)) {
            throw new Exception("设备ID不能为空");
        }

        // 获取该设备的所有历史粉丝记录
        $history_sql = "SELECT * FROM device_fans_records
                       WHERE device_id = ?
                       ORDER BY record_date DESC, record_time DESC";

        $history_stmt = $conn->prepare($history_sql);
        if (!$history_stmt) {
            throw new Exception("准备历史记录SQL语句失败: " . $conn->error);
        }

        $history_stmt->bind_param("s", $device_id);

        if (!$history_stmt->execute()) {
            throw new Exception("执行历史记录查询失败: " . $history_stmt->error);
        }

        $history_result = $history_stmt->get_result();

        $history_records = [];
        if ($history_result && $history_result->num_rows > 0) {
            while ($row = $history_result->fetch_assoc()) {
                $history_records[] = [
                    'device_id' => $row['device_id'],
                    'device_model' => $row['device_model'],
                    'device_group' => $row['device_group'],
                    'fans_count' => intval($row['fans_count']),
                    'record_date' => $row['record_date'],
                    'record_time' => $row['record_time'],
                    'ip_address' => $row['ip_address']
                ];
            }
        }
        $history_stmt->close();

        echo json_encode([
            'success' => true,
            'device_id' => $device_id,
            'history_records' => $history_records
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// 处理获取今日任务记录请求
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_daily_tasks') {
    header('Content-Type: application/json; charset=utf-8');

    try {
        // 确保今日任务表存在（适配MySQL 5.5.62）
        $create_table_sql = "CREATE TABLE IF NOT EXISTS `device_daily_tasks` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` varchar(255) NOT NULL,
            `device_model` varchar(255) DEFAULT NULL,
            `android_version` varchar(50) DEFAULT NULL,
            `device_group` varchar(100) DEFAULT NULL,
            `likes_target` int(11) NOT NULL DEFAULT 0,
            `comments_target` int(11) NOT NULL DEFAULT 0,
            `recommendations_target` int(11) NOT NULL DEFAULT 0,
            `record_date` date NOT NULL,
            `record_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `ip_address` varchar(45) DEFAULT NULL,
            `created_at` datetime DEFAULT NULL,
            `updated_at` datetime DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_device_date` (`device_id`, `record_date`),
            KEY `idx_device_id` (`device_id`),
            KEY `idx_record_date` (`record_date`),
            KEY `idx_device_group` (`device_group`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci";

        if (!$conn->query($create_table_sql)) {
            throw new Exception("创建今日任务表失败: " . $conn->error);
        }

        // 构建筛选条件
        $where_conditions = [];
        $params = [];
        $types = '';

        if (!empty($_GET['device_filter'])) {
            $where_conditions[] = "(device_id LIKE ? OR device_model LIKE ?)";
            $filter_value = '%' . $_GET['device_filter'] . '%';
            $params[] = $filter_value;
            $params[] = $filter_value;
            $types .= 'ss';
        }

        if (!empty($_GET['group_filter'])) {
            if ($_GET['group_filter'] === '未分组') {
                $where_conditions[] = "(device_group IS NULL OR device_group = '' OR device_group = '未分组')";
            } else {
                $where_conditions[] = "device_group = ?";
                $params[] = $_GET['group_filter'];
                $types .= 's';
            }
        }

        if (!empty($_GET['date_filter'])) {
            $where_conditions[] = "record_date = ?";
            $params[] = $_GET['date_filter'];
            $types .= 's';
        }

        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }

        // 获取每个设备的最新今日任务记录
        $tasks_sql = "SELECT ddt1.*
                      FROM device_daily_tasks ddt1
                      INNER JOIN (
                          SELECT device_id, MAX(record_date) as max_date, MAX(record_time) as max_time
                          FROM device_daily_tasks ddt2
                          $where_clause
                          GROUP BY device_id
                      ) ddt_max ON ddt1.device_id = ddt_max.device_id
                                AND ddt1.record_date = ddt_max.max_date
                                AND ddt1.record_time = ddt_max.max_time
                      ORDER BY ddt1.record_date DESC, ddt1.record_time DESC
                      LIMIT 1000";

        $tasks_stmt = $conn->prepare($tasks_sql);
        if (!$tasks_stmt) {
            throw new Exception("准备任务SQL语句失败: " . $conn->error);
        }

        if (!empty($params)) {
            $tasks_stmt->bind_param($types, ...$params);
        }

        if (!$tasks_stmt->execute()) {
            throw new Exception("执行任务查询失败: " . $tasks_stmt->error);
        }

        $tasks_result = $tasks_stmt->get_result();

        $tasks = [];
        if ($tasks_result && $tasks_result->num_rows > 0) {
            while ($row = $tasks_result->fetch_assoc()) {
                $tasks[] = [
                    'device_id' => $row['device_id'],
                    'device_model' => $row['device_model'],
                    'device_group' => $row['device_group'],
                    'likes_target' => intval($row['likes_target']),
                    'comments_target' => intval($row['comments_target']),
                    'recommendations_target' => intval($row['recommendations_target']),
                    'record_date' => $row['record_date'],
                    'record_time' => $row['record_time']
                ];
            }
        }
        $tasks_stmt->close();

        echo json_encode([
            'success' => true,
            'tasks' => $tasks
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// 处理定时任务管理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && strpos($_POST['action'], 'cron_') === 0) {
    header('Content-Type: application/json; charset=utf-8');

    // 检查管理权限
    require_permission('manage_devices');

    $action = $_POST['action'];

    try {
        switch ($action) {
            case 'cron_create':
                // 创建定时任务
                $task_name = "设备使用时间限制检查";
                $script_path = __DIR__ . DIRECTORY_SEPARATOR . "check_expired_devices_cron.php";

                if (!file_exists($script_path)) {
                    throw new Exception('检查脚本不存在: ' . $script_path);
                }

                // 检查PHP是否可用
                $php_check = shell_exec('php --version 2>&1');
                if (strpos($php_check, 'PHP') === false) {
                    throw new Exception('系统中未找到PHP，请先安装PHP或将PHP添加到系统PATH环境变量中');
                }

                // 创建Windows定时任务
                $command = 'schtasks /create /tn "' . $task_name . '" /tr "php \"' . $script_path . '\"" /sc hourly /mo 1 /f 2>&1';
                $output = shell_exec($command);

                if (strpos($output, '成功') !== false || strpos($output, 'SUCCESS') !== false) {
                    log_user_action('cron_task_created', "Created cron task: {$task_name}");
                    echo json_encode(['success' => true, 'message' => '定时任务创建成功！任务将每小时执行一次。', 'output' => $output]);
                } else {
                    throw new Exception('创建定时任务失败: ' . $output);
                }
                break;

            case 'cron_delete':
                // 删除定时任务
                $task_name = "设备使用时间限制检查";
                $command = 'schtasks /delete /tn "' . $task_name . '" /f 2>&1';
                $output = shell_exec($command);

                if (strpos($output, '成功') !== false || strpos($output, 'SUCCESS') !== false) {
                    log_user_action('cron_task_deleted', "Deleted cron task: {$task_name}");
                    echo json_encode(['success' => true, 'message' => '定时任务删除成功！', 'output' => $output]);
                } else {
                    throw new Exception('删除定时任务失败: ' . $output);
                }
                break;

            case 'cron_status':
                // 查询定时任务状态
                $task_name = "设备使用时间限制检查";
                $command = 'schtasks /query /tn "' . $task_name . '" /fo csv 2>&1';
                $output = shell_exec($command);

                if (strpos($output, $task_name) !== false) {
                    // 解析CSV输出获取任务状态
                    $lines = explode("\n", trim($output));
                    $status_info = [];

                    if (count($lines) >= 2) {
                        $headers = str_getcsv($lines[0]);
                        $data = str_getcsv($lines[1]);

                        for ($i = 0; $i < min(count($headers), count($data)); $i++) {
                            $status_info[$headers[$i]] = $data[$i];
                        }
                    }

                    echo json_encode(['success' => true, 'exists' => true, 'status' => $status_info, 'output' => $output]);
                } else {
                    echo json_encode(['success' => true, 'exists' => false, 'message' => '定时任务不存在']);
                }
                break;

            case 'cron_run_now':
                // 立即执行一次检查
                $script_path = __DIR__ . DIRECTORY_SEPARATOR . "check_expired_devices_cron.php";

                if (!file_exists($script_path)) {
                    throw new Exception('检查脚本不存在: ' . $script_path);
                }

                // 执行脚本并捕获输出
                ob_start();
                include $script_path;
                $output = ob_get_clean();

                log_user_action('cron_manual_run', "Manually executed expired devices check");
                echo json_encode(['success' => true, 'message' => '手动检查执行完成', 'output' => $output]);
                break;

            default:
                throw new Exception('无效的定时任务操作');
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }

    exit();
}

// 处理获取设备分组请求
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_device_groups') {
    header('Content-Type: application/json; charset=utf-8');

    try {
        // 借鉴首页分组筛选的方式：从device_groups表获取分组列表
        $groups_sql = "SELECT group_name FROM device_groups ORDER BY group_name";
        $groups_result = $conn->query($groups_sql);
        $groups = [];

        if ($groups_result && $groups_result->num_rows > 0) {
            while ($row = $groups_result->fetch_assoc()) {
                $groups[] = $row['group_name'];
            }
        }

        // 如果device_groups表为空，从实际设备数据中获取分组
        if (empty($groups)) {
            // 从devices表获取分组
            $devices_groups_sql = "SELECT DISTINCT device_group FROM devices
                                  WHERE device_group IS NOT NULL AND device_group != ''
                                  ORDER BY device_group";
            $devices_result = $conn->query($devices_groups_sql);

            if ($devices_result && $devices_result->num_rows > 0) {
                while ($row = $devices_result->fetch_assoc()) {
                    if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                        $groups[] = $row['device_group'];
                    }
                }
            }

            // 从心跳记录表获取分组（作为补充）
            $heartbeat_groups_sql = "SELECT DISTINCT device_group FROM device_heartbeat
                                    WHERE device_group IS NOT NULL AND device_group != ''
                                    AND device_group != '未分组'
                                    ORDER BY device_group";
            $heartbeat_result = $conn->query($heartbeat_groups_sql);

            if ($heartbeat_result && $heartbeat_result->num_rows > 0) {
                while ($row = $heartbeat_result->fetch_assoc()) {
                    if (!empty($row['device_group']) && !in_array($row['device_group'], $groups)) {
                        $groups[] = $row['device_group'];
                    }
                }
            }
        }

        // 添加"未分组"选项
        $groups[] = '未分组';

        echo json_encode([
            'success' => true,
            'groups' => $groups
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// 按组筛选
$group_filter = $_GET['group'] ?? '';

// 黑名单筛选
$blacklist_filter = isset($_GET['blacklist']) ? (int)$_GET['blacklist'] : -1;

// 处理搜索和分页
$search = $_GET['search'] ?? '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // 每页显示10条（修改为10）
$offset = ($page - 1) * $limit;

// 构建搜索条件
$where = [];
if (!empty($search)) {
    $search_escaped = $conn->real_escape_string($search);
    $where[] = "(d.device_id LIKE '%{$search_escaped}%' OR d.device_model LIKE '%{$search_escaped}%')";
}
if (!empty($group_filter)) {
    if ($group_filter === '未分组') {
        $where[] = "(d.device_group IS NULL OR d.device_group = '' OR d.device_group = '未分组')";
    } else {
        $group_escaped = $conn->real_escape_string($group_filter);
        $where[] = "d.device_group = '{$group_escaped}'";
    }
}
if ($blacklist_filter === 1) {
    $where[] = "b.device_id IS NOT NULL";
} else if ($blacklist_filter === 0) {
    $where[] = "b.device_id IS NULL";
}
$where_sql = '';
if (count($where) > 0) {
    $where_sql = 'WHERE ' . implode(' AND ', $where);
}

// 计算总记录数
$count_sql = "SELECT COUNT(*) as total FROM devices d
              LEFT JOIN device_blacklist b ON d.device_id = b.device_id AND b.is_active = 1
              {$where_sql}";
$count_result = $conn->query($count_sql);
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $limit);

// 获取设备组列表 - 修改前
$groups_sql = "SELECT DISTINCT device_group FROM devices WHERE device_group IS NOT NULL AND device_group != '' ORDER BY device_group";

// 修改为从device_groups表获取分组列表
$groups_sql = "SELECT group_name FROM device_groups ORDER BY group_name";
$groups_result = $conn->query($groups_sql);
$groups = [];
if ($groups_result) {
    while($row = $groups_result->fetch_assoc()) {
        $groups[] = $row['group_name'];
    }
}

// 获取每个分组的设备数量统计
$group_stats = [];
try {
    // 获取所有分组的设备数量（包括未分组和在线设备统计）
    $group_stats_sql = "
        SELECT
            CASE
                WHEN d.device_group IS NULL OR d.device_group = '' THEN '未分组'
                ELSE d.device_group
            END as group_name,
            COUNT(*) as device_count,
            COUNT(CASE WHEN d.last_active >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_24h_count,
            COUNT(CASE WHEN h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) THEN 1 END) as online_count,
            SUM(CASE WHEN b.device_id IS NOT NULL THEN 1 ELSE 0 END) as blacklisted_count
        FROM devices d
        LEFT JOIN device_blacklist b ON d.device_id = b.device_id AND b.is_active = 1
        LEFT JOIN device_heartbeat h ON d.device_id = h.device_id
        GROUP BY
            CASE
                WHEN d.device_group IS NULL OR d.device_group = '' THEN '未分组'
                ELSE d.device_group
            END
        ORDER BY
            CASE
                WHEN d.device_group IS NULL OR d.device_group = '' THEN 1
                ELSE 0
            END,
            d.device_group
    ";

    $group_stats_result = $conn->query($group_stats_sql);
    if ($group_stats_result) {
        while($row = $group_stats_result->fetch_assoc()) {
            $group_stats[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("获取分组统计失败: " . $e->getMessage());
    $group_stats = [];
}

// 检查并创建心跳记录表（如果不存在）
$check_heartbeat_table = "SHOW TABLES LIKE 'device_heartbeat'";
$check_result = $conn->query($check_heartbeat_table);

if ($check_result->num_rows == 0) {
    // 表不存在，创建表（修复TIMESTAMP问题）
    $create_heartbeat_table = "CREATE TABLE device_heartbeat (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
        device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
        last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后心跳时间',
        script_version VARCHAR(50) DEFAULT NULL COMMENT '脚本版本',
        running_status ENUM('running', 'idle', 'stopped') DEFAULT 'idle' COMMENT '运行状态',
        device_model VARCHAR(100) DEFAULT NULL COMMENT '设备型号',
        android_version VARCHAR(50) DEFAULT NULL COMMENT 'Android版本',
        battery_level INT DEFAULT NULL COMMENT '电池电量百分比',
        memory_usage INT DEFAULT NULL COMMENT '内存使用量MB',
        created_at DATETIME DEFAULT NULL COMMENT '创建时间',
        UNIQUE KEY unique_device (device_id) COMMENT '设备ID唯一索引',
        INDEX idx_last_heartbeat (last_heartbeat) COMMENT '心跳时间索引',
        INDEX idx_device_id (device_id) COMMENT '设备ID索引',
        INDEX idx_running_status (running_status) COMMENT '运行状态索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备心跳记录表'";

    if (!$conn->query($create_heartbeat_table)) {
        error_log("创建心跳表失败: " . $conn->error);
        // 如果表创建失败，设置默认值避免后续查询出错
        $heartbeat_stats = [
            'total_heartbeat_devices' => 0,
            'online_devices' => 0,
            'running_devices' => 0,
            'offline_devices' => 0
        ];
    }
}

// 获取统计数据（包含心跳统计）
$stats_sql = "SELECT
              COUNT(*) as total_devices,
              COUNT(DISTINCT device_model) as unique_models,
              SUM(access_count) as total_accesses,
              COUNT(CASE WHEN last_active >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_24h,
              COUNT(CASE WHEN last_active >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_7d,
              COUNT(CASE WHEN first_seen >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as new_devices_24h
              FROM devices";

$stats_result = $conn->query($stats_sql);
if ($stats_result === false) {
    echo "统计查询失败: " . $conn->error;
    exit;
}
$stats = $stats_result->fetch_assoc();

// 获取心跳统计数据（只有在表存在时才查询）
if (!isset($heartbeat_stats)) {
    $heartbeat_stats_sql = "SELECT
                           COUNT(*) as total_heartbeat_devices,
                           COUNT(CASE WHEN last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) THEN 1 END) as online_devices,
                           COUNT(CASE WHEN running_status = 'running' THEN 1 END) as running_devices,
                           COUNT(CASE WHEN last_heartbeat < DATE_SUB(NOW(), INTERVAL 2 MINUTE) THEN 1 END) as offline_devices
                           FROM device_heartbeat";

    $heartbeat_result = $conn->query($heartbeat_stats_sql);

    if ($heartbeat_result) {
        $heartbeat_stats = $heartbeat_result->fetch_assoc();
    } else {
        // 查询失败时的默认值
        error_log("心跳统计查询失败: " . $conn->error);
        $heartbeat_stats = [
            'total_heartbeat_devices' => 0,
            'online_devices' => 0,
            'running_devices' => 0,
            'offline_devices' => 0
        ];
    }
}
// 获取活跃黑名单设备数量
$blacklist_count_sql = "SELECT COUNT(*) as blacklist_count FROM device_blacklist WHERE is_active = 1";
$blacklist_result = $conn->query($blacklist_count_sql);
$blacklist_count = $blacklist_result->fetch_assoc()['blacklist_count'];

// 获取自动拉黑的统计信息
$auto_blacklist_stats = [
    'total_auto_blacklisted' => 0,
    'today_auto_blacklisted' => 0,
    'pending_ungrouped' => 0
];

try {
    // 总的自动拉黑设备数
    $auto_blacklist_total_sql = "SELECT COUNT(*) as total FROM device_blacklist WHERE blacklisted_by = 'auto-system'";
    $auto_total_result = $conn->query($auto_blacklist_total_sql);
    if ($auto_total_result) {
        $auto_blacklist_stats['total_auto_blacklisted'] = $auto_total_result->fetch_assoc()['total'];
    }

    // 今天自动拉黑的设备数
    $auto_blacklist_today_sql = "SELECT COUNT(*) as today FROM device_blacklist
                                WHERE blacklisted_by = 'auto-system'
                                AND DATE(blacklist_start_time) = CURDATE()";
    $auto_today_result = $conn->query($auto_blacklist_today_sql);
    if ($auto_today_result) {
        $auto_blacklist_stats['today_auto_blacklisted'] = $auto_today_result->fetch_assoc()['today'];
    }

    // 等待分组的设备数（5分钟内注册且未分组的设备）
    $pending_ungrouped_sql = "SELECT COUNT(*) as pending FROM devices
                             WHERE (device_group IS NULL OR device_group = '' OR device_group = '未分组')
                             AND first_seen > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                             AND device_id NOT IN (SELECT device_id FROM device_blacklist)";
    $pending_result = $conn->query($pending_ungrouped_sql);
    if ($pending_result) {
        $auto_blacklist_stats['pending_ungrouped'] = $pending_result->fetch_assoc()['pending'];
    }
} catch (Exception $e) {
    error_log("获取自动拉黑统计失败: " . $e->getMessage());
}

// 按访问次数排序
$sort_by = $_GET['sort_by'] ?? 'last_active';
$sort_order = $_GET['sort_order'] ?? 'DESC';

// 验证排序参数
$allowed_sort_fields = ['device_id', 'device_model', 'android_version', 'last_active', 'access_count', 'first_seen'];
if (!in_array($sort_by, $allowed_sort_fields)) {
    $sort_by = 'last_active';
}
$sort_order = ($sort_order == 'ASC') ? 'ASC' : 'DESC';

// 获取设备列表
$sql = "SELECT d.*,
        d.ip_address,
        d.first_seen,
        CASE WHEN b.device_id IS NOT NULL THEN 1 ELSE 0 END as is_blacklisted,
        CASE
            WHEN (d.device_group IS NULL OR d.device_group = '' OR d.device_group = '未分组')
                 AND d.first_seen <= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                 AND b.device_id IS NULL
            THEN 1
            ELSE 0
        END as should_be_blacklisted
        FROM devices d
        LEFT JOIN device_blacklist b ON d.device_id = b.device_id AND b.is_active = 1
        {$where_sql}
        ORDER BY {$sort_by} {$sort_order}
        LIMIT {$offset}, {$limit}";

$result = $conn->query($sql);
if ($result === false) {
    echo "查询失败: " . $conn->error;
    exit;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控管理系统</title>
    <?php echo generate_user_menu_css(); ?>
    <style>
        /* 基本样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            padding: 20px 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* 头部样式 - 优化布局 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .header-text h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-text p {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            text-align: right;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-dot.online {
            background: #2ecc71;
            box-shadow: 0 0 8px rgba(46, 204, 113, 0.6);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 8px rgba(46, 204, 113, 0.6); }
            50% { box-shadow: 0 0 16px rgba(46, 204, 113, 0.8); }
            100% { box-shadow: 0 0 8px rgba(46, 204, 113, 0.6); }
        }

        @media (max-width: 768px) {
            .header {
                padding: 24px 20px;
            }
            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 16px;
            }
            .header-text h1 {
                font-size: 28px;
            }
            .header-actions {
                text-align: center;
            }
        }

        /* 统计卡片样式 - 优化布局 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 16px;
            margin: 30px 0;
            padding: 0 10px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 12px;
            padding: 24px 16px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c, #9b59b6);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card h3 {
            font-size: 13px;
            color: #6c757d;
            margin: 0 0 12px 0;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .stat-card p {
            font-size: 32px;
            font-weight: 700;
            margin: 0;
            line-height: 1;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .stats-container {
                grid-template-columns: repeat(4, 1fr);
                gap: 14px;
            }
        }

        @media (max-width: 1200px) {
            .stats-container {
                grid-template-columns: repeat(3, 1fr);
                gap: 14px;
            }
            .stat-card {
                padding: 20px 14px;
            }
            .stat-card p {
                font-size: 28px;
            }
        }

        @media (max-width: 768px) {
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                margin: 20px 0;
                padding: 0 5px;
            }
            .stat-card {
                padding: 18px 12px;
            }
            .stat-card h3 {
                font-size: 12px;
            }
            .stat-card p {
                font-size: 24px;
            }
        }

        @media (max-width: 480px) {
            .stats-container {
                grid-template-columns: 1fr;
            }
        }

        /* 统计卡片颜色主题 */
        .stat-blue {
            color: #3498db;
            background: linear-gradient(135deg, #ebf3fd 0%, #d6e9fc 100%);
        }
        .stat-green {
            color: #2ecc71;
            background: linear-gradient(135deg, #e8f8f0 0%, #d1f2df 100%);
        }
        .stat-yellow {
            color: #f39c12;
            background: linear-gradient(135deg, #fef9e7 0%, #fcf3cd 100%);
        }
        .stat-red {
            color: #e74c3c;
            background: linear-gradient(135deg, #fdf2f2 0%, #fce4e4 100%);
        }
        .stat-purple {
            color: #9b59b6;
            background: linear-gradient(135deg, #f4f1f8 0%, #e8ddf0 100%);
        }
        .stat-orange {
            color: #e67e22;
            background: linear-gradient(135deg, #fef5f0 0%, #fde8d7 100%);
        }
        .stat-gray {
            color: #6c757d;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .stat-cyan {
            color: #17a2b8;
            background: linear-gradient(135deg, #e6f7ff 0%, #b3e5fc 100%);
        }

        /* 桌面查看相关样式 */
        #desktop-view-modal .modal-content {
            overflow: hidden;
        }

        #desktop-content {
            max-height: 80vh;
            overflow: auto;
        }

        #desktop-image-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px;
        }

        #desktop-image {
            transition: all 0.3s ease;
            border: 1px solid #ddd;
        }

        #desktop-image:hover {
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            transform: scale(1.02);
        }

        #desktop-image.fullsize {
            max-width: none !important;
            max-height: none !important;
            cursor: zoom-out;
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            #desktop-image {
                max-width: 85% !important;
                max-height: 65vh !important;
            }
        }

        @media (max-width: 768px) {
            #desktop-view-modal .modal-content {
                max-width: 98vw;
                max-height: 98vh;
                margin: 1vh auto;
                padding: 10px;
            }

            #desktop-image {
                max-width: 95% !important;
                max-height: 55vh !important;
            }
        }

        @media (max-width: 480px) {
            #desktop-image {
                max-width: 100% !important;
                max-height: 50vh !important;
            }
        }

        /* 历史截图状态样式 */
        .historical-screenshot {
            position: relative;
        }

        .historical-screenshot::before {
            content: '历史截图';
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(162, 155, 254, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10;
        }

        .offline-screenshot::before {
            content: '设备离线';
            background: rgba(99, 110, 114, 0.9);
        }

        /* 历史截图相关样式 */
        .screenshot-item {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .screenshot-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
            border-color: #74b9ff;
        }

        .screenshot-thumbnail {
            width: 100%;
            height: 120px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .screenshot-info {
            padding: 10px;
            border-top: 1px solid #e9ecef;
        }

        .screenshot-time {
            font-size: 12px;
            color: #495057;
            margin-bottom: 4px;
        }

        .screenshot-size {
            font-size: 11px;
            color: #6c757d;
        }

        .screenshot-age {
            font-size: 11px;
            color: #28a745;
            font-weight: bold;
        }

        .screenshot-age.old {
            color: #ffc107;
        }

        .screenshot-age.very-old {
            color: #dc3545;
        }

        /* 分页按钮样式 */
        .button-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .button-secondary:hover {
            background: #5a6268;
        }

        .button-secondary:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 统计卡片图标样式 */
        .stat-icon {
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.8;
            display: block;
        }

        .stat-card small {
            display: block;
            font-size: 11px;
            color: rgba(0,0,0,0.6);
            margin-top: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        /* 卡片特殊效果 */
        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .stat-card:hover::after {
            opacity: 1;
        }

        /* 分组统计区域样式 */
        .group-stats-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 30px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .group-stats-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .group-stats-header h3 {
            font-size: 24px;
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-weight: 700;
        }

        .group-stats-header p {
            color: #6c757d;
            margin: 0;
            font-size: 14px;
        }

        .group-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .group-stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .group-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .group-stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e9ecef;
        }

        .group-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .group-total {
            font-size: 24px;
            font-weight: 700;
            color: #3498db;
        }

        .group-stat-details {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .stat-label {
            display: block;
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 4px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
        }

        .stat-value.online {
            color: #27ae60;
            font-weight: 700;
        }

        .stat-value.active {
            color: #2ecc71;
        }

        .stat-value.blacklisted {
            color: #e74c3c;
        }

        .stat-value.normal {
            color: #3498db;
        }



        /* 响应式设计 */
        @media (max-width: 768px) {
            .group-stats-grid {
                grid-template-columns: 1fr;
            }

            .group-stat-details {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .group-stats-container {
                padding: 16px;
                margin: 20px 0;
            }
        }

        @media (max-width: 480px) {
            .group-stat-details {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .stat-item {
                padding: 6px;
            }
        }

        /* 导航标签页样式 */
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 6px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .nav-tab {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 14px 20px;
            border-radius: 8px;
            text-decoration: none;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-tab:hover {
            color: #495057;
            background: rgba(0,0,0,0.02);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .nav-icon {
            font-size: 16px;
        }

        .nav-text {
            font-size: 14px;
        }

        /* 设备日志标签特殊样式 */
        .nav-tab[href*="startup_logs"].active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        @media (max-width: 768px) {
            .nav-tab {
                padding: 12px 16px;
                flex-direction: column;
                gap: 4px;
            }
            .nav-icon {
                font-size: 18px;
            }
            .nav-text {
                font-size: 12px;
            }
        }

        /* 控制面板样式 */
        .controls-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #34495e;
            font-size: 14px;
        }

        .control-group input,
        .control-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        /* 分组筛选选择框特殊样式 */
        .control-group select#group {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            font-weight: 500;
        }

        .control-group select#group:focus {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        /* 分组选项样式 */
        .control-group select#group option {
            background: #ffffff;
            color: #495057;
            padding: 8px 12px;
        }

        .control-group select#group option[value=""] {
            background: #f8f9fa;
            font-weight: 600;
            color: #6c757d;
        }

        .control-group select#group option[value="未分组"] {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            font-weight: 600;
        }

        .control-group select#group option:not([value=""]):not([value="未分组"]) {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            font-weight: 500;
        }

        /* 选中状态的特殊样式 */
        .control-group select#group option:checked {
            background: #007bff !important;
            color: white !important;
        }

        /* 分组筛选标签增强 */
        .control-group label[for="group"] {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            display: inline-block;
        }

        /* 当前筛选状态提示 */
        .current-filter-hint {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
            font-style: italic;
        }

        /* 批量操作样式 */
        .batch-actions {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .dropdown-toggle:hover {
            background: #2980b9;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 9999;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 0;
            /* 确保下拉菜单在表格底部时向上显示 */
            bottom: 100%;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
        }

        /* 显示下拉菜单 */
        .dropdown-menu.show {
            display: block;
        }

        /* 当下拉菜单在页面顶部时，向下显示 */
        .dropdown-menu.show-below {
            bottom: auto;
            top: 100%;
        }

        .dropdown-item {
            color: #333;
            padding: 8px 16px;
            text-decoration: none;
            display: block;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f1f1f1;
        }

        .dropdown-divider {
            height: 1px;
            margin: 5px 0;
            background-color: #e9ecef;
        }

        /* 表格容器滚动适配 */
        .table-container {
            overflow-x: auto;
            overflow-y: visible; /* 允许垂直方向溢出以显示下拉菜单 */
            position: relative;
        }

        /* 确保下拉菜单不被表格容器裁剪 */
        .table-container table {
            position: relative;
        }

        .select-all-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .select-all-container input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .select-all-container label {
            font-weight: 500;
            color: #34495e;
        }

        /* 按钮样式 */
        .button {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: background 0.2s;
        }

        .button-primary {
            background-color: #3498db;
            color: white;
        }

        .button-primary:hover {
            background-color: #2980b9;
        }

        .button-success {
            background-color: #2ecc71;
            color: white;
        }

        .button-success:hover {
            background-color: #27ae60;
        }

        .button-warning {
            background-color: #e74c3c;
            color: white;
        }

        .button-warning:hover {
            background-color: #c0392b;
        }

        .button-danger {
            background-color: #e74c3c;
            color: white;
        }

        .button-danger:hover {
            background-color: #c0392b;
        }
        
        .button-info {
            background-color: #17a2b8;
            color: white;
        }

        .button-info:hover {
            background-color: #138496;
        }

        .button-secondary {
            background-color: #6c757d;
            color: white;
        }

        .button-secondary:hover {
            background-color: #5a6268;
        }

        .button-info {
            background-color: #17a2b8;
            color: white;
        }

        .button-info:hover {
            background-color: #138496;
        }

        .button-purple {
            background-color: #9b59b6;
            color: white;
        }

        .button-purple:hover {
            background-color: #8e44ad;
        }

        .button-group {
            display: flex;
            gap: 8px;
        }

        /* 表格容器样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow-x: auto; /* 水平滚动 */
            overflow-y: visible; /* 垂直方向允许溢出以显示下拉菜单 */
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background-color: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            border-bottom: 1px solid #eee;
        }

        td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        tr:nth-child(even) {
            background-color: rgba(248, 249, 250, 0.5);
        }

        /* 标签样式 */
        .status-tag, .group-tag {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-tag.normal {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .status-tag.blacklisted {
            background-color: #ffebee;
            color: #c62828;
        }

        .status-tag.warning {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .status-tag.safe {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status-tag.pending {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .group-tag {
            background-color: #e3f2fd;
            color: #1565c0;
        }

        .group-tag.no-group {
            background-color: #f5f5f5;
            color: #757575;
        }

        /* 设备状态样式 */
        .device-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }

        .status-running {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-idle {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-abnormal {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            animation: pulse-red 2s infinite;
        }

        .status-never {
            background-color: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .status-unknown {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @keyframes pulse-red {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .script-status {
            font-weight: 500;
            color: #2c3e50;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            margin: 25px 0;
            flex-wrap: wrap;
        }

        .pagination a, .pagination span {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 4px;
            border-radius: 4px;
            text-decoration: none;
            color: #3498db;
            background-color: white;
            border: 1px solid #eee;
        }

        .pagination a:hover {
            background-color: #f8f9fa;
        }

        .pagination span.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        /* 黑名单模态框特殊样式 */
        #blacklist-view-modal .modal-content {
            max-width: 900px;
            max-height: 85vh;
            display: flex;
            flex-direction: column;
        }

        #blacklist-view-modal #blacklist-content {
            flex: 1;
            overflow-y: auto;
            max-height: calc(85vh - 200px);
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
        }

        #blacklist-view-modal .batch-actions {
            flex-shrink: 0;
            margin: 15px 0 !important;
        }

        #blacklist-view-modal h2 {
            flex-shrink: 0;
            margin-bottom: 20px !important;
        }

        /* 黑名单表格样式优化 */
        .blacklist-table {
            background: white;
            border-radius: 4px;
            overflow: hidden;
        }

        .blacklist-table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
            color: #495057;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
        }

        .blacklist-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .blacklist-table tbody tr:nth-child(even) {
            background-color: #fdfdfd;
        }

        .blacklist-table tbody tr:nth-child(even):hover {
            background-color: #f1f3f4;
        }

        .close-btn {
            color: #aaa;
            float: right;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-btn:hover {
            color: #333;
        }

        /* 模态框内表单样式 */
        .modal-content h2 {
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }

        .modal-content p {
            margin-bottom: 15px;
            color: #7f8c8d;
            font-size: 14px;
        }

        .modal-content .control-group {
            margin-bottom: 15px;
        }

        .modal-content .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #34495e;
            font-size: 14px;
        }

        .modal-content .control-group input,
        .modal-content .control-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .modal-content .control-group input:focus,
        .modal-content .control-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        /* 按钮组样式 */
        .modal-content .button-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        /* 输入框组合样式 */
        .modal-content div[style*="display: flex"] {
            display: flex !important;
            gap: 10px;
            align-items: stretch;
        }

        .modal-content div[style*="display: flex"] input {
            flex: 1;
        }

        .modal-content div[style*="display: flex"] button {
            white-space: nowrap;
            padding: 10px 16px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }

            .batch-actions {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .button-group {
                width: 100%;
                justify-content: space-between;
            }

            .table-container {
                overflow-x: auto;
            }

            /* 移动端模态框适配 */
            .modal-content {
                margin: 2% auto;
                padding: 15px;
                width: 95%;
                max-height: 95vh;
            }

            #blacklist-view-modal .modal-content {
                max-width: 95%;
                max-height: 95vh;
                padding: 15px;
            }

            #blacklist-view-modal #blacklist-content {
                max-height: calc(95vh - 180px);
                padding: 5px;
            }

            #blacklist-view-modal .batch-actions {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            #blacklist-view-modal .button-group {
                width: 100%;
            }

            /* 黑名单表格在移动端的适配 */
            #blacklist-content table {
                font-size: 12px;
            }

            #blacklist-content th,
            #blacklist-content td {
                padding: 5px !important;
                word-break: break-all;
            }
        }

        @media (max-width: 480px) {
            #blacklist-view-modal h2 {
                font-size: 16px;
            }

            #blacklist-content table {
                font-size: 11px;
            }

            #blacklist-content th:nth-child(3),
            #blacklist-content td:nth-child(3) {
                display: none; /* 在很小的屏幕上隐藏拉黑原因列 */
            }
        }

        /* 心跳统计弹窗样式 */
        .heartbeat-stats-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .heartbeat-stats-header h2 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stats-summary {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .stats-summary .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
            min-width: 120px;
            flex: 1;
            max-width: 150px;
        }

        .stats-summary .stat-card.total {
            border-left: 4px solid #3498db;
        }

        .stats-summary .stat-card.online {
            border-left: 4px solid #27ae60;
        }

        .stats-summary .stat-card.warning {
            border-left: 4px solid #f39c12;
        }

        .stats-summary .stat-card.offline {
            border-left: 4px solid #e74c3c;
        }

        .stats-summary .stat-card.running {
            border-left: 4px solid #9b59b6;
        }

        .stats-summary .stat-number {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stats-summary .stat-card.total .stat-number {
            color: #3498db;
        }

        .stats-summary .stat-card.online .stat-number {
            color: #27ae60;
        }

        .stats-summary .stat-card.warning .stat-number {
            color: #f39c12;
        }

        .stats-summary .stat-card.offline .stat-number {
            color: #e74c3c;
        }

        .stats-summary .stat-card.running .stat-number {
            color: #9b59b6;
        }

        .stats-summary .stat-label {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }

        .group-stats-section {
            margin-bottom: 25px;
        }

        .group-stats-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .group-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }

        .group-stat-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .group-stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .group-stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .group-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }

        .group-total {
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .group-stat-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .group-stat-item-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 8px;
            background: white;
            border-radius: 4px;
            font-size: 13px;
        }

        .group-stat-label {
            color: #6c757d;
        }

        .group-stat-value {
            font-weight: bold;
        }

        .group-stat-value.online {
            color: #27ae60;
        }

        .group-stat-value.warning {
            color: #f39c12;
        }

        .group-stat-value.offline {
            color: #e74c3c;
        }

        .group-stat-value.running {
            color: #9b59b6;
        }

        .modal-actions {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .modal-actions .button {
            margin: 0 10px;
        }

        .loading-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-size: 16px;
        }

        @media (max-width: 768px) {
            .stats-summary {
                flex-direction: column;
            }

            .stats-summary .stat-card {
                max-width: none;
            }

            .group-stats-grid {
                grid-template-columns: 1fr;
            }
        }



        @media screen and (max-width: 480px) {
            /* 超小屏幕适配 */
            .controls-section h2 {
                font-size: 16px;
                text-align: center;
            }

            /* 进一步隐藏列 */
            .controls-section th:nth-child(4),
            .controls-section td:nth-child(4) {
                display: none; /* 隐藏启动方式列 */
            }

            .controls-section th:nth-child(7),
            .controls-section td:nth-child(7) {
                display: none; /* 隐藏运行时长列 */
            }

            .controls-section th:nth-child(2),
            .controls-section td:nth-child(2) {
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            /* 调整表格最小宽度 */
            .controls-section table {
                min-width: 600px;
            }
        }

        /* 横屏适配 */
        @media screen and (max-height: 500px) and (orientation: landscape) {
            .controls-section {
                padding: 10px;
            }

            .controls-section h2 {
                margin-bottom: 10px !important;
            }

            .controls-section > div:nth-child(2) {
                margin-bottom: 15px !important;
            }
        }

        /* 定时任务管理页面样式 */
        .info-section {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .info-section h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .info-section ul {
            margin: 0;
            padding-left: 20px;
            color: #6c757d;
        }

        .log-section {
            margin-top: 30px;
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

    </style>
</head>
<body>
    <?php echo generate_user_menu(); ?>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-text">
                    <h1>设备监控管理系统</h1>
                    <p>实时监控设备状态，高效管理设备信息</p>
                </div>
                <div class="header-actions">
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span>系统运行正常</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <?php if (isset($message)): ?>
        <div class="alert alert-success" style="margin: 20px 0; padding: 15px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 4px;">
            <strong>操作成功：</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="alert alert-error" style="margin: 20px 0; padding: 15px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px;">
            <strong>操作失败：</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <!-- 统计卡片 -->
        <div class="stats-container">
            <div class="stat-card stat-blue">
                <div class="stat-icon">总设备数</div>
                <p><?php echo number_format($stats['total_devices']); ?></p>
                <small>已注册设备</small>
            </div>
            <div class="stat-card stat-green">
                <div class="stat-icon">在线设备</div>
                <p><?php echo number_format($heartbeat_stats['online_devices']); ?></p>
                <small>实时在线</small>
            </div>
            <div class="stat-card stat-gray">
                <div class="stat-icon">离线设备</div>
                <p><?php echo number_format($heartbeat_stats['offline_devices']); ?></p>
                <small>超过2分钟未响应</small>
            </div>
            <div class="stat-card stat-cyan">
                <div class="stat-icon">新增设备</div>
                <p><?php echo number_format($stats['new_devices_24h']); ?></p>
                <small>24小时内新增</small>
            </div>
            <div class="stat-card stat-red">
                <div class="stat-icon">黑名单</div>
                <h3>黑名单设备</h3>
                <p><?php echo number_format($blacklist_count); ?></p>
                <small>已被禁用</small>
            </div>
        </div>

        <!-- 分组统计区域 -->
        <?php if (!empty($group_stats)): ?>
        <div class="group-stats-container">
            <div class="group-stats-header">
                <h3>分组统计</h3>
                <p>各分组设备数量概览</p>
            </div>
            <div class="group-stats-grid">
                <?php foreach ($group_stats as $group_stat): ?>
                <div class="group-stat-card">
                    <div class="group-stat-header">
                        <span class="group-name">
                            <?php echo htmlspecialchars($group_stat['group_name']); ?>
                        </span>
                        <span class="group-total"><?php echo number_format($group_stat['device_count']); ?></span>
                    </div>
                    <div class="group-stat-details">
                        <div class="stat-item">
                            <span class="stat-label">在线设备</span>
                            <span class="stat-value online"><?php echo number_format($group_stat['online_count']); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">24h活跃</span>
                            <span class="stat-value active"><?php echo number_format($group_stat['active_24h_count']); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">黑名单</span>
                            <span class="stat-value blacklisted"><?php echo number_format($group_stat['blacklisted_count']); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">正常</span>
                            <span class="stat-value normal"><?php echo number_format($group_stat['device_count'] - $group_stat['blacklisted_count']); ?></span>
                        </div>
                    </div>

                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (($_GET['view'] ?? '') != 'startup_logs' && ($_GET['view'] ?? '') != 'stop_logs'): ?>
        <!-- 导航标签页 -->
        <div class="nav-tabs">
            <a href="monitor.php" class="nav-tab <?php echo (($_GET['view'] ?? '') == '' || $_GET['view'] == 'devices') ? 'active' : ''; ?>">
                <span class="nav-text">设备管理</span>
            </a>
            <a href="monitor.php?view=heartbeat" class="nav-tab <?php echo (($_GET['view'] ?? '') == 'heartbeat') ? 'active' : ''; ?>">
                <span class="nav-text">心跳监控</span>
            </a>
            <?php if (check_permission('manage_devices')): ?>
            <a href="device_usage_limits.php" class="nav-tab">
                <span class="nav-text">使用时间限制</span>
            </a>
            <a href="monitor.php?view=cron_tasks" class="nav-tab <?php echo (($_GET['view'] ?? '') == 'cron_tasks') ? 'active' : ''; ?>">
                <span class="nav-text">定时任务管理</span>
            </a>
            <?php endif; ?>

        </div>
        <?php endif; ?>

        <?php if (($_GET['view'] ?? '') == 'heartbeat'): ?>
        <!-- 心跳监控页面 -->
        <div class="controls-section">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">设备心跳监控</h2>
            <form method="get" action="monitor.php">
                <input type="hidden" name="view" value="heartbeat">
                <div class="controls-grid">
                    <div class="control-group">
                        <label for="heartbeat_search">搜索设备</label>
                        <input type="text" id="heartbeat_search" name="search" placeholder="输入设备ID或型号"
                               value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                    </div>

                    <div class="control-group">
                        <label for="status_filter">状态筛选</label>
                        <select name="status" id="status_filter">
                            <option value="">所有状态</option>
                            <option value="running" <?php echo (($_GET['status'] ?? '') == 'running') ? 'selected' : ''; ?>>运行中</option>
                            <option value="offline" <?php echo (($_GET['status'] ?? '') == 'offline') ? 'selected' : ''; ?>>离线</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <div class="button-group">
                            <button type="submit" class="button button-primary">搜索</button>
                            <button type="button" class="button button-success" onclick="refreshHeartbeat()">刷新</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <?php
        // 心跳监控查询逻辑
        $heartbeat_search = $_GET['search'] ?? '';
        $status_filter = $_GET['status'] ?? '';
        $heartbeat_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $heartbeat_limit = 15;
        $heartbeat_offset = ($heartbeat_page - 1) * $heartbeat_limit;

        // 检查心跳表是否存在
        $check_heartbeat_table = "SHOW TABLES LIKE 'device_heartbeat'";
        $table_exists = $conn->query($check_heartbeat_table)->num_rows > 0;

        $heartbeat_result = null;
        $heartbeat_total_records = 0;
        $heartbeat_total_pages = 0;

        if ($table_exists) {
            // 构建心跳查询条件
            $heartbeat_where = [];
            if (!empty($heartbeat_search)) {
                $search_escaped = $conn->real_escape_string($heartbeat_search);
                $heartbeat_where[] = "(h.device_id LIKE '%{$search_escaped}%' OR h.device_model LIKE '%{$search_escaped}%')";
            }

            if ($status_filter == 'offline') {
                $heartbeat_where[] = "h.last_heartbeat < DATE_SUB(NOW(), INTERVAL 2 MINUTE)";
            } elseif ($status_filter == 'running') {
                $heartbeat_where[] = "h.running_status = 'running' AND h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)";
            }

            $heartbeat_where_sql = '';
            if (count($heartbeat_where) > 0) {
                $heartbeat_where_sql = 'WHERE ' . implode(' AND ', $heartbeat_where);
            }

            // 获取心跳记录总数
            $heartbeat_count_sql = "SELECT COUNT(*) as total FROM device_heartbeat h
                                   LEFT JOIN devices d ON h.device_id = d.device_id
                                   {$heartbeat_where_sql}";
            $heartbeat_count_result = $conn->query($heartbeat_count_sql);

            if ($heartbeat_count_result) {
                $heartbeat_total_records = $heartbeat_count_result->fetch_assoc()['total'];
                $heartbeat_total_pages = ceil($heartbeat_total_records / $heartbeat_limit);

                // 获取心跳记录
                $heartbeat_sql = "SELECT h.*,
                                 d.device_group,
                                 CASE
                                     WHEN h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) THEN 'online'
                                     WHEN h.last_heartbeat >= DATE_SUB(NOW(), INTERVAL 2 MINUTE) THEN 'warning'
                                     ELSE 'offline'
                                 END as connection_status,
                                 TIMESTAMPDIFF(MINUTE, h.last_heartbeat, NOW()) as minutes_ago
                                 FROM device_heartbeat h
                                 LEFT JOIN devices d ON h.device_id = d.device_id
                                 {$heartbeat_where_sql}
                                 ORDER BY h.last_heartbeat DESC
                                 LIMIT {$heartbeat_offset}, {$heartbeat_limit}";

                $heartbeat_result = $conn->query($heartbeat_sql);
            }
        }
        ?>

        <!-- 心跳监控列表 -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="select-all-heartbeat" onchange="toggleHeartbeatSelectAll(this)">
                        </th>
                        <th>设备ID</th>
                        <th>设备型号</th>
                        <th>Android版本</th>
                        <th>脚本版本</th>
                        <th>分组</th>
                        <th>运行状态</th>
                        <th>连接状态</th>
                        <th>最后心跳</th>
                        <th>电池电量</th>
                        <th>内存使用</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if (!$table_exists) {
                        echo "<tr><td colspan='12' style='text-align:center; padding: 30px; color: #e74c3c;'>
                                device_heartbeat 表不存在<br>
                                <a href='init_heartbeat_table.php' style='color: #3498db; text-decoration: underline;'>点击这里初始化数据库表</a>
                              </td></tr>";
                    } elseif ($heartbeat_result && $heartbeat_result->num_rows > 0) {
                        while($row = $heartbeat_result->fetch_assoc()) {
                            echo "<tr>";

                            // 复选框
                            echo "<td><input type='checkbox' class='heartbeat-checkbox' value='" . htmlspecialchars($row['device_id']) . "'></td>";

                            // 设备ID
                            echo "<td><strong>" . htmlspecialchars($row['device_id']) . "</strong></td>";

                            // 设备型号
                            echo "<td>" . htmlspecialchars($row['device_model'] ?? '未知') . "</td>";

                            // Android版本
                            echo "<td>" . htmlspecialchars($row['android_version'] ?? '未知') . "</td>";

                            // 脚本版本
                            echo "<td>" . htmlspecialchars($row['script_version'] ?? '未知') . "</td>";

                            // 分组
                            echo "<td>";
                            if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                                echo "<span class='group-tag'>" . htmlspecialchars($row['device_group']) . "</span>";
                            } else {
                                echo "<span class='group-tag no-group'>无分组</span>";
                            }
                            echo "</td>";

                            // 运行状态
                            $status_class = '';
                            $status_text = '';
                            // 基于连接状态判断运行状态，移除空闲状态
                            if ($row['connection_status'] == 'online') {
                                $status_class = 'status-running';
                                $status_text = '运行中';
                            } else {
                                $status_class = 'status-offline';
                                $status_text = '离线';
                            }
                            echo "<td><span class='device-status {$status_class}'>{$status_text}</span></td>";

                            // 连接状态
                            $connection_class = '';
                            $connection_text = '';
                            switch($row['connection_status']) {
                                case 'online':
                                    $connection_class = 'status-online';
                                    $connection_text = '在线';
                                    break;
                                case 'warning':
                                    $connection_class = 'status-warning';
                                    $connection_text = '警告';
                                    break;
                                default:
                                    $connection_class = 'status-offline';
                                    $connection_text = '离线';
                            }
                            echo "<td><span class='device-status {$connection_class}'>{$connection_text}</span></td>";

                            // 最后心跳时间
                            $minutes_ago = $row['minutes_ago'];
                            if ($minutes_ago == 0) {
                                echo "<td>刚刚</td>";
                            } elseif ($minutes_ago < 60) {
                                echo "<td>{$minutes_ago}分钟前</td>";
                            } else {
                                echo "<td>" . date('Y-m-d H:i:s', strtotime($row['last_heartbeat'])) . "</td>";
                            }

                            // 电池电量
                            $battery = $row['battery_level'];
                            if ($battery > 0) {
                                $battery_class = $battery > 20 ? 'battery-good' : 'battery-low';
                                echo "<td><span class='{$battery_class}'>{$battery}%</span></td>";
                            } else {
                                echo "<td>未知</td>";
                            }

                            // 内存使用
                            $memory = $row['memory_usage'];
                            if ($memory > 0) {
                                echo "<td>{$memory} MB</td>";
                            } else {
                                echo "<td>未知</td>";
                            }

                            // 操作按钮
                            echo "<td>";
                            echo "<div class='button-group'>";
                            echo "<button type='button' class='button button-primary' onclick='viewHeartbeatDetails(\"" . htmlspecialchars($row['device_id']) . "\")' style='padding: 4px 8px; font-size: 12px;'>详情</button>";
                            echo "</div>";
                            echo "</td>";

                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='12' style='text-align:center; padding: 30px; color: #7f8c8d;'>没有找到心跳记录</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <!-- 心跳监控批量操作 -->
        <div class="batch-actions">
            <div class="select-all-container">
                <input type="checkbox" id="select-all-heartbeat-bottom" onchange="toggleHeartbeatSelectAll(this)">
                <label for="select-all-heartbeat-bottom">全选此页</label>
            </div>
            <div class="button-group">
                <?php if (check_permission('manage_devices')): ?>
                <button class="button button-danger" onclick="batchDeleteHeartbeatDevices()">批量删除设备</button>
                <?php endif; ?>
                <?php if (check_permission('manage_blacklist')): ?>
                <button class="button button-warning" onclick="batchBlacklistHeartbeatDevices()">批量拉黑设备</button>
                <button class="button button-success" onclick="batchRemoveBlacklistHeartbeatDevices()">批量解除拉黑</button>
                <button class="button button-info" onclick="checkExpiredDevices()">检查过期设备</button>
                <?php endif; ?>
                <button class="button button-success" onclick="refreshHeartbeat()">刷新数据</button>

                <!-- 操作员只读提示 -->
                <?php if (!check_permission('manage_devices') && !check_permission('manage_blacklist')): ?>
                <div class="readonly-notice" style="background: #fff3cd; color: #856404; padding: 8px; border-radius: 5px; font-size: 12px; margin-left: 10px;">
                    <i>只读权限</i>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 心跳监控分页 -->
        <div class="pagination">
            <?php
            $heartbeat_query_params = $_GET;

            // 首页链接
            $heartbeat_query_params['page'] = 1;
            $heartbeat_first_link = 'monitor.php?' . http_build_query($heartbeat_query_params);

            // 上一页链接
            $heartbeat_query_params['page'] = max(1, $heartbeat_page - 1);
            $heartbeat_prev_link = 'monitor.php?' . http_build_query($heartbeat_query_params);

            // 下一页链接
            $heartbeat_query_params['page'] = min($heartbeat_total_pages, $heartbeat_page + 1);
            $heartbeat_next_link = 'monitor.php?' . http_build_query($heartbeat_query_params);

            // 末页链接
            $heartbeat_query_params['page'] = $heartbeat_total_pages;
            $heartbeat_last_link = 'monitor.php?' . http_build_query($heartbeat_query_params);

            echo "<a href=\"{$heartbeat_first_link}\">&laquo; 首页</a>";
            echo "<a href=\"{$heartbeat_prev_link}\">&lsaquo; 上一页</a>";

            // 显示页码
            $heartbeat_start_page = max(1, $heartbeat_page - 2);
            $heartbeat_end_page = min($heartbeat_total_pages, $heartbeat_page + 2);

            for ($i = $heartbeat_start_page; $i <= $heartbeat_end_page; $i++) {
                $heartbeat_query_params['page'] = $i;
                $heartbeat_link = 'monitor.php?' . http_build_query($heartbeat_query_params);

                if ($i == $heartbeat_page) {
                    echo "<span class=\"active\">{$i}</span>";
                } else {
                    echo "<a href=\"{$heartbeat_link}\">{$i}</a>";
                }
            }

            echo "<a href=\"{$heartbeat_next_link}\">下一页 &rsaquo;</a>";
            echo "<a href=\"{$heartbeat_last_link}\">末页 &raquo;</a>";
            ?>
        </div>

        <?php elseif (($_GET['view'] ?? '') == '' || $_GET['view'] == 'devices'): ?>
        <!-- 设备管理页面 -->
        <!-- 搜索和筛选控件 -->
        <div class="controls-section">
            <form method="get" action="monitor.php">
                <div class="controls-grid">
                    <div class="control-group">
                        <label for="search">搜索设备</label>
                        <input type="text" id="search" name="search" placeholder="输入设备ID或型号"
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>

                    <div class="control-group">
                        <label for="group">分组筛选</label>
                        <select name="group" id="group">
                            <option value="">所有分组</option>
                            <option value="未分组" <?php echo ($group_filter === '未分组') ? 'selected' : ''; ?>>未分组</option>
                            <?php foreach($groups as $group): ?>
                            <option value="<?php echo htmlspecialchars($group); ?>"
                                    <?php echo ($group_filter == $group) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($group); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($group_filter)): ?>
                        <div class="current-filter-hint">
                            当前筛选：<?php echo htmlspecialchars($group_filter); ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="control-group">
                        <label for="blacklist">黑名单筛选</label>
                        <select name="blacklist" id="blacklist">
                            <option value="-1" <?php echo ($blacklist_filter == -1) ? 'selected' : ''; ?>>所有设备</option>
                            <option value="1" <?php echo ($blacklist_filter == 1) ? 'selected' : ''; ?>>仅黑名单</option>
                            <option value="0" <?php echo ($blacklist_filter == 0) ? 'selected' : ''; ?>>排除黑名单</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <div class="button-group">
                            <button type="submit" class="button button-primary">搜索</button>
                            <a href="monitor.php" class="button button-warning">重置</a>
                            <button type="button" class="button button-success" onclick="showBlacklistModal()">查看黑名单</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 批量操作 -->
        <div class="batch-actions">
            <div class="select-all-container">
                <input type="checkbox" id="select-all" onchange="toggleSelectAll(this)">
                <label for="select-all">全选此页</label>
            </div>
            <div class="button-group">
                <!-- 粉丝记录按钮 -->
                <button class="button button-info" onclick="showFansRecordsModal()" style="margin-right: 10px;">
                    粉丝记录
                </button>

                <!-- 今日任务按钮 -->
                <button class="button button-success" onclick="showDailyTasksModal()" style="margin-right: 10px;">
                    今日任务
                </button>

                <!-- 批量操作下拉菜单 -->
                <?php if (check_permission('manage_groups') || check_permission('manage_blacklist') || check_permission('manage_devices')): ?>
                <div class="dropdown">
                    <button class="dropdown-toggle" onclick="toggleDropdown('batch-dropdown')">
                         批量操作 <span>▼</span>
                    </button>
                    <div class="dropdown-menu" id="batch-dropdown">
                        <?php if (check_permission('manage_groups')): ?>
                        <button class="dropdown-item" onclick="batchGroup()"> 批量分组</button>
                        <?php endif; ?>
                        <?php if (check_permission('manage_blacklist')): ?>
                        <button class="dropdown-item" onclick="batchBlacklist()"> 批量拉黑</button>
                        <button class="dropdown-item" onclick="batchRemoveBlacklist()"> 批量解除拉黑</button>
                        <?php endif; ?>
                        <?php if (check_permission('manage_devices')): ?>
                        <button class="dropdown-item" onclick="batchDelete()"> 批量删除</button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 分组管理下拉菜单 -->
                <?php if (check_permission('manage_groups')): ?>
                <div class="dropdown">
                    <button class="dropdown-toggle button-success" onclick="toggleDropdown('group-dropdown')">
                         分组管理 <span>▼</span>
                    </button>
                    <div class="dropdown-menu" id="group-dropdown">
                        <button class="dropdown-item" onclick="showCreateGroupModal()"> 创建分组</button>
                        <button class="dropdown-item" onclick="showEditGroupModal()"> 编辑分组</button>
                        <button class="dropdown-item" onclick="showDeleteGroupModal()"> 删除分组</button>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 系统操作 -->
                <?php if (check_permission('manage_blacklist')): ?>
                <button class="button button-purple" onclick="triggerAutoBlacklist()">自动拉黑</button>
                <?php endif; ?>

                <!-- 远程停止功能 -->
                <?php if (check_permission('manage_devices')): ?>
                <div class="dropdown">
                    <button class="dropdown-toggle button-danger" onclick="toggleDropdown('stop-devices-dropdown')">
                        停止设备运行 <span>▼</span>
                    </button>
                    <div class="dropdown-menu" id="stop-devices-dropdown">
                        <button class="dropdown-item" onclick="showStopDevicesModal('all')">停止所有设备</button>
                        <button class="dropdown-item" onclick="showStopDevicesModal('group')">按分组停止</button>
                        <button class="dropdown-item" onclick="showStopDevicesModal('selected')">选择设备停止</button>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 操作员只读提示 -->
                <?php if (!check_permission('manage_groups') && !check_permission('manage_blacklist') && !check_permission('manage_devices')): ?>
                <div class="readonly-notice" style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; font-size: 14px;">
                    <i>您当前为只读权限，仅可查看设备信息</i>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="select-all-table" onchange="toggleSelectAll(this)">
                        </th>
                        <th>设备ID</th>
                        <th>型号</th>
                        <th>Android版本</th>
                        <th>IP地址</th>
                        <th>最后活跃</th>
                        <th>首次注册</th>
                        <th>访问次数</th>
                        <th>分组</th>
                        <th>状态</th>
                        <th>自动拉黑</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            echo "<tr>";

                            // 复选框
                            echo "<td><input type='checkbox' class='device-checkbox' value='" . htmlspecialchars($row['device_id']) . "'></td>";

                            // 设备ID
                            echo "<td><strong>" . htmlspecialchars($row['device_id']) . "</strong></td>";

                            // 型号
                            echo "<td>" . htmlspecialchars($row['device_model'] ?? '未知') . "</td>";

                            // Android版本
                            echo "<td>" . htmlspecialchars($row['android_version'] ?? '未知') . "</td>";

                            // IP地址
                            echo "<td>" . htmlspecialchars($row['ip_address'] ?? '未知') . "</td>";

                            // 最后活跃时间
                            echo "<td>" . ($row['last_active'] ? date('Y-m-d H:i', strtotime($row['last_active'])) : '未知') . "</td>";

                            // 首次注册时间
                            echo "<td>" . ($row['first_login'] ? date('Y-m-d H:i', strtotime($row['first_login'])) : '未知') . "</td>";

                            // 访问次数
                            echo "<td>" . number_format((int)($row['access_count'] ?? 0)) . "</td>";

                            // 分组
                            echo "<td>";
                            if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                                echo "<span class='group-tag'>" . htmlspecialchars($row['device_group']) . "</span>";
                            } else {
                                echo "<span class='group-tag no-group'>无分组</span>";
                            }
                            echo "</td>";

                            // 状态
                            echo "<td>";
                            if ($row['is_blacklisted']) {
                                echo "<span class='status-tag blacklisted'>已拉黑</span>";
                            } else {
                                echo "<span class='status-tag normal'>正常</span>";
                            }
                            echo "</td>";

                            // 自动拉黑状态
                            echo "<td>";
                            if ($row['should_be_blacklisted']) {
                                echo "<span class='status-tag warning'>待拉黑</span>";
                            } else if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                                echo "<span class='status-tag safe'>已分组</span>";
                            } else if ($row['first_seen'] && strtotime($row['first_seen']) > strtotime('-10 minutes')) {
                                $remaining_minutes = 10 - floor((time() - strtotime($row['first_seen'])) / 60);
                                $remaining_seconds = (10 * 60) - (time() - strtotime($row['first_seen']));

                                if ($remaining_minutes > 0) {
                                    echo "<span class='status-tag pending' data-remaining='{$remaining_seconds}'>还有{$remaining_minutes}分钟</span>";
                                } else {
                                    echo "<span class='status-tag warning'>即将拉黑</span>";
                                }
                            } else if (empty($row['device_group']) || $row['device_group'] === '未分组') {
                                echo "<span class='status-tag warning'>未分组</span>";
                            } else {
                                echo "<span class='status-tag normal'>正常</span>";
                            }
                            echo "</td>";

                            // 操作按钮
                            echo "<td>";
                            echo "<div class='dropdown'>";
                            echo "<button class='dropdown-toggle' style='padding: 4px 8px; font-size: 12px; background: #3498db;' onclick='toggleDropdown(\"device-" . htmlspecialchars($row['device_id']) . "\")'>";
                            echo "操作 <span style='font-size: 10px;'>▼</span>";
                            echo "</button>";
                            echo "<div class='dropdown-menu' id='device-" . htmlspecialchars($row['device_id']) . "'>";

                            // 分组选项 - 需要管理分组权限
                            if (check_permission('manage_groups')) {
                                echo "<button class='dropdown-item' onclick='assignGroup(\"" . htmlspecialchars($row['device_id']) . "\")'>分组</button>";
                            }

                            // 拉黑/解除拉黑选项 - 需要管理黑名单权限
                            if (check_permission('manage_blacklist')) {
                                if ($row['is_blacklisted']) {
                                    echo "<button class='dropdown-item' onclick='unblacklistDevice(\"" . htmlspecialchars($row['device_id']) . "\")'>解除拉黑</button>";
                                } else {
                                    echo "<button class='dropdown-item' onclick='blacklistDevice(\"" . htmlspecialchars($row['device_id']) . "\")'>拉黑</button>";
                                }
                            }

                            // 如果有任何操作权限，显示分隔线
                            if (check_permission('manage_groups') || check_permission('manage_blacklist')) {
                                echo "<div class='dropdown-divider'></div>";
                            }

                            // 查看详情 - 所有用户都可以查看
                            echo "<button class='dropdown-item' onclick='viewDeviceDetails(\"" . htmlspecialchars($row['device_id']) . "\")'>详情</button>";

                            // 查看桌面 - 需要查看截图权限
                            if (check_permission('view_screenshots')) {
                                echo "<button class='dropdown-item' onclick='viewDesktop(\"" . htmlspecialchars($row['device_id']) . "\")'>查看桌面</button>";
                            }

                            // 如果是操作员，显示只读提示
                            if (!check_permission('manage_groups') && !check_permission('manage_blacklist') && !check_permission('manage_devices')) {
                                echo "<div class='dropdown-divider'></div>";
                                echo "<div class='dropdown-item' style='color: #856404; font-style: italic; cursor: default;'>只读权限</div>";
                            }

                            echo "</div>";
                            echo "</div>";
                            echo "</td>";

                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='11' style='text-align:center; padding: 30px; color: #7f8c8d;'>没有找到设备记录</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="pagination">
            <?php
            $query_params = $_GET;

            // 首页链接
            $query_params['page'] = 1;
            $first_link = 'monitor.php?' . http_build_query($query_params);

            // 上一页链接
            $query_params['page'] = max(1, $page - 1);
            $prev_link = 'monitor.php?' . http_build_query($query_params);

            // 下一页链接
            $query_params['page'] = min($total_pages, $page + 1);
            $next_link = 'monitor.php?' . http_build_query($query_params);

            // 末页链接
            $query_params['page'] = $total_pages;
            $last_link = 'monitor.php?' . http_build_query($query_params);

            echo "<a href=\"{$first_link}\">&laquo; 首页</a>";
            echo "<a href=\"{$prev_link}\">&lsaquo; 上一页</a>";

            // 显示页码
            $start_page = max(1, $page - 2);
            $end_page = min($total_pages, $page + 2);

            for ($i = $start_page; $i <= $end_page; $i++) {
                $query_params['page'] = $i;
                $link = 'monitor.php?' . http_build_query($query_params);

                if ($i == $page) {
                    echo "<span class=\"active\">{$i}</span>";
                } else {
                    echo "<a href=\"{$link}\">{$i}</a>";
                }
            }

            echo "<a href=\"{$next_link}\">下一页 &rsaquo;</a>";
            echo "<a href=\"{$last_link}\">末页 &raquo;</a>";
            endif;
            ?>
        </div>














    </div>

    <!-- 查看黑名单模态框 -->
    <div id="blacklist-view-modal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <span class="close-btn" onclick="closeModal('blacklist-view-modal')">&times;</span>
            <h2>黑名单设备列表</h2>
            <div class="batch-actions" style="margin: 15px 0; display: flex; justify-content: space-between; align-items: center;">
                <div class="select-all-container">
                    <input type="checkbox" id="blacklist-select-all" onchange="toggleBlacklistSelectAll(this)">
                    <label for="blacklist-select-all">全选</label>
                </div>
                <div class="button-group">
                    <?php if (check_permission('manage_blacklist')): ?>
                    <button class="button button-success" onclick="removeFromBlacklist()">移除黑名单</button>
                    <?php else: ?>
                    <button class="button button-secondary" disabled title="权限不足">移除黑名单</button>
                    <div class="readonly-notice" style="background: #fff3cd; color: #856404; padding: 8px; border-radius: 5px; font-size: 12px; margin-left: 10px;">
                        <i>只读权限，无法移除黑名单</i>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div id="blacklist-content">
                <p>加载中...</p>
            </div>
        </div>
    </div>

    <!-- 批量分组模态框 -->
    <div id="batch-group-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('batch-group-modal')">&times;</span>
            <h2>批量分组</h2>
            <p>已选择 <span id="selected-count-group">0</span> 个设备</p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="batch_group">
                <input type="hidden" name="device_ids" id="batch-device-ids">

                <div class="control-group">
                    <label for="batch_group_name">选择分组：</label>
                    <select name="group_name" id="batch_group_name">
                        <option value="">无分组</option>
                        <?php foreach($groups as $group): ?>
                        <option value="<?php echo htmlspecialchars($group); ?>">
                            <?php echo htmlspecialchars($group); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="control-group">
                    <label for="batch_new_group">或创建新分组：</label>
                    <input type="text" id="batch_new_group" placeholder="输入新分组名称">
                    <button type="button" class="button button-primary" onclick="useBatchNewGroup()">使用新分组</button>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-primary">确认分组</button>
                    <button type="button" class="button button-warning" onclick="closeModal('batch-group-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量拉黑模态框 -->
    <div id="batch-blacklist-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('batch-blacklist-modal')">&times;</span>
            <h2>批量拉黑</h2>
            <p>已选择 <span id="selected-count-blacklist">0</span> 个设备</p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="batch_blacklist">
                <input type="hidden" name="device_ids" id="batch-blacklist-ids">

                <div class="control-group">
                    <label for="blacklist_reason">拉黑原因：</label>
                    <input type="text" name="reason" id="blacklist_reason" placeholder="请输入拉黑原因" required>
                </div>

                <div class="control-group">
                    <label for="blacklist_admin">操作人员：</label>
                    <input type="text" name="admin" id="blacklist_admin" value="管理员" required>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-warning">确认拉黑</button>
                    <button type="button" class="button button-primary" onclick="closeModal('batch-blacklist-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量删除模态框 -->
    <div id="batch-delete-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('batch-delete-modal')">&times;</span>
            <h2>批量删除</h2>
            <p style="color: red;">警告：此操作将永久删除设备的所有信息，包括黑名单记录！</p>
            <p>已选择 <span id="selected-count-delete">0</span> 个设备</p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="batch_delete">
                <input type="hidden" name="device_ids" id="batch-delete-device-ids">

                <div class="control-group">
                    <label>
                        <input type="checkbox" id="confirm-delete" required>
                        我确认要删除这些设备的所有信息
                    </label>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-danger">确认删除</button>
                    <button type="button" class="button button-primary" onclick="closeModal('batch-delete-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 单个设备分组模态框 -->
    <div id="single-group-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('single-group-modal')">&times;</span>
            <h2>设备分组</h2>
            <p>设备ID: <span id="single-device-id"></span></p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="assign_group">
                <input type="hidden" name="device_id" id="single-device-id-input">

                <div class="control-group">
                    <label for="single_group_name">选择分组：</label>
                    <select name="group_name" id="single_group_name">
                        <option value="">无分组</option>
                        <?php foreach($groups as $group): ?>
                        <option value="<?php echo htmlspecialchars($group); ?>">
                            <?php echo htmlspecialchars($group); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="control-group">
                    <label for="single_new_group">或创建新分组：</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="single_new_group" placeholder="输入新分组名称">
                        <button type="button" class="button button-primary" onclick="useSingleNewGroup()">使用</button>
                    </div>
                </div>

                <div class="control-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-success">确认分组</button>
                    <button type="button" class="button button-warning" onclick="closeModal('single-group-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 单个设备拉黑模态框 -->
    <div id="single-blacklist-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('single-blacklist-modal')">&times;</span>
            <h2>拉黑设备</h2>
            <p>设备ID: <span id="blacklist-device-id"></span></p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="blacklist">
                <input type="hidden" name="device_id" id="blacklist-device-id-input">

                <div class="control-group">
                    <label for="single_blacklist_reason">拉黑原因：</label>
                    <input type="text" name="reason" id="single_blacklist_reason" placeholder="请输入拉黑原因" required>
                </div>

                <div class="control-group">
                    <label for="single_blacklist_admin">操作人员：</label>
                    <input type="text" name="admin" id="single_blacklist_admin" value="管理员" required>
                </div>

                <div class="control-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-warning">确认拉黑</button>
                    <button type="button" class="button button-success" onclick="closeModal('single-blacklist-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 删除分组模态框 -->
    <div id="delete-group-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('delete-group-modal')">&times;</span>
            <h2>删除分组</h2>
            <p style="color: #e74c3c; font-weight: 500;">警告：删除分组后，该分组下的所有设备将被移至"无分组"状态！</p>

            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="delete_group">

                <div class="control-group">
                    <label for="delete_group_name">选择要删除的分组：</label>
                    <select name="group_name" id="delete_group_name" required>
                        <option value="">请选择分组</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>
                        <input type="checkbox" id="confirm-delete-group" required>
                        我确认要删除此分组，并将其下的所有设备移至"无分组"状态
                    </label>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-danger">确认删除</button>
                    <button type="button" class="button button-primary" onclick="closeModal('delete-group-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 创建分组模态框 -->
    <div id="create-group-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('create-group-modal')">&times;</span>
            <h2>创建新分组</h2>
            <div class="modal-tip">
                <p style="color: #27ae60; font-weight: 500;">
                    提示：创建新分组后，您可以将设备分配到该分组中进行统一管理。
                </p>
            </div>

            <form method="post" action="monitor.php" id="create-group-form">
                <input type="hidden" name="action" value="create_group">

                <div class="control-group">
                    <label for="create_group_name">
                        <span class="required">*</span> 分组名称：
                    </label>
                    <input type="text"
                           name="group_name"
                           id="create_group_name"
                           placeholder="请输入分组名称（如：测试设备、生产设备、开发环境等）"
                           required
                           maxlength="100"
                           autocomplete="off">
                    <div class="input-hint">
                        <small id="group-name-hint">分组名称不能超过100个字符，不能包含特殊字符</small>
                    </div>
                </div>

                <div class="control-group">
                    <label for="create_group_description">分组描述（可选）：</label>
                    <textarea name="description"
                              id="create_group_description"
                              placeholder="请输入分组的用途、管理范围等描述信息..."
                              rows="4"
                              maxlength="500"
                              style="resize: vertical;"></textarea>
                    <div class="input-hint">
                        <small>描述信息有助于更好地管理和识别分组用途</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="button button-success" id="create-group-submit">
                        <span class="button-icon">✓</span> 创建分组
                    </button>
                    <button type="button" class="button button-secondary" onclick="closeModal('create-group-modal')">
                        <span class="button-icon">✕</span> 取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑分组模态框 -->
    <div id="edit-group-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('edit-group-modal')">&times;</span>
            <h2>编辑分组</h2>
            <div class="modal-tip">
                <p style="color: #3498db; font-weight: 500;">
                    提示：修改分组信息后，该分组下的所有设备都会受到影响。
                </p>
            </div>

            <form method="post" action="monitor.php" id="edit-group-form">
                <input type="hidden" name="action" value="edit_group">
                <input type="hidden" name="original_group_name" id="edit_original_group_name">

                <div class="control-group">
                    <label for="edit_group_select">选择要编辑的分组：</label>
                    <select id="edit_group_select" onchange="loadGroupInfo()" required>
                        <option value="">请选择分组</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="edit_group_name">
                        <span class="required">*</span> 分组名称：
                    </label>
                    <input type="text"
                           name="group_name"
                           id="edit_group_name"
                           placeholder="输入新的分组名称"
                           required
                           maxlength="50">
                </div>

                <div class="control-group">
                    <label for="edit_group_description">分组描述：</label>
                    <textarea name="group_description"
                              id="edit_group_description"
                              placeholder="输入分组描述（可选）"
                              rows="3"
                              maxlength="200"></textarea>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-primary" id="edit-group-submit">
                        <span class="button-icon">✓</span> 保存修改
                    </button>
                    <button type="button" class="button button-secondary" onclick="closeModal('edit-group-modal')">
                        <span class="button-icon">✕</span> 取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 心跳监控批量删除模态框 -->
    <div id="heartbeat-batch-delete-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('heartbeat-batch-delete-modal')">&times;</span>
            <h2>批量删除心跳设备</h2>
            <p style="color: red;">警告：此操作将永久删除设备的所有信息，包括心跳记录、设备记录和黑名单记录！</p>
            <p>已选择 <span id="selected-count-heartbeat-delete">0</span> 个设备</p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="batch_delete_heartbeat_devices">
                <input type="hidden" name="device_ids" id="heartbeat-batch-delete-device-ids">

                <div class="control-group">
                    <label>
                        <input type="checkbox" id="confirm-heartbeat-delete" required>
                        我确认要删除这些设备的所有信息（包括心跳记录、设备记录、黑名单记录）
                    </label>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-danger">确认删除</button>
                    <button type="button" class="button button-primary" onclick="closeModal('heartbeat-batch-delete-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 心跳监控批量拉黑模态框 -->
    <div id="heartbeat-batch-blacklist-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('heartbeat-batch-blacklist-modal')">&times;</span>
            <h2>批量拉黑心跳设备</h2>
            <p>已选择 <span id="selected-count-heartbeat-blacklist">0</span> 个设备</p>
            <form method="post" action="monitor.php">
                <input type="hidden" name="action" value="batch_blacklist_heartbeat_devices">
                <input type="hidden" name="device_ids" id="heartbeat-batch-blacklist-device-ids">

                <div class="control-group">
                    <label for="heartbeat_blacklist_reason">拉黑原因：</label>
                    <select name="blacklist_reason" id="heartbeat_blacklist_reason">
                        <option value="异常行为">异常行为</option>
                        <option value="违规操作">违规操作</option>
                        <option value="恶意设备">恶意设备</option>
                        <option value="测试完成">测试完成</option>
                        <option value="手动拉黑">手动拉黑</option>
                        <option value="其他">其他</option>
                    </select>
                </div>

                <div class="button-group" style="margin-top: 20px;">
                    <button type="submit" class="button button-danger">确认拉黑</button>
                    <button type="button" class="button button-primary" onclick="closeModal('heartbeat-batch-blacklist-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 桌面查看模态框 -->
    <div id="desktop-view-modal" class="modal">
        <div class="modal-content" style="max-width: 95vw; max-height: 95vh; width: auto; height: auto; padding: 15px;">
            <span class="close-btn" onclick="closeDesktopView()" title="关闭桌面查看并停止截图">&times;</span>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h2 id="desktop-device-title" style="margin: 0; font-size: 18px;">设备桌面</h2>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <span id="desktop-status" style="padding: 4px 8px; border-radius: 4px; font-size: 12px; background: #e9ecef;">等待连接...</span>
                    <button id="desktop-refresh-btn" class="button button-primary" onclick="refreshDesktop()" style="padding: 6px 12px; font-size: 12px;" title="等待设备上传最新截图">刷新</button>
                    <button id="desktop-history-btn" class="button button-secondary" onclick="showScreenshotHistory()" style="padding: 6px 12px; font-size: 12px;" title="查看历史截图">历史</button>
                    <button id="debug-btn" class="button button-secondary" onclick="toggleDebugInfo()" style="padding: 6px 12px; font-size: 12px;" title="显示调试信息">调试</button>
                </div>
            </div>
            <div id="desktop-content" style="text-align: center; min-height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 8px; position: relative; overflow: hidden;">
                <div id="desktop-loading" style="display: none;">
                    <div style="font-size: 24px; margin-bottom: 10px; font-weight: bold;">加载中</div>
                    <p>正在获取设备桌面...</p>
                </div>
                <div id="desktop-error" style="display: none; color: #e74c3c;">
                    <div style="font-size: 24px; margin-bottom: 10px; font-weight: bold;">错误</div>
                    <p>无法获取设备桌面</p>
                    <small>请确保设备在线且脚本正在运行</small>
                </div>
                <div id="desktop-image-container" style="display: none; width: 100%; height: 100%; position: relative;">
                    <img id="desktop-image" style="
                        max-width: 90%;
                        max-height: 70vh;
                        width: auto;
                        height: auto;
                        object-fit: contain;
                        border-radius: 4px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        display: block;
                        margin: 0 auto;
                        cursor: zoom-in;
                    " alt="设备桌面截图">
                    <div style="margin-top: 10px; font-size: 12px; color: #6c757d; text-align: center;">
                        <span>更新时间: </span><span id="desktop-update-time">--</span>
                        <span style="margin-left: 15px;">文件大小: </span><span id="desktop-file-size">--</span>
                        <span style="margin-left: 15px;">尺寸: </span><span id="desktop-image-size">--</span>
                    </div>

                    <!-- 调试信息面板 -->
                    <div id="debug-info" style="display: none; margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 11px; color: #495057;">
                        <div style="font-weight: bold; margin-bottom: 5px;">调试信息:</div>
                        <div id="debug-content">等待调试数据...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史截图模态框 -->
    <div id="screenshot-history-modal" class="modal">
        <div class="modal-content" style="max-width: 95vw; max-height: 95vh; width: auto; height: auto; padding: 15px;">
            <span class="close-btn" onclick="closeScreenshotHistory()">&times;</span>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h2 id="history-device-title" style="margin: 0; font-size: 18px;">历史截图</h2>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <span id="history-info" style="font-size: 12px; color: #6c757d;">加载中...</span>
                    <button id="history-refresh-btn" class="button button-primary" onclick="refreshScreenshotHistory()" style="padding: 6px 12px; font-size: 12px;">刷新</button>
                </div>
            </div>

            <!-- 历史截图列表 -->
            <div id="screenshot-history-content" style="max-height: 75vh; overflow-y: auto;">
                <div id="history-loading" style="text-align: center; padding: 40px;">
                    <div style="font-size: 24px; margin-bottom: 10px; font-weight: bold;">加载中</div>
                    <p>正在加载历史截图...</p>
                </div>

                <div id="history-error" style="display: none; text-align: center; padding: 40px; color: #e74c3c;">
                    <div style="font-size: 24px; margin-bottom: 10px; font-weight: bold;">错误</div>
                    <p>加载历史截图失败</p>
                </div>

                <div id="history-list" style="display: none;">
                    <!-- 历史截图网格 -->
                    <div id="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                        <!-- 动态生成截图项 -->
                    </div>

                    <!-- 分页控制 -->
                    <div id="history-pagination" style="text-align: center; padding: 20px; border-top: 1px solid #e9ecef;">
                        <button id="prev-page-btn" class="button button-secondary" onclick="loadPreviousPage()" style="margin-right: 10px;">← 上一页</button>
                        <span id="page-info" style="margin: 0 15px; font-size: 14px;">第 1 页，共 1 页</span>
                        <button id="next-page-btn" class="button button-secondary" onclick="loadNextPage()">下一页 →</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史截图详情模态框 -->
    <div id="screenshot-detail-modal" class="modal">
        <div class="modal-content" style="max-width: 95vw; max-height: 95vh; width: auto; height: auto; padding: 15px;">
            <span class="close-btn" onclick="closeScreenshotDetail()">&times;</span>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h2 id="detail-title" style="margin: 0; font-size: 18px;">截图详情</h2>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <span id="detail-time" style="font-size: 12px; color: #6c757d;">--</span>
                </div>
            </div>

            <div id="screenshot-detail-content" style="text-align: center; min-height: 400px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 8px; position: relative; overflow: hidden;">
                <div id="detail-loading" style="display: none;">
                    <div style="font-size: 24px; margin-bottom: 10px; font-weight: bold;">加载中</div>
                    <p>正在加载截图...</p>
                </div>

                <div id="detail-image-container" style="display: none; width: 100%; height: 100%; position: relative;">
                    <img id="detail-image" style="
                        max-width: 90%;
                        max-height: 70vh;
                        width: auto;
                        height: auto;
                        object-fit: contain;
                        border-radius: 4px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        display: block;
                        margin: 0 auto;
                        cursor: zoom-in;
                    " alt="历史截图">
                    <div style="margin-top: 10px; font-size: 12px; color: #6c757d; text-align: center;">
                        <span>上传时间: </span><span id="detail-upload-time">--</span>
                        <span style="margin-left: 15px;">文件大小: </span><span id="detail-file-size">--</span>
                        <span style="margin-left: 15px;">尺寸: </span><span id="detail-image-size">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 心跳统计公告弹窗 -->
    <div id="heartbeat-stats-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px; width: 90%;">
            <span class="close-btn" onclick="closeHeartbeatStatsModal()">&times;</span>
            <div class="heartbeat-stats-header">
                <h2>设备心跳状态总览</h2>
                <p style="color: #6c757d; margin: 5px 0 20px 0;">实时监控各分组设备运行状态</p>
            </div>

            <!-- 总体统计 -->
            <div id="total-stats-section" class="stats-summary">
                <div class="stat-card total">
                    <div class="stat-number" id="total-devices-count">0</div>
                    <div class="stat-label">总设备数</div>
                </div>
                <div class="stat-card online">
                    <div class="stat-number" id="total-online-count">0</div>
                    <div class="stat-label">在线设备</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="total-warning-count">0</div>
                    <div class="stat-label">警告设备</div>
                </div>
                <div class="stat-card offline">
                    <div class="stat-number" id="total-offline-count">0</div>
                    <div class="stat-label">离线设备</div>
                </div>
                <div class="stat-card running">
                    <div class="stat-number" id="total-running-count">0</div>
                    <div class="stat-label">运行中</div>
                </div>
            </div>

            <!-- 分组详细统计 -->
            <div class="group-stats-section">
                <h3>分组详细统计</h3>
                <div id="group-stats-content" class="group-stats-grid">
                    <div class="loading-message">
                        <p>正在加载分组统计数据...</p>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="modal-actions">
                <button class="button button-primary" onclick="refreshHeartbeatStats()">刷新数据</button>
                <button class="button button-secondary" onclick="closeHeartbeatStatsModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 粉丝记录模态框 -->
    <div id="fans-records-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 1200px; width: 90%;">
            <div class="modal-header">
                <h2>设备粉丝记录</h2>
                <span class="close" onclick="closeFansRecordsModal()">&times;</span>
            </div>

            <!-- 筛选器 -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h3 style="margin: 0 0 15px 0; color: #495057;">筛选条件</h3>
                <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">设备筛选</label>
                        <input type="text" id="fans-device-filter" placeholder="设备ID或型号" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">设备分组</label>
                        <select id="fans-group-filter" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                            <option value="">全部分组</option>
                        </select>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">日期范围</label>
                        <input type="date" id="fans-date-start" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">至</label>
                        <input type="date" id="fans-date-end" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">&nbsp;</label>
                        <button onclick="loadFansRecords()" style="padding: 8px 16px; border: none; border-radius: 4px; font-size: 14px; cursor: pointer; background: #007bff; color: white;">筛选</button>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div id="fans-stats" style="background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                <div class="loading-message">
                    <p>正在加载统计数据...</p>
                </div>
            </div>

            <!-- 粉丝记录列表 -->
            <div id="fans-records-content" style="background: white; border-radius: 8px; overflow: hidden; border: 1px solid #e9ecef; overflow-x: auto;">
                <div class="loading-message">
                    <p>正在加载粉丝记录数据...</p>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="modal-actions">
                <button class="button button-primary" onclick="loadFansRecords()">刷新数据</button>
                <button class="button button-secondary" onclick="closeFansRecordsModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 设备历史粉丝记录模态框 -->
    <div id="device-fans-history-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 1000px; width: 90%;">
            <div class="modal-header">
                <h2 id="device-fans-history-title">设备历史粉丝记录</h2>
                <span class="close" onclick="closeDeviceFansHistoryModal()">&times;</span>
            </div>

            <!-- 设备信息 -->
            <div id="device-fans-history-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <div class="loading-message">
                    <p>正在加载设备信息...</p>
                </div>
            </div>

            <!-- 历史记录列表 -->
            <div id="device-fans-history-content" style="background: white; border-radius: 8px; overflow: hidden; border: 1px solid #e9ecef; overflow-x: auto;">
                <div class="loading-message">
                    <p>正在加载历史记录数据...</p>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="modal-actions">
                <button class="button button-primary" onclick="refreshDeviceFansHistory()">刷新数据</button>
                <button class="button button-secondary" onclick="closeDeviceFansHistoryModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 今日任务模态框 -->
    <div id="daily-tasks-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 1200px; width: 90%;">
            <div class="modal-header">
                <h2>设备今日任务</h2>
                <span class="close" onclick="closeDailyTasksModal()">&times;</span>
            </div>

            <!-- 筛选器 -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">设备筛选</label>
                        <input type="text" id="tasks-device-filter" placeholder="设备ID或型号" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; width: 200px;">
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">设备分组</label>
                        <select id="tasks-group-filter" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; width: 150px;">
                            <option value="">全部分组</option>
                        </select>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">日期筛选</label>
                        <input type="date" id="tasks-date-filter" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">今日执行任务设备数量</label>
                        <div style="padding: 8px 12px; background: #e3f2fd; border: 1px solid #90caf9; border-radius: 4px; font-size: 14px; font-weight: 600; color: #1565c0; min-width: 120px; text-align: center;">
                            <span id="daily-tasks-device-count">-</span> 台
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <label style="font-size: 14px; font-weight: 500; color: #495057;">&nbsp;</label>
                        <button onclick="loadDailyTasks()" class="button button-primary">筛选</button>
                    </div>
                </div>
            </div>

            <!-- 今日任务列表 -->
            <div id="daily-tasks-content" style="background: white; border-radius: 8px; overflow: hidden; border: 1px solid #e9ecef; overflow-x: auto;">
                <div class="loading-message">
                    <p>正在加载今日任务数据...</p>
                </div>
            </div>

            <div class="modal-actions">
                <button onclick="loadDailyTasks()" class="button button-primary">刷新</button>
                <button onclick="closeDailyTasksModal()" class="button button-secondary">关闭</button>
            </div>
        </div>
    </div>

    <!-- 停止设备运行模态框 -->
    <div id="stop-devices-modal" class="modal">
        <div class="modal-content" style="max-width: 800px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header">
                <h2 id="stop-devices-title">停止设备运行</h2>
                <span class="close" onclick="closeStopDevicesModal()">&times;</span>
            </div>

            <div class="modal-body">
                <div id="stop-mode-selection" style="margin-bottom: 20px;">
                    <div class="control-group">
                        <label>停止模式：</label>
                        <div style="display: flex; gap: 15px; margin-top: 10px;">
                            <label style="display: flex; align-items: center; gap: 5px;">
                                <input type="radio" name="stop_mode" value="all" checked onchange="updateStopMode()">
                                <span>停止所有在线设备</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 5px;">
                                <input type="radio" name="stop_mode" value="group" onchange="updateStopMode()">
                                <span>按分组停止</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 5px;">
                                <input type="radio" name="stop_mode" value="selected" onchange="updateStopMode()">
                                <span>选择设备停止</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 分组选择 -->
                <div id="group-selection" style="display: none; margin-bottom: 20px;">
                    <div class="control-group">
                        <label for="stop_group_select">选择分组：</label>
                        <select id="stop_group_select" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">请选择分组</option>
                        </select>
                    </div>
                </div>

                <!-- 设备选择 -->
                <div id="device-selection" style="display: none; margin-bottom: 20px;">
                    <div class="control-group">
                        <label>选择设备：</label>

                        <!-- 搜索框 -->
                        <div style="margin: 10px 0;">
                            <input type="text" id="device-search" placeholder="搜索设备ID..."
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;"
                                   oninput="filterDeviceList()">
                        </div>

                        <!-- 设备统计信息 -->
                        <div id="device-stats" style="margin: 10px 0; padding: 8px; background: #e9ecef; border-radius: 4px; font-size: 12px; color: #495057;">
                            <span id="total-devices">总设备: 0</span> |
                            <span id="visible-devices">显示: 0</span> |
                            <span id="selected-devices">已选: 0</span>
                        </div>

                        <div id="device-list" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px; background: #f8f9fa;">
                            <div class="loading-message">正在加载设备列表...</div>
                        </div>
                    </div>
                </div>

                <!-- 确认信息 -->
                <div id="stop-confirmation" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <span style="font-size: 20px; font-weight: bold;">[!]</span>
                        <strong style="color: #856404;">确认停止设备运行</strong>
                    </div>
                    <p style="margin: 0; color: #856404; font-size: 14px;">
                        此操作将向选中的设备发送停止指令，设备将立即停止当前运行的脚本。请确认您要执行此操作。
                    </p>
                    <div id="stop-target-info" style="margin-top: 10px; font-weight: bold; color: #856404;">
                        目标：所有在线设备
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="button-group">
                    <button type="button" class="button button-danger" onclick="executeStopDevices()" id="confirm-stop-btn">
                        确认停止
                    </button>
                    <button type="button" class="button button-secondary" onclick="closeStopDevicesModal()">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>

    <?php if (($_GET['view'] ?? '') == 'cron_tasks'): ?>
    <!-- 定时任务管理页面 -->
    <div class="controls-section">
        <h2 style="margin-bottom: 20px; color: #2c3e50;">定时任务管理</h2>
        <p style="color: #7f8c8d; margin-bottom: 30px;">管理设备使用时间限制检查的定时任务，确保过期设备能够自动拉黑。</p>

        <!-- 任务状态显示区域 -->
        <div id="cron-status-section" style="margin-bottom: 30px;">
            <div class="stat-card" style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 8px;">
                <h3 style="margin-bottom: 15px; color: #495057;">任务状态</h3>
                <div id="cron-status-content">
                    <p>正在检查任务状态...</p>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="button-group" style="margin-bottom: 30px;">
            <button class="button button-success" onclick="createCronTask()">创建定时任务</button>
            <button class="button button-danger" onclick="deleteCronTask()">删除定时任务</button>
            <button class="button button-info" onclick="checkCronStatus()">检查任务状态</button>
            <button class="button button-warning" onclick="runCronNow()">立即执行检查</button>
        </div>

        <!-- 任务信息说明 -->
        <div class="info-section" style="background: #e9ecef; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
            <h3 style="margin-bottom: 15px; color: #495057;">任务说明</h3>
            <ul style="margin: 0; padding-left: 20px; color: #6c757d;">
                <li><strong>任务名称：</strong>设备使用时间限制检查</li>
                <li><strong>执行频率：</strong>每小时一次</li>
                <li><strong>执行脚本：</strong>check_expired_devices_cron.php</li>
                <li><strong>主要功能：</strong>自动检查过期设备并加入黑名单</li>
                <li><strong>日志文件：</strong>expired_devices_check.log 和 expired_devices_error.log</li>
            </ul>
        </div>

        <!-- 最近日志显示 -->
        <div class="log-section">
            <h3 style="margin-bottom: 15px; color: #495057;">最近执行日志</h3>
            <div class="log-container" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; font-family: 'Courier New', monospace; font-size: 12px; max-height: 400px; overflow-y: auto;">
                <div id="log-content">
                    <p>点击"检查任务状态"或"立即执行检查"来查看日志</p>
                </div>
            </div>
        </div>

        <!-- 操作结果显示区域 -->
        <div id="cron-message-area" style="margin-top: 20px;"></div>
    </div>

    <?php endif; ?>

    <script>
        // 显示黑名单模态框
        function showBlacklistModal() {
            document.getElementById('blacklist-view-modal').style.display = 'block';
            loadBlacklistData();
        }

        // 加载黑名单数据
        function loadBlacklistData() {
            fetch('monitor.php?action=get_blacklist')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    let content = '';
                    if (data.error) {
                        content = '<p style="color: red;">加载失败: ' + data.error + '</p>';
                    } else if (data.length > 0) {
                        content = '<div style="overflow-x: auto;"><table class="blacklist-table" style="width: 100%; border-collapse: collapse; min-width: 600px;">';
                        content += '<thead><tr style="background: #f8f9fa; position: sticky; top: 0; z-index: 1;">';
                        content += '<th style="padding: 8px; border: 1px solid #ddd; width: 40px; text-align: center;"><input type="checkbox" id="select-all-blacklist" onchange="toggleBlacklistSelectAll(this)"></th>';
                        content += '<th style="padding: 8px; border: 1px solid #ddd; min-width: 120px;">设备ID</th>';
                        content += '<th style="padding: 8px; border: 1px solid #ddd; min-width: 150px;">拉黑原因</th>';
                        content += '<th style="padding: 8px; border: 1px solid #ddd; min-width: 140px;">拉黑时间</th>';
                        content += '<th style="padding: 8px; border: 1px solid #ddd; min-width: 80px;">操作人员</th>';
                        content += '</tr></thead>';
                        content += '<tbody>';

                        data.forEach(item => {
                            const formattedTime = new Date(item.blacklist_time).toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                            content += `<tr style="border-bottom: 1px solid #eee;">
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;"><input type="checkbox" class="blacklist-checkbox" data-id="${item.device_id}"></td>
                                <td style="padding: 8px; border: 1px solid #ddd; word-break: break-all; font-family: monospace; font-size: 12px;">${item.device_id}</td>
                                <td style="padding: 8px; border: 1px solid #ddd; word-wrap: break-word; max-width: 200px;">${item.blacklist_reason || '无'}</td>
                                <td style="padding: 8px; border: 1px solid #ddd; font-size: 12px;">${formattedTime}</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${item.blacklisted_by || '未知'}</td>
                            </tr>`;
                        });

                        content += '</tbody></table></div>';
                    } else {
                        content = '<p style="text-align: center; padding: 20px;">暂无黑名单设备</p>';
                    }

                    document.getElementById('blacklist-content').innerHTML = content;
                })
                .catch(error => {
                    console.error('加载黑名单数据失败:', error);
                    document.getElementById('blacklist-content').innerHTML = '<p style="color: red;">加载失败: ' + error.message + '</p>';
                });
        }

        // 全选/取消全选黑名单设备
        function toggleBlacklistSelectAll(selectAllCheckbox) {
            const checkboxes = document.querySelectorAll('.blacklist-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        }

        // 移除黑名单
        function removeFromBlacklist() {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('移除黑名单');
                return;
            }

            const checkboxes = document.querySelectorAll('.blacklist-checkbox:checked');
            const selectedIds = [];

            checkboxes.forEach(checkbox => {
                selectedIds.push(checkbox.getAttribute('data-id'));
            });

            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            if (confirm(`确定要将选中的 ${selectedIds.length} 个设备从黑名单中移除吗？`)) {
                // 发送请求移除黑名单
                fetch('remove_blacklist.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'device_ids=' + selectedIds.join(',')
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`成功移除 ${data.count} 个设备的黑名单状态`);
                        loadBlacklistData(); // 重新加载黑名单数据
                        // 如果当前页面有这些设备，刷新页面
                        window.location.reload();
                    } else {
                        alert('移除失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    alert('请求失败: ' + error.message);
                });
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            console.log('关闭模态框:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            } else {
                console.error('模态框未找到:', modalId);
            }
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    const modalId = modal.id;
                    closeModal(modalId);
                }
            });
        });

        // 用户权限配置
        const userPermissions = {
            manage_groups: <?php echo check_permission('manage_groups') ? 'true' : 'false'; ?>,
            manage_blacklist: <?php echo check_permission('manage_blacklist') ? 'true' : 'false'; ?>,
            manage_devices: <?php echo check_permission('manage_devices') ? 'true' : 'false'; ?>,
            clear_logs: <?php echo check_permission('clear_logs') ? 'true' : 'false'; ?>,
            view_screenshots: <?php echo check_permission('view_screenshots') ? 'true' : 'false'; ?>
        };

        // 权限检查函数
        function checkPermission(permission) {
            return userPermissions[permission] === true;
        }

        // 显示权限不足提示
        function showPermissionDenied(action) {
            alert(`权限不足：您没有执行"${action}"操作的权限。\n\n请联系管理员获取相应权限。`);
        }

        // 访问系统管理入口（仅系统管理员）
        function accessAdminIndex() {
            // 创建身份验证弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.6); z-index: 10000; display: flex;
                align-items: center; justify-content: center;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white; border-radius: 8px; padding: 30px;
                max-width: 400px; width: 90%; box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                text-align: center;
            `;

            content.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <div style="font-size: 24px; margin-bottom: 15px; font-weight: bold;">管理员验证</div>
                    <h3 style="margin: 0 0 10px 0; color: #2c3e50;">系统管理员身份验证</h3>
                    <p style="color: #666; font-size: 14px; margin: 0;">请输入管理员密码以访问系统管理入口</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <input type="password" id="adminPassword" placeholder="请输入管理员密码"
                           style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
                    <div id="passwordError" style="color: #dc3545; font-size: 12px; margin-top: 5px; display: none;"></div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button onclick="verifyAdminAccess()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        验证并访问
                    </button>
                    <button onclick="this.closest('.admin-modal').remove()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        取消
                    </button>
                </div>

                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
                    <strong>安全提示：</strong>系统管理入口包含敏感功能，请确保您有权限访问
                </div>
            `;

            modal.className = 'admin-modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            // 聚焦到密码输入框
            setTimeout(() => {
                document.getElementById('adminPassword').focus();
            }, 100);

            // 支持回车键提交
            document.getElementById('adminPassword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyAdminAccess();
                }
            });
        }

        // 验证管理员访问权限
        function verifyAdminAccess() {
            const password = document.getElementById('adminPassword').value;
            const errorDiv = document.getElementById('passwordError');

            if (!password) {
                errorDiv.textContent = '请输入密码';
                errorDiv.style.display = 'block';
                return;
            }

            // 发送验证请求
            fetch('verify_admin_access.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    password: password,
                    action: 'verify_admin_index_access'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 验证成功，跳转到index.php
                    document.querySelector('.admin-modal').remove();
                    window.open('index.php', '_blank');
                } else {
                    errorDiv.textContent = data.error || '密码错误，请重试';
                    errorDiv.style.display = 'block';
                    document.getElementById('adminPassword').value = '';
                    document.getElementById('adminPassword').focus();
                }
            })
            .catch(error => {
                console.error('验证请求失败:', error);
                errorDiv.textContent = '验证请求失败，请重试';
                errorDiv.style.display = 'block';
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化分组功能');
            console.log('用户权限:', userPermissions);

            // 检查必要的元素是否存在
            const createModal = document.getElementById('create-group-modal');
            const deleteModal = document.getElementById('delete-group-modal');

            if (!createModal) {
                console.error('创建分组模态框未找到');
            }

            if (!deleteModal) {
                console.error('删除分组模态框未找到');
            }

            // 初始化倒计时功能
            initializeCountdown();

            // 测试拉黑函数是否存在
            if (typeof blacklistDevice === 'function') {
                console.log('[OK] blacklistDevice函数已正确加载');
            } else {
                console.error('[ERROR] blacklistDevice函数未找到');
            }

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.dropdown')) {
                    closeAllDropdowns();
                }
            });
        });

        // 下拉菜单控制函数
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (!dropdown) return;

            // 关闭其他下拉菜单
            closeAllDropdowns(dropdownId);

            // 切换当前下拉菜单
            dropdown.classList.toggle('show');

            // 如果菜单被显示，检查位置并调整
            if (dropdown.classList.contains('show')) {
                adjustDropdownPosition(dropdown);
            }
        }

        // 调整下拉菜单位置
        function adjustDropdownPosition(dropdown) {
            const rect = dropdown.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const dropdownHeight = dropdown.offsetHeight;

            // 如果下拉菜单底部超出视窗，向上显示
            if (rect.bottom > viewportHeight) {
                dropdown.classList.add('show-below');
                dropdown.style.bottom = '100%';
                dropdown.style.top = 'auto';
            } else {
                dropdown.classList.remove('show-below');
                dropdown.style.bottom = 'auto';
                dropdown.style.top = '100%';
            }
        }

        function closeAllDropdowns(exceptId = null) {
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            dropdowns.forEach(dropdown => {
                if (dropdown.id !== exceptId) {
                    dropdown.classList.remove('show');
                }
            });
        }



        // 初始化倒计时功能
        function initializeCountdown() {
            // 查找所有带有倒计时的元素
            const countdownElements = document.querySelectorAll('.status-tag.pending[data-remaining]');

            if (countdownElements.length > 0) {
                console.log(`找到 ${countdownElements.length} 个倒计时元素`);

                // 每秒更新倒计时
                setInterval(function() {
                    updateCountdowns();
                }, 1000);
            }
        }

        // 更新倒计时显示
        function updateCountdowns() {
            const countdownElements = document.querySelectorAll('.status-tag.pending[data-remaining]');

            countdownElements.forEach(function(element) {
                const remaining = parseInt(element.getAttribute('data-remaining'));
                const newRemaining = remaining - 1;

                if (newRemaining <= 0) {
                    // 倒计时结束，标记为待拉黑
                    element.className = 'status-tag warning';
                    element.innerHTML = '待拉黑';
                    element.removeAttribute('data-remaining');

                    // 可以在这里添加自动刷新页面的逻辑
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    // 更新倒计时
                    const minutes = Math.floor(newRemaining / 60);
                    const seconds = newRemaining % 60;

                    if (minutes > 0) {
                        element.innerHTML = `⏳ 还有${minutes}分${seconds}秒`;
                    } else {
                        element.innerHTML = `⏳ 还有${seconds}秒`;
                    }

                    element.setAttribute('data-remaining', newRemaining);

                    // 当剩余时间少于1分钟时，改变颜色
                    if (newRemaining < 60) {
                        element.className = 'status-tag warning';
                    }
                }
            });
        }

        // 获取选中的设备ID
        function getSelectedDeviceIds() {
            const checkboxes = document.querySelectorAll('.device-checkbox:checked');
            const selectedIds = [];
            checkboxes.forEach(checkbox => {
                selectedIds.push(checkbox.value);
            });
            return selectedIds;
        }

        // 全选/取消全选
        function toggleSelectAll(selectAllCheckbox) {
            const checkboxes = document.querySelectorAll('.device-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        }

        // 获取最新的分组列表
        function refreshGroupOptions() {
            fetch('get_groups.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新批量分组选择框
                        const batchSelect = document.getElementById('batch_group_name');
                        const singleSelect = document.getElementById('single_group_name');

                        // 清空现有选项（保留"无分组"选项）
                        batchSelect.innerHTML = '<option value="">无分组</option>';
                        singleSelect.innerHTML = '<option value="">无分组</option>';

                        // 添加新的分组选项
                        data.groups.forEach(group => {
                            const batchOption = new Option(group, group);
                            const singleOption = new Option(group, group);
                            batchSelect.add(batchOption);
                            singleSelect.add(singleOption);
                        });
                    }
                })
                .catch(error => {
                    console.error('获取分组列表失败:', error);
                });
        }

        // 批量分组
        function batchGroup() {
            if (!checkPermission('manage_groups')) {
                showPermissionDenied('批量分组');
                return;
            }

            const selectedIds = getSelectedDeviceIds();

            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            // 刷新分组选项
            refreshGroupOptions();

            document.getElementById('selected-count-group').textContent = selectedIds.length;
            document.getElementById('batch-device-ids').value = selectedIds.join(',');
            document.getElementById('batch-group-modal').style.display = 'block';
        }

        // 批量拉黑
        function batchBlacklist() {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('批量拉黑');
                return;
            }

            const selectedIds = getSelectedDeviceIds();
            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            document.getElementById('selected-count-blacklist').textContent = selectedIds.length;
            document.getElementById('batch-blacklist-ids').value = selectedIds.join(',');
            document.getElementById('batch-blacklist-modal').style.display = 'block';
        }

        // 批量解除拉黑
        function batchRemoveBlacklist() {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('批量解除拉黑');
                return;
            }

            const selectedIds = getSelectedDeviceIds();
            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            // 过滤出在黑名单中的设备
            const blacklistedDevices = selectedIds.filter(deviceId => {
                const row = document.querySelector(`tr[data-device-id="${deviceId}"]`);
                if (row) {
                    // 查找状态标签，检查是否为已拉黑状态
                    const statusTag = row.querySelector('.status-tag.blacklisted');
                    if (statusTag) {
                        return true;
                    }
                    // 备用方案：检查文本内容
                    const statusCell = row.querySelector('.status-tag');
                    return statusCell && statusCell.textContent.includes('已拉黑');
                }
                return false;
            });

            if (blacklistedDevices.length === 0) {
                alert('所选设备中没有被拉黑的设备');
                return;
            }

            if (confirm(`确定要解除 ${blacklistedDevices.length} 个设备的拉黑状态吗？`)) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'batch_remove_blacklist';
                form.appendChild(actionInput);

                const deviceIdsInput = document.createElement('input');
                deviceIdsInput.type = 'hidden';
                deviceIdsInput.name = 'device_ids';
                deviceIdsInput.value = blacklistedDevices.join(',');
                form.appendChild(deviceIdsInput);

                document.body.appendChild(form);
                form.submit();
            }
        }

        // 批量删除
        function batchDelete() {
            if (!checkPermission('manage_devices')) {
                showPermissionDenied('批量删除');
                return;
            }

            const selectedIds = getSelectedDeviceIds();
            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            document.getElementById('selected-count-delete').textContent = selectedIds.length;
            document.getElementById('batch-delete-device-ids').value = selectedIds.join(',');
            document.getElementById('batch-delete-modal').style.display = 'block';
        }

        // 触发自动拉黑检查
        function triggerAutoBlacklist() {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('自动拉黑');
                return;
            }

            if (!confirm('确定要执行自动拉黑检查吗？\n\n此操作将：\n1. 检查所有10分钟前注册且未分组的设备\n2. 自动将这些设备加入黑名单\n3. 刷新页面显示最新状态')) {
                return;
            }

            // 显示加载状态
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '执行中...';
            button.disabled = true;

            // 发送请求到服务器执行自动拉黑
            fetch('monitor.php?action=trigger_auto_blacklist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'trigger_auto_blacklist=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`自动拉黑执行完成！\n\n处理结果：\n- 检查了 ${data.checked_count} 个设备\n- 拉黑了 ${data.blacklisted_count} 个设备\n\n页面将自动刷新显示最新状态。`);
                    window.location.reload();
                } else {
                    alert('自动拉黑执行失败：' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('自动拉黑请求失败:', error);
                alert('自动拉黑执行失败：网络错误');
            })
            .finally(() => {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // 使用新分组名称（批量）
        function useBatchNewGroup() {
            const newGroupName = document.getElementById('batch_new_group').value.trim();
            if (newGroupName) {
                const selectElement = document.getElementById('batch_group_name');

                // 检查是否已存在该选项
                let optionExists = false;
                for (let i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].value === newGroupName) {
                        optionExists = true;
                        break;
                    }
                }

                if (!optionExists) {
                    // 添加新选项
                    const option = new Option(newGroupName, newGroupName);
                    selectElement.add(option);

                    // 同时添加到单个设备分组选择框
                    const singleSelect = document.getElementById('single_group_name');
                    const singleOption = new Option(newGroupName, newGroupName);
                    singleSelect.add(singleOption);
                }
            }
        }

        // 单个设备分组
        function assignGroup(deviceId) {
            if (!checkPermission('manage_groups')) {
                showPermissionDenied('设备分组');
                return;
            }

            // 刷新分组选项
            refreshGroupOptions();

            document.getElementById('single-device-id').textContent = deviceId;
            document.getElementById('single-device-id-input').value = deviceId;

            // 重置表单
            document.getElementById('single_group_name').value = '';
            document.getElementById('single_new_group').value = '';

            // 显示模态框
            document.getElementById('single-group-modal').style.display = 'block';
        }

        // 单个设备拉黑
        function blacklistDevice(deviceId) {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('设备拉黑');
                return;
            }

            console.log('blacklistDevice函数被调用，设备ID:', deviceId);

            try {
                // 检查必要的元素是否存在
                const deviceIdSpan = document.getElementById('blacklist-device-id');
                const deviceIdInput = document.getElementById('blacklist-device-id-input');
                const reasonInput = document.getElementById('single_blacklist_reason');
                const adminInput = document.getElementById('single_blacklist_admin');
                const modal = document.getElementById('single-blacklist-modal');

                if (!deviceIdSpan) {
                    console.error('blacklist-device-id元素未找到');
                    return;
                }
                if (!deviceIdInput) {
                    console.error('blacklist-device-id-input元素未找到');
                    return;
                }
                if (!reasonInput) {
                    console.error('single_blacklist_reason元素未找到');
                    return;
                }
                if (!adminInput) {
                    console.error('single_blacklist_admin元素未找到');
                    return;
                }
                if (!modal) {
                    console.error('single-blacklist-modal元素未找到');
                    return;
                }

                // 设置设备ID
                deviceIdSpan.textContent = deviceId;
                deviceIdInput.value = deviceId;

                // 重置表单
                reasonInput.value = '';
                adminInput.value = '管理员';

                // 显示模态框
                modal.style.display = 'block';
                console.log('拉黑模态框已显示');

            } catch (error) {
                console.error('blacklistDevice函数执行出错:', error);
                alert('拉黑功能出现错误，请刷新页面重试');
            }
        }

        // 使用新分组名称（单个设备）
        function useSingleNewGroup() {
            const newGroupName = document.getElementById('single_new_group').value.trim();
            if (newGroupName) {
                const selectElement = document.getElementById('single_group_name');

                // 检查是否已存在该选项
                let optionExists = false;
                for (let i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].value === newGroupName) {
                        optionExists = true;
                        break;
                    }
                }

                if (!optionExists) {
                    // 添加新选项
                    const option = new Option(newGroupName, newGroupName);
                    selectElement.add(option);
                }

                // 选中新分组
                selectElement.value = newGroupName;

                // 清空输入框
                document.getElementById('single_new_group').value = '';
            } else {
                alert('请输入分组名称');
            }
        }

        // 显示创建分组模态框
        function showCreateGroupModal() {
            if (!checkPermission('manage_groups')) {
                showPermissionDenied('创建分组');
                return;
            }

            console.log('显示创建分组模态框');

            // 重置表单
            const createGroupForm = document.getElementById('create-group-form');
            if (createGroupForm) {
                createGroupForm.reset();
            }

            // 清空输入框
            const groupNameInput = document.getElementById('create_group_name');
            const groupDescInput = document.getElementById('create_group_description');

            if (groupNameInput) groupNameInput.value = '';
            if (groupDescInput) groupDescInput.value = '';

            // 显示模态框
            const modal = document.getElementById('create-group-modal');
            if (modal) {
                modal.style.display = 'block';
                // 聚焦到分组名称输入框
                setTimeout(() => {
                    if (groupNameInput) groupNameInput.focus();
                }, 100);
            } else {
                console.error('创建分组模态框未找到');
                alert('创建分组功能暂时不可用，请刷新页面重试');
            }
        }

        // 显示编辑分组模态框
        function showEditGroupModal() {
            if (!checkPermission('manage_groups')) {
                showPermissionDenied('编辑分组');
                return;
            }

            console.log('显示编辑分组模态框');

            // 刷新分组选项
            refreshEditGroupOptions();

            // 重置表单
            const editGroupForm = document.getElementById('edit-group-form');
            if (editGroupForm) {
                editGroupForm.reset();
            }

            // 显示模态框
            const modal = document.getElementById('edit-group-modal');
            if (modal) {
                modal.style.display = 'block';
            } else {
                console.error('编辑分组模态框未找到');
                alert('编辑分组功能暂时不可用，请刷新页面重试');
            }
        }

        // 刷新编辑分组的选项
        function refreshEditGroupOptions() {
            const select = document.getElementById('edit_group_select');
            if (!select) {
                console.error('edit_group_select元素未找到');
                return;
            }

            // 清空现有选项
            select.innerHTML = '<option value="">请选择分组</option>';

            console.log('开始获取分组列表...');

            // 获取所有分组
            fetch('monitor.php?action=get_groups')
                .then(response => {
                    console.log('分组列表响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('分组列表数据:', data);
                    if (data.success && data.groups) {
                        console.log('找到', data.groups.length, '个分组');
                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.group_name;
                            option.textContent = group.group_name + (group.description ? ` (${group.description})` : '');
                            select.appendChild(option);
                        });
                    } else {
                        console.error('获取分组列表失败:', data.error || '未知错误');
                        alert('获取分组列表失败：' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('获取分组列表失败:', error);
                    alert('获取分组列表失败，请检查网络连接');
                });
        }

        // 加载分组信息到编辑表单
        function loadGroupInfo() {
            const select = document.getElementById('edit_group_select');
            const groupName = select.value;

            console.log('loadGroupInfo被调用，选择的分组:', groupName);

            if (!groupName) {
                console.log('没有选择分组，清空表单');
                // 清空表单
                document.getElementById('edit_group_name').value = '';
                document.getElementById('edit_group_description').value = '';
                document.getElementById('edit_original_group_name').value = '';
                return;
            }

            const url = `monitor.php?action=get_group_info&group_name=${encodeURIComponent(groupName)}`;
            console.log('请求URL:', url);

            // 获取分组详细信息
            fetch(url)
                .then(response => {
                    console.log('分组信息响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP错误! 状态: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('分组信息数据:', data);
                    if (data.success && data.group) {
                        console.log('成功获取分组信息，填充表单');
                        document.getElementById('edit_group_name').value = data.group.group_name;
                        document.getElementById('edit_group_description').value = data.group.description || '';
                        document.getElementById('edit_original_group_name').value = data.group.group_name;
                    } else {
                        console.error('获取分组信息失败:', data.error);
                        alert('获取分组信息失败：' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('获取分组信息失败:', error);
                    alert('获取分组信息失败：' + error.message);
                });
        }

        // 解除拉黑设备
        function unblacklistDevice(deviceId) {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('解除拉黑');
                return;
            }

            if (!confirm(`确定要解除设备 ${deviceId} 的拉黑状态吗？`)) {
                return;
            }

            // 创建表单并提交
            const form = document.createElement('form');
            form.method = 'post';
            form.action = 'monitor.php';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'unblacklist';

            const deviceInput = document.createElement('input');
            deviceInput.type = 'hidden';
            deviceInput.name = 'device_id';
            deviceInput.value = deviceId;

            form.appendChild(actionInput);
            form.appendChild(deviceInput);
            document.body.appendChild(form);
            form.submit();
        }

        // 查看设备详情
        function viewDeviceDetails(deviceId) {
            // 创建设备详情模态框
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px;">
                    <span class="close-btn" onclick="this.closest('.modal').remove()">&times;</span>
                    <h2>设备详情</h2>
                    <p>设备ID: <strong>${deviceId}</strong></p>
                    <div id="device-details-content">
                        <p>加载中...</p>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 获取设备详细信息
            fetch(`monitor.php?action=get_device_details&device_id=${encodeURIComponent(deviceId)}`)
                .then(response => response.json())
                .then(data => {
                    const content = document.getElementById('device-details-content');
                    if (data.success && data.device) {
                        const device = data.device;
                        content.innerHTML = `
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                                <div><strong>设备型号:</strong> ${device.device_model || '未知'}</div>
                                <div><strong>Android版本:</strong> ${device.android_version || '未知'}</div>
                                <div><strong>IP地址:</strong> ${device.ip_address || '未知'}</div>
                                <div><strong>分组:</strong> ${device.device_group || '无分组'}</div>
                                <div><strong>首次注册:</strong> ${device.first_seen || '未知'}</div>
                                <div><strong>最后活跃:</strong> ${device.last_active || '未知'}</div>
                                <div><strong>访问次数:</strong> ${device.access_count || 0}</div>
                                <div><strong>状态:</strong> ${device.is_blacklisted ? '已拉黑' : '正常'}</div>
                            </div>
                            ${device.is_blacklisted ? `
                                <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0;">
                                    <strong>拉黑信息:</strong><br>
                                    原因: ${device.blacklist_reason || '未知'}<br>
                                    操作人: ${device.blacklisted_by || '未知'}<br>
                                    时间: ${device.blacklist_time || '未知'}
                                </div>
                            ` : ''}
                        `;
                    } else {
                        content.innerHTML = '<p style="color: #e74c3c;">获取设备详情失败: ' + (data.error || '未知错误') + '</p>';
                    }
                })
                .catch(error => {
                    console.error('获取设备详情失败:', error);
                    document.getElementById('device-details-content').innerHTML = '<p style="color: #e74c3c;">获取设备详情失败，请重试</p>';
                });
        }

        // 显示删除分组模态框
        function showDeleteGroupModal() {
            if (!checkPermission('manage_groups')) {
                showPermissionDenied('删除分组');
                return;
            }

            console.log('显示删除分组模态框');

            // 刷新分组选项
            refreshDeleteGroupOptions();

            // 重置表单
            const deleteGroupSelect = document.getElementById('delete_group_name');
            const confirmCheckbox = document.getElementById('confirm-delete-group');

            if (deleteGroupSelect) deleteGroupSelect.value = '';
            if (confirmCheckbox) confirmCheckbox.checked = false;

            // 显示模态框
            const modal = document.getElementById('delete-group-modal');
            if (modal) {
                modal.style.display = 'block';
            } else {
                console.error('删除分组模态框未找到');
                alert('删除分组功能暂时不可用，请刷新页面重试');
            }
        }

        // 刷新删除分组选择框的选项
function refreshDeleteGroupOptions() {
    fetch('monitor.php?action=get_groups')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const deleteSelect = document.getElementById('delete_group_name');

                if (deleteSelect) {
                    // 清空现有选项（保留默认选项）
                    deleteSelect.innerHTML = '<option value="">请选择分组</option>';

                    // 添加分组选项
                    if (data.groups && data.groups.length > 0) {
                        data.groups.forEach(group => {
                            const option = new Option(
                                `${group.group_name}`,
                                group.group_name
                            );
                            deleteSelect.add(option);
                        });
                    } else {
                        const option = new Option('暂无分组可删除', '');
                        option.disabled = true;
                        deleteSelect.add(option);
                    }
                }
            } else {
                console.error('获取分组列表失败:', data.error);
                const deleteSelect = document.getElementById('delete_group_name');
                if (deleteSelect) {
                    deleteSelect.innerHTML = '<option value="">获取分组列表失败</option>';
                }
            }
        })
        .catch(error => {
            console.error('获取分组列表失败:', error);
            const deleteSelect = document.getElementById('delete_group_name');
            if (deleteSelect) {
                deleteSelect.innerHTML = '<option value="">获取分组列表失败</option>';
            }
        });
}

// 获取最新的分组列表
function refreshGroupOptions() {
    fetch('monitor.php?action=get_groups')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新批量分组选择框
                const batchSelect = document.getElementById('batch_group_name');
                const singleSelect = document.getElementById('single_group_name');

                // 清空现有选项（保留"无分组"选项）
                if (batchSelect) {
                    batchSelect.innerHTML = '<option value="">无分组</option>';
                }
                if (singleSelect) {
                    singleSelect.innerHTML = '<option value="">无分组</option>';
                }

                // 添加新的分组选项
                if (data.groups && data.groups.length > 0) {
                    data.groups.forEach(group => {
                        if (batchSelect) {
                            const batchOption = new Option(group.group_name, group.group_name);
                            batchSelect.add(batchOption);
                        }
                        if (singleSelect) {
                            const singleOption = new Option(group.group_name, group.group_name);
                            singleSelect.add(singleOption);
                        }
                    });
                }
            }
        })
        .catch(error => {
            console.error('获取分组列表失败:', error);
        });
}
        // 心跳监控相关函数
        function refreshHeartbeat() {
            window.location.reload();
        }
        
        // 检查过期设备
        function checkExpiredDevices() {
            if (confirm('确定要检查并处理过期设备吗？过期设备将被自动拉黑。')) {
                fetch('monitor.php?action=check_expired_devices')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        if (data.count > 0) {
                            location.reload();
                        }
                    } else {
                        alert('错误: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('请求失败: ' + error.message);
                });
            }
        }

        // 获取选中的心跳设备ID
        function getSelectedHeartbeatDeviceIds() {
            const checkboxes = document.querySelectorAll('.heartbeat-checkbox:checked');
            const selectedIds = [];
            checkboxes.forEach(checkbox => {
                selectedIds.push(checkbox.value);
            });
            return selectedIds;
        }

        // 全选/取消全选心跳设备
        function toggleHeartbeatSelectAll(selectAllCheckbox) {
            const checkboxes = document.querySelectorAll('.heartbeat-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            // 同步其他全选框状态
            const otherSelectAll = selectAllCheckbox.id === 'select-all-heartbeat' ?
                document.getElementById('select-all-heartbeat-bottom') :
                document.getElementById('select-all-heartbeat');
            if (otherSelectAll) {
                otherSelectAll.checked = selectAllCheckbox.checked;
            }
        }

        // 批量删除心跳设备
        function batchDeleteHeartbeatDevices() {
            if (!checkPermission('manage_devices')) {
                showPermissionDenied('批量删除心跳设备');
                return;
            }

            const selectedIds = getSelectedHeartbeatDeviceIds();
            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            document.getElementById('selected-count-heartbeat-delete').textContent = selectedIds.length;
            document.getElementById('heartbeat-batch-delete-device-ids').value = selectedIds.join(',');
            document.getElementById('heartbeat-batch-delete-modal').style.display = 'block';
        }

        // 批量拉黑心跳设备
        function batchBlacklistHeartbeatDevices() {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('批量拉黑心跳设备');
                return;
            }

            const selectedIds = getSelectedHeartbeatDeviceIds();
            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            document.getElementById('selected-count-heartbeat-blacklist').textContent = selectedIds.length;
            document.getElementById('heartbeat-batch-blacklist-device-ids').value = selectedIds.join(',');
            document.getElementById('heartbeat-batch-blacklist-modal').style.display = 'block';
        }

        // 批量解除拉黑心跳设备
        function batchRemoveBlacklistHeartbeatDevices() {
            if (!checkPermission('manage_blacklist')) {
                showPermissionDenied('批量解除拉黑心跳设备');
                return;
            }

            const selectedIds = getSelectedHeartbeatDeviceIds();
            if (selectedIds.length === 0) {
                alert('请选择至少一个设备');
                return;
            }

            // 过滤出在黑名单中的设备
            const blacklistedDevices = selectedIds.filter(deviceId => {
                const row = document.querySelector(`tr[data-device-id="${deviceId}"]`);
                if (row) {
                    // 查找状态标签，检查是否为已拉黑状态
                    const statusTag = row.querySelector('.status-tag.blacklisted');
                    if (statusTag) {
                        return true;
                    }
                    // 备用方案：检查文本内容
                    const statusCell = row.querySelector('.status-tag');
                    return statusCell && statusCell.textContent.includes('已拉黑');
                }
                return false;
            });

            if (blacklistedDevices.length === 0) {
                alert('所选设备中没有被拉黑的设备');
                return;
            }

            if (confirm(`确定要解除 ${blacklistedDevices.length} 个心跳设备的拉黑状态吗？`)) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'batch_remove_blacklist_heartbeat_devices';
                form.appendChild(actionInput);

                const deviceIdsInput = document.createElement('input');
                deviceIdsInput.type = 'hidden';
                deviceIdsInput.name = 'device_ids';
                deviceIdsInput.value = blacklistedDevices.join(',');
                form.appendChild(deviceIdsInput);

                document.body.appendChild(form);
                form.submit();
            }
        }

        function viewHeartbeatDetails(deviceId) {
            // 创建心跳详情模态框
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <span class="close-btn" onclick="this.closest('.modal').remove()">&times;</span>
                    <h2>设备心跳详情</h2>
                    <p>设备ID: <strong>${deviceId}</strong></p>
                    <div id="heartbeat-details-content">
                        <p>加载中...</p>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // 获取心跳详情数据
            fetch('monitor.php?action=get_heartbeat_details&device_id=' + encodeURIComponent(deviceId))
                .then(response => response.json())
                .then(data => {
                    const content = document.getElementById('heartbeat-details-content');
                    if (data.success && data.data) {
                        const heartbeat = data.data;
                        const lastHeartbeat = new Date(heartbeat.last_heartbeat);
                        const now = new Date();
                        const minutesAgo = Math.floor((now - lastHeartbeat) / (1000 * 60));

                        let timeText = '';
                        if (minutesAgo === 0) {
                            timeText = '刚刚';
                        } else if (minutesAgo < 60) {
                            timeText = minutesAgo + '分钟前';
                        } else {
                            timeText = lastHeartbeat.toLocaleString();
                        }

                        content.innerHTML = `
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 20px;">
                                <div><strong>设备型号:</strong> ${heartbeat.device_model || '未知'}</div>
                                <div><strong>Android版本:</strong> ${heartbeat.android_version || '未知'}</div>
                                <div><strong>脚本版本:</strong> ${heartbeat.script_version || '未知'}</div>
                                <div><strong>运行状态:</strong>
                                    <span class="device-status ${heartbeat.running_status === 'running' ? 'status-running' : 'status-offline'}">
                                        ${heartbeat.running_status === 'running' ? '运行中' : '离线'}
                                    </span>
                                </div>
                                <div><strong>最后心跳:</strong> ${timeText}</div>
                                <div><strong>电池电量:</strong> ${heartbeat.battery_level > 0 ? heartbeat.battery_level + '%' : '未知'}</div>
                                <div><strong>内存使用:</strong> ${heartbeat.memory_usage > 0 ? heartbeat.memory_usage + ' MB' : '未知'}</div>
                                <div><strong>创建时间:</strong> ${new Date(heartbeat.created_at).toLocaleString()}</div>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <button class="button button-primary" onclick="refreshHeartbeat()">刷新数据</button>
                            </div>
                        `;
                    } else {
                        content.innerHTML = '<p style="color: #e74c3c;">获取心跳详情失败: ' + (data.error || '未知错误') + '</p>';
                    }
                })
                .catch(error => {
                    console.error('获取心跳详情失败:', error);
                    document.getElementById('heartbeat-details-content').innerHTML = '<p style="color: #e74c3c;">网络错误，请稍后重试</p>';
                });
        }

        // 自动刷新心跳监控页面（每30秒）
        if (window.location.search.includes('view=heartbeat')) {
            setInterval(function() {
                // 只有在没有打开模态框的情况下才自动刷新
                if (!document.querySelector('.modal[style*="display: block"]')) {
                    refreshHeartbeat();
                }
            }, 30000); // 30秒刷新一次
        }

        // 桌面查看相关函数
        let desktopRefreshInterval = null;
        let currentDesktopDeviceId = null;

        function viewDesktop(deviceId) {
            if (!checkPermission('view_screenshots')) {
                showPermissionDenied('查看桌面');
                return;
            }

            currentDesktopDeviceId = deviceId;
            document.getElementById('desktop-device-title').textContent = `设备桌面 - ${deviceId}`;
            document.getElementById('desktop-view-modal').style.display = 'block';

            // 重置状态
            showDesktopLoading();
            document.querySelector('#desktop-loading p').textContent = '正在获取最新截图...';

            // 立即获取最近的截图显示（不等待新截图）
            fetchDesktopScreenshot('latest'); // 使用latest模式立即显示已有截图

            // 然后发送截图请求给设备（让设备开始上传新截图）
            setTimeout(() => {
                requestScreenshot(deviceId);

                // 开始定时刷新，使用refresh模式等待新截图
                desktopRefreshInterval = setInterval(() => {
                    fetchDesktopScreenshot('refresh');
                }, 3000); // 每3秒检查一次新截图
            }, 500); // 0.5秒后开始发送请求
        }

        function closeDesktopView() {
            // 不再发送停止截图请求，让设备继续自动上传截图
            console.log('关闭桌面查看，设备将继续自动上传截图');

            // 直接关闭窗口
            document.getElementById('desktop-view-modal').style.display = 'none';

            // 停止定时刷新
            if (desktopRefreshInterval) {
                clearInterval(desktopRefreshInterval);
                desktopRefreshInterval = null;
            }

            currentDesktopDeviceId = null;
        }

        function refreshDesktop() {
            if (currentDesktopDeviceId) {
                // 添加刷新按钮动画
                const refreshBtn = document.getElementById('desktop-refresh-btn');
                refreshBtn.style.transform = 'rotate(360deg)';
                refreshBtn.style.transition = 'transform 0.5s ease';

                setTimeout(() => {
                    refreshBtn.style.transform = 'rotate(0deg)';
                }, 500);

                showDesktopLoading();
                document.querySelector('#desktop-loading p').textContent = '正在等待最新截图...';

                // 刷新模式：等待最新的截图
                fetchDesktopScreenshot('refresh');
            }
        }

        function showDesktopLoading() {
            document.getElementById('desktop-loading').style.display = 'block';
            document.getElementById('desktop-error').style.display = 'none';
            document.getElementById('desktop-image-container').style.display = 'none';
            document.getElementById('desktop-status').textContent = '正在获取...';
            document.getElementById('desktop-status').style.background = '#ffeaa7';
        }

        function showDesktopError(message) {
            document.getElementById('desktop-loading').style.display = 'none';
            document.getElementById('desktop-error').style.display = 'block';
            document.getElementById('desktop-image-container').style.display = 'none';
            document.getElementById('desktop-status').textContent = '连接失败';
            document.getElementById('desktop-status').style.background = '#fab1a0';

            if (message) {
                document.querySelector('#desktop-error p').textContent = message;
            }
        }

        function showDesktopWaiting(message) {
            document.getElementById('desktop-loading').style.display = 'block';
            document.getElementById('desktop-error').style.display = 'none';
            document.getElementById('desktop-image-container').style.display = 'none';
            document.getElementById('desktop-status').textContent = '等待新截图';
            document.getElementById('desktop-status').style.background = '#74b9ff';
            document.getElementById('desktop-status').style.color = 'white';

            if (message) {
                document.querySelector('#desktop-loading p').textContent = message;
            }
        }

        function showDesktopImage(data) {
            document.getElementById('desktop-loading').style.display = 'none';
            document.getElementById('desktop-error').style.display = 'none';
            document.getElementById('desktop-image-container').style.display = 'block';

            // 更新图片
            const img = document.getElementById('desktop-image');
            const container = document.getElementById('desktop-image-container');
            img.src = 'data:image/png;base64,' + data.screenshot_data;

            // 重置图片状态
            img.classList.remove('fullsize');
            container.classList.remove('historical-screenshot', 'offline-screenshot');
            img.style.cursor = 'zoom-in';

            // 根据截图类型添加样式
            if (data.is_historical) {
                container.classList.add('historical-screenshot');
            } else if (data.no_active_request) {
                container.classList.add('offline-screenshot');
            }

            // 当图片加载完成后获取尺寸信息
            img.onload = function() {
                const naturalWidth = this.naturalWidth;
                const naturalHeight = this.naturalHeight;

                // 等待DOM更新后获取显示尺寸
                setTimeout(() => {
                    const displayWidth = this.clientWidth;
                    const displayHeight = this.clientHeight;

                    // 更新图片尺寸信息
                    let sizeText = `原始: ${naturalWidth}×${naturalHeight} | 显示: ${displayWidth}×${displayHeight}`;
                    if (data.is_historical) {
                        sizeText += ' | 历史截图';
                    } else if (data.no_active_request) {
                        sizeText += ' | 设备离线';
                    }
                    document.getElementById('desktop-image-size').textContent = sizeText;
                }, 100);

                // 添加点击放大功能
                this.onclick = function() {
                    if (this.classList.contains('fullsize')) {
                        // 恢复适配大小
                        this.classList.remove('fullsize');
                        this.style.cursor = 'zoom-in';
                        this.title = '点击查看原始大小';

                        // 滚动到图片顶部
                        this.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    } else {
                        // 显示原始大小
                        this.classList.add('fullsize');
                        this.style.cursor = 'zoom-out';
                        this.title = '点击恢复适配大小';
                    }
                };

                this.title = '点击查看原始大小';
            };

            // 更新时间和文件大小
            document.getElementById('desktop-update-time').textContent = new Date(data.upload_time).toLocaleString('zh-CN');
            document.getElementById('desktop-file-size').textContent = formatFileSize(data.file_size);
        }

        function fetchDesktopScreenshot(mode = 'latest') {
            if (!currentDesktopDeviceId) return;

            let url = `monitor.php?action=get_screenshot&device_id=${encodeURIComponent(currentDesktopDeviceId)}&mode=${mode}`;

            console.log(`获取截图: 设备=${currentDesktopDeviceId}, 模式=${mode}`);

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('截图响应:', data);

                    if (data.success) {
                        showDesktopImage(data);

                        // 根据不同模式显示不同状态
                        const status = document.getElementById('desktop-status');

                        if (data.mode === 'latest') {
                            // 查看桌面模式：显示历史截图状态
                            if (data.is_historical) {
                                status.textContent = `历史截图 (${formatTimeAgo(data.age_seconds)})`;
                                status.style.background = '#a29bfe';
                                status.style.color = 'white';
                            } else if (data.age_seconds > 10) {
                                status.textContent = `${data.age_seconds}秒前`;
                                status.style.background = '#fdcb6e';
                                status.style.color = 'black';
                            } else {
                                status.textContent = '实时连接';
                                status.style.background = '#00b894';
                                status.style.color = 'white';
                            }
                        } else if (data.mode === 'refresh') {
                            // 刷新模式：显示刷新状态
                            if (data.no_active_request) {
                                status.textContent = `设备离线 (${formatTimeAgo(data.age_seconds)})`;
                                status.style.background = '#636e72';
                                status.style.color = 'white';
                            } else if (data.age_seconds <= 10) {
                                status.textContent = '已刷新';
                                status.style.background = '#00b894';
                                status.style.color = 'white';
                            } else {
                                status.textContent = `已刷新 (${data.age_seconds}秒前)`;
                                status.style.background = '#fdcb6e';
                                status.style.color = 'black';
                            }
                        }
                    } else if (data.waiting) {
                        // 显示等待状态
                        showDesktopWaiting(data.error || '等待设备上传新截图...');
                    } else {
                        showDesktopError(data.error || '获取截图失败');
                    }
                })
                .catch(error => {
                    console.error('获取截图失败:', error);
                    showDesktopError('网络连接失败');
                });
        }

        // 格式化时间显示
        function formatTimeAgo(seconds) {
            if (seconds < 60) {
                return `${seconds}秒前`;
            } else if (seconds < 3600) {
                return `${Math.floor(seconds / 60)}分钟前`;
            } else if (seconds < 86400) {
                return `${Math.floor(seconds / 3600)}小时前`;
            } else {
                return `${Math.floor(seconds / 86400)}天前`;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 发送截图请求给设备
        function requestScreenshot(deviceId) {
            console.log('发送截图请求给设备:', deviceId);

            const formData = new FormData();
            formData.append('action', 'request_screenshot');
            formData.append('device_id', deviceId);

            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('截图请求发送成功:', data.message);
                    document.getElementById('desktop-status').textContent = '已请求截图...';
                    document.getElementById('desktop-status').style.background = '#74b9ff';
                    document.getElementById('desktop-status').style.color = 'white';
                } else {
                    console.error('截图请求发送失败:', data.error);
                }
            })
            .catch(error => {
                console.error('发送截图请求失败:', error);
            });
        }

        // 发送停止截图请求给设备
        function stopScreenshotRequest(deviceId) {
            console.log('发送停止截图请求给设备:', deviceId);

            const formData = new FormData();
            formData.append('action', 'stop_screenshot');
            formData.append('device_id', deviceId);

            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('停止截图请求发送成功:', data.message);
                } else {
                    console.error('停止截图请求发送失败:', data.error);
                }
            })
            .catch(error => {
                console.error('发送停止截图请求失败:', error);
            });
        }

        // 点击模态框外部关闭桌面查看
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('desktop-view-modal');
            if (event.target === modal) {
                closeDesktopView();
            }
        });

        // 监听ESC键关闭桌面查看
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('desktop-view-modal');
                if (modal.style.display === 'block') {
                    closeDesktopView();
                }
            }
        });

        // 历史截图相关变量
        let currentHistoryDeviceId = null;
        let currentHistoryPage = 1;
        let totalHistoryPages = 1;
        const historyPageSize = 12; // 每页显示12张截图

        // 显示历史截图
        function showScreenshotHistory() {
            if (!currentDesktopDeviceId) return;

            currentHistoryDeviceId = currentDesktopDeviceId;
            currentHistoryPage = 1;

            document.getElementById('history-device-title').textContent = `历史截图 - ${currentHistoryDeviceId}`;
            document.getElementById('screenshot-history-modal').style.display = 'block';

            loadScreenshotHistory();
        }

        // 关闭历史截图
        function closeScreenshotHistory() {
            document.getElementById('screenshot-history-modal').style.display = 'none';
            currentHistoryDeviceId = null;
        }

        // 刷新历史截图
        function refreshScreenshotHistory() {
            if (currentHistoryDeviceId) {
                currentHistoryPage = 1;
                loadScreenshotHistory();
            }
        }

        // 加载历史截图列表
        function loadScreenshotHistory() {
            if (!currentHistoryDeviceId) return;

            console.log('开始加载历史截图:', currentHistoryDeviceId, '页码:', currentHistoryPage);

            // 显示加载状态
            document.getElementById('history-loading').style.display = 'block';
            document.getElementById('history-error').style.display = 'none';
            document.getElementById('history-list').style.display = 'none';

            const offset = (currentHistoryPage - 1) * historyPageSize;
            const url = `monitor.php?action=get_screenshot_history&device_id=${encodeURIComponent(currentHistoryDeviceId)}&limit=${historyPageSize}&offset=${offset}`;

            console.log('请求URL:', url);

            fetch(url)
                .then(response => {
                    console.log('响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('历史截图数据:', data);
                    if (data.success) {
                        if (data.screenshots && data.screenshots.length > 0) {
                            displayScreenshotHistory(data);
                        } else {
                            showHistoryError('该设备暂无历史截图');
                        }
                    } else {
                        showHistoryError(data.error || '加载历史截图失败');
                    }
                })
                .catch(error => {
                    console.error('加载历史截图失败:', error);
                    showHistoryError('网络连接失败: ' + error.message);
                });
        }

        // 显示历史截图列表
        function displayScreenshotHistory(data) {
            document.getElementById('history-loading').style.display = 'none';
            document.getElementById('history-error').style.display = 'none';
            document.getElementById('history-list').style.display = 'block';

            // 更新信息
            document.getElementById('history-info').textContent = `共 ${data.total_count} 张截图`;

            // 更新分页信息
            totalHistoryPages = data.total_pages;
            currentHistoryPage = data.current_page;
            document.getElementById('page-info').textContent = `第 ${currentHistoryPage} 页，共 ${totalHistoryPages} 页`;

            // 更新分页按钮状态
            document.getElementById('prev-page-btn').disabled = currentHistoryPage <= 1;
            document.getElementById('next-page-btn').disabled = currentHistoryPage >= totalHistoryPages;

            // 生成截图网格
            const grid = document.getElementById('screenshot-grid');
            grid.innerHTML = '';

            data.screenshots.forEach(screenshot => {
                const item = createScreenshotItem(screenshot);
                grid.appendChild(item);
            });
        }

        // 创建截图项元素
        function createScreenshotItem(screenshot) {
            const item = document.createElement('div');
            item.className = 'screenshot-item';
            item.onclick = () => showScreenshotDetail(screenshot.id);

            // 生成缩略图（使用第一个字符作为占位符）
            const thumbnail = document.createElement('div');
            thumbnail.className = 'screenshot-thumbnail';
            thumbnail.style.display = 'flex';
            thumbnail.style.alignItems = 'center';
            thumbnail.style.justifyContent = 'center';
            thumbnail.style.fontSize = '24px';
            thumbnail.style.color = '#6c757d';
            thumbnail.textContent = '[图片]';

            // 截图信息
            const info = document.createElement('div');
            info.className = 'screenshot-info';

            const time = document.createElement('div');
            time.className = 'screenshot-time';
            time.textContent = screenshot.formatted_time;

            const size = document.createElement('div');
            size.className = 'screenshot-size';
            size.textContent = formatFileSize(screenshot.file_size);

            const age = document.createElement('div');
            age.className = 'screenshot-age';
            age.textContent = formatTimeAgo(screenshot.age_seconds);

            // 根据年龄设置颜色
            if (screenshot.age_seconds > 86400) { // 超过1天
                age.classList.add('very-old');
            } else if (screenshot.age_seconds > 3600) { // 超过1小时
                age.classList.add('old');
            }

            info.appendChild(time);
            info.appendChild(size);
            info.appendChild(age);

            item.appendChild(thumbnail);
            item.appendChild(info);

            return item;
        }

        // 显示历史截图错误
        function showHistoryError(message) {
            document.getElementById('history-loading').style.display = 'none';
            document.getElementById('history-list').style.display = 'none';
            document.getElementById('history-error').style.display = 'block';

            if (message) {
                document.querySelector('#history-error p').textContent = message;
            }
        }

        // 上一页
        function loadPreviousPage() {
            if (currentHistoryPage > 1) {
                currentHistoryPage--;
                loadScreenshotHistory();
            }
        }

        // 下一页
        function loadNextPage() {
            if (currentHistoryPage < totalHistoryPages) {
                currentHistoryPage++;
                loadScreenshotHistory();
            }
        }

        // 显示截图详情
        function showScreenshotDetail(screenshotId, retryCount = 0) {
            console.log('显示截图详情:', screenshotId, '重试次数:', retryCount);

            document.getElementById('screenshot-detail-modal').style.display = 'block';
            document.getElementById('detail-loading').style.display = 'block';
            document.getElementById('detail-image-container').style.display = 'none';

            // 更新加载提示
            const loadingText = document.querySelector('#detail-loading p');
            if (retryCount > 0) {
                loadingText.textContent = `正在加载截图... (重试 ${retryCount}/3)`;
            } else {
                loadingText.textContent = '正在加载截图...';
            }

            const url = `monitor.php?action=get_screenshot_by_id&id=${screenshotId}`;
            console.log('请求截图详情URL:', url);

            // 设置超时时间为30秒
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000);

            fetch(url, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            })
                .then(response => {
                    clearTimeout(timeoutId);
                    console.log('截图详情响应状态:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('截图详情数据:', {
                        success: data.success,
                        hasData: !!data.screenshot_data,
                        dataSize: data.data_size,
                        error: data.error
                    });

                    if (data.success) {
                        if (data.screenshot_data) {
                            displayScreenshotDetail(data);
                        } else {
                            throw new Error('截图数据为空');
                        }
                    } else {
                        throw new Error(data.error || '未知错误');
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('加载截图详情失败:', error);

                    // 如果是网络错误且重试次数少于3次，则重试
                    if (retryCount < 3 && (error.name === 'AbortError' || error.message.includes('网络') || error.message.includes('HTTP'))) {
                        console.log('准备重试加载截图详情...');
                        setTimeout(() => {
                            showScreenshotDetail(screenshotId, retryCount + 1);
                        }, 2000); // 2秒后重试
                        return;
                    }

                    // 显示错误信息
                    let errorMessage = '加载截图失败: ';
                    if (error.name === 'AbortError') {
                        errorMessage += '请求超时，请检查网络连接';
                    } else {
                        errorMessage += error.message;
                    }

                    alert(errorMessage);
                    closeScreenshotDetail();
                });
        }

        // 显示截图详情
        function displayScreenshotDetail(data) {
            console.log('开始显示截图详情');

            document.getElementById('detail-loading').style.display = 'none';
            document.getElementById('detail-image-container').style.display = 'block';

            // 更新标题和时间
            document.getElementById('detail-title').textContent = `截图详情 - ${data.device_id}`;
            document.getElementById('detail-time').textContent = formatTimeAgo(data.age_seconds);

            // 更新图片
            const img = document.getElementById('detail-image');
            const imageData = 'data:image/png;base64,' + data.screenshot_data;

            console.log('图片数据长度:', data.screenshot_data.length);

            // 重置图片状态
            img.classList.remove('fullsize');
            img.style.cursor = 'zoom-in';

            // 设置图片加载超时
            let imageLoadTimeout;
            let imageLoaded = false;

            // 图片加载成功处理
            img.onload = function() {
                if (imageLoaded) return; // 防止重复处理
                imageLoaded = true;

                clearTimeout(imageLoadTimeout);
                console.log('图片加载成功');

                const naturalWidth = this.naturalWidth;
                const naturalHeight = this.naturalHeight;

                console.log('图片尺寸:', naturalWidth, 'x', naturalHeight);

                setTimeout(() => {
                    const displayWidth = this.clientWidth;
                    const displayHeight = this.clientHeight;

                    document.getElementById('detail-image-size').textContent =
                        `${naturalWidth}×${naturalHeight} (显示: ${displayWidth}×${displayHeight})`;
                }, 100);

                // 添加点击放大功能
                this.onclick = function() {
                    if (this.classList.contains('fullsize')) {
                        this.classList.remove('fullsize');
                        this.style.cursor = 'zoom-in';
                        this.title = '点击查看原始大小';
                        this.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    } else {
                        this.classList.add('fullsize');
                        this.style.cursor = 'zoom-out';
                        this.title = '点击恢复适配大小';
                    }
                };

                this.title = '点击查看原始大小';
            };

            // 图片加载失败处理
            img.onerror = function() {
                if (imageLoaded) return;
                imageLoaded = true;

                clearTimeout(imageLoadTimeout);
                console.error('图片加载失败');
                alert('图片加载失败，可能是数据损坏');
                closeScreenshotDetail();
            };

            // 设置15秒超时
            imageLoadTimeout = setTimeout(() => {
                if (!imageLoaded) {
                    console.error('图片加载超时');
                    alert('图片加载超时，请重试');
                    closeScreenshotDetail();
                }
            }, 15000);

            // 开始加载图片
            console.log('开始加载图片...');
            img.src = imageData;

            // 更新详情信息
            document.getElementById('detail-upload-time').textContent = new Date(data.upload_time).toLocaleString('zh-CN');
            document.getElementById('detail-file-size').textContent = formatFileSize(data.file_size);
        }

        // 关闭截图详情
        function closeScreenshotDetail() {
            document.getElementById('screenshot-detail-modal').style.display = 'none';
        }

        // 点击模态框外部关闭历史截图
        window.addEventListener('click', function(event) {
            const historyModal = document.getElementById('screenshot-history-modal');
            const detailModal = document.getElementById('screenshot-detail-modal');

            if (event.target === historyModal) {
                closeScreenshotHistory();
            }

            if (event.target === detailModal) {
                closeScreenshotDetail();
            }
        });

        // 监听ESC键关闭历史截图
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const historyModal = document.getElementById('screenshot-history-modal');
                const detailModal = document.getElementById('screenshot-detail-modal');

                if (detailModal.style.display === 'block') {
                    closeScreenshotDetail();
                } else if (historyModal.style.display === 'block') {
                    closeScreenshotHistory();
                }
            }
        });

        // 调试相关函数
        let debugInfoVisible = false;

        function toggleDebugInfo() {
            debugInfoVisible = !debugInfoVisible;
            const debugPanel = document.getElementById('debug-info');
            debugPanel.style.display = debugInfoVisible ? 'block' : 'none';

            if (debugInfoVisible) {
                updateDebugInfo();
            }
        }

        function updateDebugInfo() {
            if (!debugInfoVisible) return;

            const debugContent = document.getElementById('debug-content');
            const now = new Date().toLocaleString('zh-CN');

            let info = `<div>当前时间: ${now}</div>`;
            info += `<div>当前设备ID: ${currentDesktopDeviceId || '未选择'}</div>`;
            info += `<div>历史截图设备ID: ${currentHistoryDeviceId || '未选择'}</div>`;
            info += `<div>历史截图页码: ${currentHistoryPage}/${totalHistoryPages}</div>`;

            // 检查数据库连接
            if (currentDesktopDeviceId) {
                const testUrl = `monitor.php?action=get_screenshot_history&device_id=${encodeURIComponent(currentDesktopDeviceId)}&limit=1&offset=0`;
                fetch(testUrl)
                    .then(response => response.json())
                    .then(data => {
                        info += `<div style="color: ${data.success ? 'green' : 'red'};">数据库连接: ${data.success ? '正常' : '异常'}</div>`;
                        if (data.success) {
                            info += `<div>总截图数: ${data.total_count}</div>`;
                        } else {
                            info += `<div>错误信息: ${data.error}</div>`;
                        }
                        debugContent.innerHTML = info;
                    })
                    .catch(error => {
                        info += `<div style="color: red;">网络连接: 异常 (${error.message})</div>`;
                        debugContent.innerHTML = info;
                    });
            } else {
                debugContent.innerHTML = info;
            }
        }

        // 每5秒更新一次调试信息
        setInterval(() => {
            if (debugInfoVisible) {
                updateDebugInfo();
            }
        }, 5000);

        // 添加测试历史截图功能的函数
        function testScreenshotHistory() {
            if (!currentDesktopDeviceId) {
                alert('请先选择一个设备');
                return;
            }

            console.log('测试历史截图功能...');

            // 测试获取历史截图列表
            const testUrl = `monitor.php?action=get_screenshot_history&device_id=${encodeURIComponent(currentDesktopDeviceId)}&limit=5&offset=0`;
            console.log('测试URL:', testUrl);

            fetch(testUrl)
                .then(response => {
                    console.log('测试响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('测试响应数据:', data);
                    if (data.success) {
                        alert(`测试成功！找到 ${data.total_count} 张历史截图`);
                        if (data.screenshots.length > 0) {
                            const firstId = data.screenshots[0].id;
                            console.log('测试第一张截图ID:', firstId);

                            // 测试获取单张截图
                            const detailUrl = `monitor.php?action=get_screenshot_by_id&id=${firstId}`;
                            return fetch(detailUrl);
                        }
                    } else {
                        alert('测试失败: ' + data.error);
                    }
                })
                .then(response => {
                    if (response) {
                        console.log('详情测试响应状态:', response.status);
                        return response.json();
                    }
                })
                .then(data => {
                    if (data) {
                        console.log('详情测试响应数据:', {
                            success: data.success,
                            hasData: !!data.screenshot_data,
                            dataSize: data.data_size
                        });
                        if (data.success) {
                            alert('详情测试也成功！');
                        } else {
                            alert('详情测试失败: ' + data.error);
                        }
                    }
                })
                .catch(error => {
                    console.error('测试失败:', error);
                    alert('测试失败: ' + error.message);
                });
        }

        // 在控制台添加测试函数
        window.testScreenshotHistory = testScreenshotHistory;
        console.log('调试函数已添加到 window.testScreenshotHistory()');
        // 心跳统计弹窗相关函数
        function showHeartbeatStatsModal() {
            document.getElementById('heartbeat-stats-modal').style.display = 'block';
            loadHeartbeatStats();
        }

        function closeHeartbeatStatsModal() {
            document.getElementById('heartbeat-stats-modal').style.display = 'none';
        }

        function refreshHeartbeatStats() {
            loadHeartbeatStats();
        }

        function loadHeartbeatStats() {
            console.log('开始加载心跳统计数据...');

            // 显示加载状态
            const content = document.getElementById('group-stats-content');
            content.innerHTML = '<div class="loading-message"><p>正在加载分组统计数据...</p></div>';

            fetch('monitor.php?action=get_group_heartbeat_stats')
                .then(response => response.json())
                .then(data => {
                    console.log('心跳统计数据:', data);
                    if (data.success) {
                        displayHeartbeatStats(data.group_stats, data.total_stats);
                    } else {
                        showHeartbeatStatsError(data.error || '获取心跳统计数据失败');
                    }
                })
                .catch(error => {
                    console.error('获取心跳统计数据失败:', error);
                    showHeartbeatStatsError('网络连接失败: ' + error.message);
                });
        }

        function displayHeartbeatStats(groupStats, totalStats) {
            // 更新总体统计
            document.getElementById('total-devices-count').textContent = totalStats.total_devices;
            document.getElementById('total-online-count').textContent = totalStats.online_devices;
            document.getElementById('total-warning-count').textContent = totalStats.warning_devices;
            document.getElementById('total-offline-count').textContent = totalStats.offline_devices;
            document.getElementById('total-running-count').textContent = totalStats.running_devices;

            // 显示分组统计
            const content = document.getElementById('group-stats-content');

            if (!groupStats || groupStats.length === 0) {
                content.innerHTML = '<div class="loading-message"><p>暂无分组统计数据</p></div>';
                return;
            }

            let html = '';
            groupStats.forEach(group => {
                html += createGroupStatCard(group);
            });

            content.innerHTML = html;
        }

        function createGroupStatCard(group) {
            return `
                <div class="group-stat-item">
                    <div class="group-stat-header">
                        <span class="group-name">${escapeHtml(group.group_name)}</span>
                        <span class="group-total">${group.total_devices}</span>
                    </div>
                    <div class="group-stat-details">
                        <div class="group-stat-item-detail">
                            <span class="group-stat-label">在线</span>
                            <span class="group-stat-value online">${group.online_devices}</span>
                        </div>
                        <div class="group-stat-item-detail">
                            <span class="group-stat-label">警告</span>
                            <span class="group-stat-value warning">${group.warning_devices}</span>
                        </div>
                        <div class="group-stat-item-detail">
                            <span class="group-stat-label">离线</span>
                            <span class="group-stat-value offline">${group.offline_devices}</span>
                        </div>
                        <div class="group-stat-item-detail">
                            <span class="group-stat-label">运行中</span>
                            <span class="group-stat-value running">${group.running_devices}</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px; font-size: 12px; color: #6c757d; text-align: center;">
                        最新心跳: ${group.formatted_time}
                    </div>
                </div>
            `;
        }

        function showHeartbeatStatsError(message) {
            const content = document.getElementById('group-stats-content');
            content.innerHTML = `<div class="loading-message" style="color: #e74c3c;"><p>[错误] ${escapeHtml(message)}</p></div>`;
        }

        // HTML转义函数（如果不存在）
        if (typeof escapeHtml === 'undefined') {
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // 页面加载完成后自动显示心跳统计弹窗（仅在直接访问monitor.php时显示）
        document.addEventListener('DOMContentLoaded', function() {
            // 检查当前页面URL和参数
            const urlParams = new URLSearchParams(window.location.search);
            const currentView = urlParams.get('view');
            const currentPath = window.location.pathname;

            // 只有在直接访问monitor.php且没有任何view参数时才显示弹窗
            // 这意味着用户是直接访问monitor.php，而不是通过其他操作跳转
            if (currentPath.endsWith('monitor.php') && !currentView && !urlParams.has('action') && !urlParams.has('group') && !urlParams.has('status') && !urlParams.has('page')) {
                // 延迟1秒显示弹窗，让页面完全加载
                setTimeout(function() {
                    showHeartbeatStatsModal();
                }, 1000);
            }
        });

        // 点击模态框外部关闭弹窗
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('heartbeat-stats-modal');
            if (event.target === modal) {
                closeHeartbeatStatsModal();
            }
        });



        // 显示后台日志记录统计
        function showBackendLogStats() {
            // 创建弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
                align-items: center; justify-content: center;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white; border-radius: 8px; padding: 20px;
                max-width: 90%; max-height: 90%; overflow-y: auto;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;

            content.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                    <h3 style="margin: 0; color: #2c3e50;">后台日志记录统计</h3>
                    <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
                </div>
                <div id="statsContent">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <div style="font-size: 18px; margin-bottom: 10px;">正在加载统计数据...</div>
                        <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                    </div>
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    <button onclick="refreshStats()" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; margin-right: 10px; cursor: pointer;">刷新数据</button>
                    <button onclick="this.closest('.modal').remove()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;

            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            // 加载统计数据
            loadBackendStats();
        }

        // 加载后台统计数据
        function loadBackendStats() {
            const statsContent = document.getElementById('statsContent');
            if (!statsContent) return;

            // 显示加载状态
            statsContent.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #666;">
                    <div style="font-size: 18px; margin-bottom: 10px;">正在加载统计数据...</div>
                    <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                </div>
            `;

            // 获取统计数据
            fetch('frequency_monitor_stats.php?minutes=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayBackendStats(data.data);
                    } else {
                        statsContent.innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #e74c3c;">
                                <div style="font-size: 18px; margin-bottom: 10px;">加载失败</div>
                                <div style="font-size: 14px;">${data.error}</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    statsContent.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #e74c3c;">
                            <div style="font-size: 18px; margin-bottom: 10px;">网络错误</div>
                            <div style="font-size: 14px;">无法连接到服务器</div>
                        </div>
                    `;
                });
        }

        // 显示后台统计数据
        function displayBackendStats(stats) {
            const statsContent = document.getElementById('statsContent');
            if (!statsContent) return;

            let html = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #2c3e50;">${stats.total_records}</div>
                        <div style="color: #6c757d; font-size: 14px;">总记录数</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #28a745;">${stats.unique_devices}</div>
                        <div style="color: #6c757d; font-size: 14px;">唯一设备数</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #dc3545;">${stats.duplicate_device_count}</div>
                        <div style="color: #6c757d; font-size: 14px;">重复设备数</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${stats.total_duplicate_records}</div>
                        <div style="color: #6c757d; font-size: 14px;">重复记录数</div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <h4 style="margin: 0 0 10px 0; color: #2c3e50;">频率监控状态</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div>监控阈值: <strong>${stats.frequency_monitor.threshold}条/分钟</strong></div>
                        <div>时间窗口: <strong>${stats.frequency_monitor.time_window}</strong></div>
                        <div>是否触发: <strong style="color: ${stats.frequency_monitor.triggered ? '#dc3545' : '#28a745'}">${stats.frequency_monitor.triggered ? '是' : '否'}</strong></div>
                        <div>过滤效果: <strong>${stats.frequency_monitor.filter_effectiveness}%</strong></div>
                    </div>
                </div>
            `;

            // 分析结果
            if (stats.analysis && stats.analysis.length > 0) {
                html += `
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">分析结果</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                `;
                stats.analysis.forEach(item => {
                    html += `<li style="margin-bottom: 5px;">${item}</li>`;
                });
                html += `</ul></div>`;
            }

            // 重复设备详情
            if (stats.duplicate_devices && stats.duplicate_devices.length > 0) {
                html += `
                    <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #856404;">重复设备详情（前10个）</h4>
                        <div style="max-height: 200px; overflow-y: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">设备ID</th>
                                        <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">分组</th>
                                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">记录数</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;
                stats.duplicate_devices.slice(0, 10).forEach(device => {
                    // 处理分组显示，如果分组为空或null则显示"未分组"
                    let deviceGroup = device.device_group || '未分组';
                    // 如果分组名称过长，进行截断
                    if (deviceGroup.length > 15) {
                        deviceGroup = deviceGroup.substring(0, 12) + '...';
                    }

                    html += `
                        <tr>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-family: monospace; font-size: 12px;">${device.device_id}</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-size: 12px; color: #495057;" title="${device.device_group || '未分组'}">${deviceGroup}</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: bold; color: #dc3545;">${device.record_count}</td>
                        </tr>
                    `;
                });
                html += `</tbody></table></div></div>`;
            }

            // 分组统计
            if (stats.group_statistics && stats.group_statistics.length > 0) {
                html += `
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #155724;">分组统计</h4>
                        <div style="max-height: 150px; overflow-y: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6;">分组名称</th>
                                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">重复设备数</th>
                                        <th style="padding: 8px; text-align: center; border-bottom: 1px solid #dee2e6;">总记录数</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;
                stats.group_statistics.slice(0, 10).forEach(group => {
                    let groupName = group.group_name || '未分组';
                    if (groupName.length > 20) {
                        groupName = groupName.substring(0, 17) + '...';
                    }

                    html += `
                        <tr>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; font-size: 12px; color: #495057;" title="${group.group_name || '未分组'}">${groupName}</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: bold; color: #dc3545;">${group.device_count}</td>
                            <td style="padding: 8px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: bold; color: #856404;">${group.total_records}</td>
                        </tr>
                    `;
                });
                html += `</tbody></table></div></div>`;
            }

            // 时间分布
            if (stats.time_distribution && stats.time_distribution.length > 0) {
                html += `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">时间分布</h4>
                        <div style="max-height: 150px; overflow-y: auto;">
                `;
                stats.time_distribution.forEach(timeData => {
                    const percentage = stats.total_records > 0 ? (timeData.count / stats.total_records * 100).toFixed(1) : 0;
                    html += `
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 4px 0; border-bottom: 1px solid #e9ecef;">
                            <span>${timeData.time_minute}</span>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 100px; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden;">
                                    <div style="width: ${percentage}%; height: 100%; background: #007bff;"></div>
                                </div>
                                <span style="font-weight: bold; min-width: 40px; text-align: right;">${timeData.count}</span>
                            </div>
                        </div>
                    `;
                });
                html += `</div></div>`;
            }

            html += `
                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 4px; font-size: 12px; color: #155724;">
                    查询时间: ${stats.query_time} | 时间范围: ${stats.time_range}
                </div>
            `;

            statsContent.innerHTML = html;
        }

        // 刷新统计数据
        function refreshStats() {
            loadBackendStats();
        }



        // 显示粉丝记录模态框
        function showFansRecordsModal() {
            document.getElementById('fans-records-modal').style.display = 'block';
            loadFansGroups();
            loadFansRecords();
        }

        // 关闭粉丝记录模态框
        function closeFansRecordsModal() {
            document.getElementById('fans-records-modal').style.display = 'none';
        }

        // 显示今日任务模态框
        function showDailyTasksModal() {
            document.getElementById('daily-tasks-modal').style.display = 'block';

            // 初始化日期筛选为今天（如果还没有设置）
            const dateFilter = document.getElementById('tasks-date-filter');
            if (!dateFilter.value) {
                const today = new Date();
                const todayStr = today.getFullYear() + '-' +
                    String(today.getMonth() + 1).padStart(2, '0') + '-' +
                    String(today.getDate()).padStart(2, '0');
                dateFilter.value = todayStr;
            }

            // 重置设备数量显示
            document.getElementById('daily-tasks-device-count').textContent = '-';

            loadTasksGroups();
            loadDailyTasks();
        }

        // 关闭今日任务模态框
        function closeDailyTasksModal() {
            document.getElementById('daily-tasks-modal').style.display = 'none';
        }

        // 加载粉丝记录分组选项
        function loadFansGroups() {
            fetch('monitor.php?action=get_fans_groups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const groupSelect = document.getElementById('fans-group-filter');
                        groupSelect.innerHTML = '<option value="">全部分组</option>';
                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group;
                            option.textContent = group;
                            groupSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading fans groups:', error);
                });
        }

        // 加载今日任务分组选项
        function loadTasksGroups() {
            fetch('monitor.php?action=get_device_groups')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const groupSelect = document.getElementById('tasks-group-filter');
                        groupSelect.innerHTML = '<option value="">全部分组</option>';

                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group;
                            option.textContent = group;
                            groupSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载分组失败:', error);
                });
        }

        // 加载粉丝记录数据
        function loadFansRecords() {
            console.log('loadFansRecords 函数被调用');

            // 显示加载状态
            document.getElementById('fans-stats').innerHTML = '<div class="loading-message"><p>正在加载统计数据...</p></div>';
            document.getElementById('fans-records-content').innerHTML = '<div class="loading-message"><p>正在加载粉丝记录数据...</p></div>';

            const deviceFilter = document.getElementById('fans-device-filter').value;
            const groupFilter = document.getElementById('fans-group-filter').value;
            const dateStart = document.getElementById('fans-date-start').value;
            const dateEnd = document.getElementById('fans-date-end').value;

            console.log('筛选参数:', { deviceFilter, groupFilter, dateStart, dateEnd });

            const params = new URLSearchParams({
                action: 'get_fans_records',
                device_filter: deviceFilter,
                group_filter: groupFilter,
                date_start: dateStart,
                date_end: dateEnd
            });

            const url = 'monitor.php?' + params.toString();
            console.log('请求URL:', url);

            fetch(url)
                .then(response => {
                    console.log('收到响应:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('收到数据:', data);
                    if (data.success) {
                        displayFansStats(data.stats);
                        displayFansRecords(data.records);
                    } else {
                        document.getElementById('fans-stats').innerHTML = '<p style="color: red;">加载统计数据失败：' + data.error + '</p>';
                        document.getElementById('fans-records-content').innerHTML = '<p style="color: red;">加载粉丝记录失败：' + data.error + '</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading fans records:', error);
                    document.getElementById('fans-stats').innerHTML = '<p style="color: red;">网络错误，无法加载统计数据：' + error.message + '</p>';
                    document.getElementById('fans-records-content').innerHTML = '<p style="color: red;">网络错误，无法加载粉丝记录：' + error.message + '</p>';
                });
        }

        // 显示粉丝统计信息
        function displayFansStats(stats) {
            const statsHtml = `
                <h3 style="margin: 0 0 15px 0; color: #495057;">统计概览</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #7b1fa2;">${stats.total_devices}</div>
                        <div style="color: #666; font-size: 14px;">设备数量</div>
                    </div>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #f57c00;">${stats.max_fans}</div>
                        <div style="color: #666; font-size: 14px;">最高粉丝数</div>
                    </div>
                </div>
            `;
            document.getElementById('fans-stats').innerHTML = statsHtml;
        }

        // 显示粉丝记录列表
        function displayFansRecords(records) {
            if (records.length === 0) {
                document.getElementById('fans-records-content').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <h3 style="margin-bottom: 10px;">暂无粉丝记录数据</h3>
                        <p>当前筛选条件下没有找到粉丝记录</p>
                    </div>
                `;
                return;
            }

            let tableHtml = `
                <table style="width: 100%; border-collapse: collapse; min-width: 1100px;">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">设备ID</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">设备型号</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">设备分组</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">最新粉丝数量</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">昨日粉丝数量</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">变化量</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">最新记录时间</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            records.forEach(record => {
                const recordTime = new Date(record.record_time);

                // 计算变化量的显示
                const fansChange = record.fans_change || 0;
                let changeColor = '#6c757d';
                let changeText = '0';
                let changeIcon = '';

                if (fansChange > 0) {
                    changeColor = '#28a745';
                    changeText = `+${fansChange.toLocaleString()}`;
                    changeIcon = '↗';
                } else if (fansChange < 0) {
                    changeColor = '#dc3545';
                    changeText = fansChange.toLocaleString();
                    changeIcon = '↘';
                } else {
                    changeText = '0';
                    changeIcon = '→';
                }

                tableHtml += `
                    <tr style="border-bottom: 1px solid #f8f9fa;">
                        <td style="padding: 12px; font-size: 13px;">${record.device_id}</td>
                        <td style="padding: 12px; font-size: 13px;">${record.device_model || '未知'}</td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 11px;">
                                ${record.device_group || '未分组'}
                            </span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="font-weight: bold; color: #2e7d32;">${record.fans_count.toLocaleString()}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="color: #6c757d;">${(record.yesterday_fans_count || 0).toLocaleString()}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="color: ${changeColor}; font-weight: bold;">
                                ${changeIcon} ${changeText}
                            </span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">${recordTime.toLocaleString()}</td>
                        <td style="padding: 12px; font-size: 13px;">
                            <button onclick="showDeviceFansHistory('${record.device_id}')"
                                    style="padding: 4px 8px; border: 1px solid #007bff; background: #007bff; color: white; border-radius: 4px; font-size: 12px; cursor: pointer;">
                                查看历史粉丝
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('fans-records-content').innerHTML = tableHtml;
        }

        // 全局变量存储当前查看的设备ID
        let currentDeviceId = '';

        // 显示设备历史粉丝记录模态框
        function showDeviceFansHistory(deviceId) {
            currentDeviceId = deviceId;
            document.getElementById('device-fans-history-modal').style.display = 'block';
            document.getElementById('device-fans-history-title').textContent = `设备 ${deviceId} 历史粉丝记录`;
            loadDeviceFansHistory(deviceId);
        }

        // 关闭设备历史粉丝记录模态框
        function closeDeviceFansHistoryModal() {
            document.getElementById('device-fans-history-modal').style.display = 'none';
            currentDeviceId = '';
        }

        // 刷新设备历史粉丝记录
        function refreshDeviceFansHistory() {
            if (currentDeviceId) {
                loadDeviceFansHistory(currentDeviceId);
            }
        }

        // 加载设备历史粉丝记录数据
        function loadDeviceFansHistory(deviceId) {
            console.log('loadDeviceFansHistory 函数被调用，设备ID:', deviceId);

            // 显示加载状态
            document.getElementById('device-fans-history-info').innerHTML = '<div class="loading-message"><p>正在加载设备信息...</p></div>';
            document.getElementById('device-fans-history-content').innerHTML = '<div class="loading-message"><p>正在加载历史记录数据...</p></div>';

            const params = new URLSearchParams({
                action: 'get_device_fans_history',
                device_id: deviceId
            });

            const url = 'monitor.php?' + params.toString();
            console.log('请求URL:', url);

            fetch(url)
                .then(response => {
                    console.log('收到响应:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('收到数据:', data);
                    if (data.success) {
                        displayDeviceFansInfo(data.device_id, data.history_records);
                        displayDeviceFansHistory(data.history_records);
                    } else {
                        document.getElementById('device-fans-history-info').innerHTML = '<p style="color: red;">加载设备信息失败：' + data.error + '</p>';
                        document.getElementById('device-fans-history-content').innerHTML = '<p style="color: red;">加载历史记录失败：' + data.error + '</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading device fans history:', error);
                    document.getElementById('device-fans-history-info').innerHTML = '<p style="color: red;">网络错误，无法加载设备信息：' + error.message + '</p>';
                    document.getElementById('device-fans-history-content').innerHTML = '<p style="color: red;">网络错误，无法加载历史记录：' + error.message + '</p>';
                });
        }

        // 显示设备信息
        function displayDeviceFansInfo(deviceId, historyRecords) {
            if (historyRecords.length === 0) {
                document.getElementById('device-fans-history-info').innerHTML = `
                    <h3 style="margin: 0 0 10px 0; color: #495057;">设备信息</h3>
                    <p>设备ID：${deviceId}</p>
                    <p style="color: #666;">该设备暂无粉丝记录</p>
                `;
                return;
            }

            const latestRecord = historyRecords[0];
            const totalRecords = historyRecords.length;

            // 计算粉丝数量变化趋势
            let trendText = '';
            let trendColor = '#666';
            if (historyRecords.length > 1) {
                const previousRecord = historyRecords[1];
                const change = latestRecord.fans_count - previousRecord.fans_count;
                if (change > 0) {
                    trendText = `较上次增加 ${change.toLocaleString()}`;
                    trendColor = '#2e7d32';
                } else if (change < 0) {
                    trendText = `较上次减少 ${Math.abs(change).toLocaleString()}`;
                    trendColor = '#d32f2f';
                } else {
                    trendText = '较上次无变化';
                    trendColor = '#666';
                }
            }

            const infoHtml = `
                <h3 style="margin: 0 0 15px 0; color: #495057;">设备信息</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">设备ID</div>
                        <div style="font-size: 16px; font-weight: bold; color: #495057;">${deviceId}</div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">设备型号</div>
                        <div style="font-size: 16px; font-weight: bold; color: #495057;">${latestRecord.device_model || '未知'}</div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">设备分组</div>
                        <div style="font-size: 16px; font-weight: bold; color: #495057;">${latestRecord.device_group || '未分组'}</div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">最新粉丝数</div>
                        <div style="font-size: 20px; font-weight: bold; color: #2e7d32;">${latestRecord.fans_count.toLocaleString()}</div>
                        ${trendText ? `<div style="font-size: 12px; color: ${trendColor};">${trendText}</div>` : ''}
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef;">
                        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">记录总数</div>
                        <div style="font-size: 20px; font-weight: bold; color: #1976d2;">${totalRecords}</div>
                    </div>
                </div>
            `;
            document.getElementById('device-fans-history-info').innerHTML = infoHtml;
        }

        // 显示设备历史粉丝记录列表
        function displayDeviceFansHistory(historyRecords) {
            if (historyRecords.length === 0) {
                document.getElementById('device-fans-history-content').innerHTML = `
                    <div style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <h3 style="margin-bottom: 10px;">暂无历史记录</h3>
                        <p>该设备还没有粉丝记录数据</p>
                    </div>
                `;
                return;
            }

            let tableHtml = `
                <table style="width: 100%; border-collapse: collapse; min-width: 800px;">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">序号</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">粉丝数量</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">变化量</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">记录时间</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">IP地址</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            historyRecords.forEach((record, index) => {
                const recordTime = new Date(record.record_time);

                // 计算变化量
                let changeText = '';
                let changeColor = '#666';
                if (index < historyRecords.length - 1) {
                    const previousRecord = historyRecords[index + 1];
                    const change = record.fans_count - previousRecord.fans_count;
                    if (change > 0) {
                        changeText = `+${change.toLocaleString()}`;
                        changeColor = '#2e7d32';
                    } else if (change < 0) {
                        changeText = `${change.toLocaleString()}`;
                        changeColor = '#d32f2f';
                    } else {
                        changeText = '0';
                        changeColor = '#666';
                    }
                } else {
                    changeText = '-';
                    changeColor = '#666';
                }

                tableHtml += `
                    <tr style="border-bottom: 1px solid #f8f9fa;">
                        <td style="padding: 12px; font-size: 13px;">${index + 1}</td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="font-weight: bold; color: #2e7d32;">${record.fans_count.toLocaleString()}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="color: ${changeColor}; font-weight: bold;">${changeText}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">${recordTime.toLocaleString()}</td>
                        <td style="padding: 12px; font-size: 13px;">${record.ip_address || '未知'}</td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('device-fans-history-content').innerHTML = tableHtml;
        }

        // ==================== 今日任务功能 ====================

        // 加载今日任务数据
        function loadDailyTasks() {
            const deviceFilter = document.getElementById('tasks-device-filter').value;
            const groupFilter = document.getElementById('tasks-group-filter').value;
            const dateFilter = document.getElementById('tasks-date-filter').value;

            let url = 'monitor.php?action=get_daily_tasks';
            const params = new URLSearchParams();

            if (deviceFilter) params.append('device_filter', deviceFilter);
            if (groupFilter) params.append('group_filter', groupFilter);
            if (dateFilter) params.append('date_filter', dateFilter);

            if (params.toString()) {
                url += '&' + params.toString();
            }

            document.getElementById('daily-tasks-content').innerHTML = '<div class="loading-message"><p>正在加载今日任务数据...</p></div>';

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayDailyTasks(data.tasks);
                        // 更新设备数量显示
                        updateDailyTasksDeviceCount(data.tasks);
                    } else {
                        document.getElementById('daily-tasks-content').innerHTML = '<div style="text-align: center; padding: 40px; color: #dc3545;"><p>加载失败：' + data.error + '</p></div>';
                        // 加载失败时重置设备数量显示
                        document.getElementById('daily-tasks-device-count').textContent = '-';
                    }
                })
                .catch(error => {
                    console.error('加载今日任务失败:', error);
                    document.getElementById('daily-tasks-content').innerHTML = '<div style="text-align: center; padding: 40px; color: #dc3545;"><p>加载失败：网络错误</p></div>';
                    // 网络错误时重置设备数量显示
                    document.getElementById('daily-tasks-device-count').textContent = '-';
                });
        }

        // 显示今日任务数据
        function displayDailyTasks(tasks) {
            if (!tasks || tasks.length === 0) {
                document.getElementById('daily-tasks-content').innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;"><p>暂无今日任务记录</p></div>';
                return;
            }

            let tableHtml = `
                <table style="width: 100%; border-collapse: collapse; min-width: 1000px;">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">设备ID</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">设备型号</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">设备分组</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">点赞目标</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">评论目标</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">推荐目标</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #f1f3f4; font-weight: 600; color: #495057;">记录时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            tasks.forEach(task => {
                const recordTime = new Date(task.record_time);

                tableHtml += `
                    <tr style="border-bottom: 1px solid #f8f9fa;">
                        <td style="padding: 12px; font-size: 13px;">${task.device_id}</td>
                        <td style="padding: 12px; font-size: 13px;">${task.device_model || '未知'}</td>
                        <td style="padding: 12px; font-size: 13px;">
                            <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 11px;">
                                ${task.device_group || '未分组'}
                            </span>
                        </td>
                        <td style="padding: 12px; font-size: 13px; text-align: center;">
                            <span style="color: #e74c3c; font-weight: bold;">${task.likes_target}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px; text-align: center;">
                            <span style="color: #3498db; font-weight: bold;">${task.comments_target}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px; text-align: center;">
                            <span style="color: #f39c12; font-weight: bold;">${task.recommendations_target}</span>
                        </td>
                        <td style="padding: 12px; font-size: 13px;">${recordTime.toLocaleString()}</td>
                    </tr>
                `;
            });

            tableHtml += `
                    </tbody>
                </table>
            `;

            document.getElementById('daily-tasks-content').innerHTML = tableHtml;
        }

        // 更新今日任务设备数量显示
        function updateDailyTasksDeviceCount(tasks) {
            if (!tasks || tasks.length === 0) {
                document.getElementById('daily-tasks-device-count').textContent = '0';
                return;
            }

            // 统计唯一设备数量（去重）
            const uniqueDevices = new Set();
            tasks.forEach(task => {
                if (task.device_id) {
                    uniqueDevices.add(task.device_id);
                }
            });

            const deviceCount = uniqueDevices.size;
            document.getElementById('daily-tasks-device-count').textContent = deviceCount;

            // 根据数量调整显示样式
            const countElement = document.getElementById('daily-tasks-device-count').parentElement;
            if (deviceCount === 0) {
                countElement.style.background = '#f5f5f5';
                countElement.style.borderColor = '#d6d8db';
                countElement.style.color = '#6c757d';
            } else if (deviceCount < 10) {
                countElement.style.background = '#fff3cd';
                countElement.style.borderColor = '#ffeaa7';
                countElement.style.color = '#856404';
            } else {
                countElement.style.background = '#e3f2fd';
                countElement.style.borderColor = '#90caf9';
                countElement.style.color = '#1565c0';
            }
        }

        // ==================== 停止设备运行功能 ====================

        // 显示停止设备模态框
        function showStopDevicesModal(mode = 'all') {
            document.getElementById('stop-devices-modal').style.display = 'block';

            // 设置模式
            document.querySelector(`input[name="stop_mode"][value="${mode}"]`).checked = true;
            updateStopMode();

            // 加载分组列表
            loadGroupsForStop();

            // 加载在线设备列表
            loadOnlineDevicesForStop();
        }

        // 关闭停止设备模态框
        function closeStopDevicesModal() {
            document.getElementById('stop-devices-modal').style.display = 'none';
        }

        // 更新停止模式
        function updateStopMode() {
            const mode = document.querySelector('input[name="stop_mode"]:checked').value;
            const groupSelection = document.getElementById('group-selection');
            const deviceSelection = document.getElementById('device-selection');
            const targetInfo = document.getElementById('stop-target-info');

            // 隐藏所有选择区域
            groupSelection.style.display = 'none';
            deviceSelection.style.display = 'none';

            switch (mode) {
                case 'all':
                    targetInfo.textContent = '目标：所有在线设备';
                    break;
                case 'group':
                    groupSelection.style.display = 'block';
                    targetInfo.textContent = '目标：选定分组的在线设备';
                    break;
                case 'selected':
                    deviceSelection.style.display = 'block';
                    targetInfo.textContent = '目标：手动选择的设备';
                    break;
            }
        }

        // 加载分组列表
        function loadGroupsForStop() {
            fetch('monitor.php?action=get_groups')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('stop_group_select');
                    select.innerHTML = '<option value="">请选择分组</option>';

                    if (data.success && data.groups) {
                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.group_name;
                            option.textContent = `${group.group_name} (${group.description || '无描述'})`;
                            select.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载分组列表失败:', error);
                });
        }

        // 存储设备数据
        let allDevicesData = [];

        // 加载在线设备列表
        function loadOnlineDevicesForStop() {
            fetch('monitor.php?action=get_online_devices')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.devices && data.devices.length > 0) {
                        allDevicesData = data.devices;
                        renderDeviceList(allDevicesData);
                        updateDeviceStats();
                    } else {
                        allDevicesData = [];
                        document.getElementById('device-list').innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">当前没有在线设备</div>';
                        updateDeviceStats();
                    }
                })
                .catch(error => {
                    console.error('加载设备列表失败:', error);
                    allDevicesData = [];
                    document.getElementById('device-list').innerHTML = '<div style="text-align: center; color: #e74c3c; padding: 20px;">加载设备列表失败</div>';
                    updateDeviceStats();
                });
        }

        // 渲染设备列表
        function renderDeviceList(devices) {
            const deviceList = document.getElementById('device-list');

            if (devices.length === 0) {
                deviceList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">没有匹配的设备</div>';
                return;
            }

            let html = '<div style="margin-bottom: 10px;"><label style="display: flex; align-items: center; gap: 5px; font-weight: bold;"><input type="checkbox" id="select-all-devices" onchange="toggleAllDevices(this)"> 全选/取消全选</label></div>';

            devices.forEach(device => {
                const groupText = device.device_group ? ` (${device.device_group})` : ' (未分组)';
                const lastHeartbeat = device.last_heartbeat ? new Date(device.last_heartbeat).toLocaleString() : '未知';

                html += `
                    <label class="device-item" style="display: flex; align-items: center; gap: 8px; padding: 8px; border-bottom: 1px solid #eee; background: #fff; margin: 2px 0; border-radius: 3px;">
                        <input type="checkbox" name="selected_devices" value="${device.device_id}" onchange="updateDeviceStats()">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span style="font-family: monospace; font-size: 13px; font-weight: bold; color: #2c3e50;">${device.device_id}</span>
                                <span style="color: #666; font-size: 12px;">${device.device_model || '未知型号'}</span>
                                <span style="color: #007bff; font-size: 11px; background: #e3f2fd; padding: 2px 6px; border-radius: 10px;">${device.device_group || '未分组'}</span>
                            </div>
                            <div style="font-size: 10px; color: #999; margin-top: 2px;">
                                最后心跳: ${lastHeartbeat}
                            </div>
                        </div>
                    </label>
                `;
            });

            deviceList.innerHTML = html;
        }

        // 过滤设备列表
        function filterDeviceList() {
            const searchTerm = document.getElementById('device-search').value.toLowerCase().trim();

            if (!searchTerm) {
                renderDeviceList(allDevicesData);
            } else {
                const filteredDevices = allDevicesData.filter(device =>
                    device.device_id.toLowerCase().includes(searchTerm) ||
                    (device.device_model && device.device_model.toLowerCase().includes(searchTerm)) ||
                    (device.device_group && device.device_group.toLowerCase().includes(searchTerm))
                );
                renderDeviceList(filteredDevices);
            }

            updateDeviceStats();
        }

        // 更新设备统计信息
        function updateDeviceStats() {
            const totalDevices = allDevicesData.length;
            const visibleDevices = document.querySelectorAll('.device-item').length;
            const selectedDevices = document.querySelectorAll('input[name="selected_devices"]:checked').length;

            document.getElementById('total-devices').textContent = `总设备: ${totalDevices}`;
            document.getElementById('visible-devices').textContent = `显示: ${visibleDevices}`;
            document.getElementById('selected-devices').textContent = `已选: ${selectedDevices}`;

            // 更新全选复选框状态
            const selectAllCheckbox = document.getElementById('select-all-devices');
            const visibleCheckboxes = document.querySelectorAll('input[name="selected_devices"]');

            if (selectAllCheckbox && visibleCheckboxes.length > 0) {
                const checkedCount = document.querySelectorAll('input[name="selected_devices"]:checked').length;
                selectAllCheckbox.checked = checkedCount === visibleCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < visibleCheckboxes.length;
            }
        }

        // 切换全选设备
        function toggleAllDevices(checkbox) {
            const deviceCheckboxes = document.querySelectorAll('input[name="selected_devices"]');
            deviceCheckboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
            updateDeviceStats();
        }

        // 执行停止设备操作
        function executeStopDevices() {
            const mode = document.querySelector('input[name="stop_mode"]:checked').value;
            const confirmBtn = document.getElementById('confirm-stop-btn');

            // 禁用按钮防止重复点击
            confirmBtn.disabled = true;
            confirmBtn.textContent = '正在发送停止指令...';

            let deviceIds = [];
            let targetDescription = '';

            switch (mode) {
                case 'all':
                    // 获取所有在线设备
                    fetch('monitor.php?action=get_online_devices')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.devices) {
                                deviceIds = data.devices.map(device => device.device_id);
                                targetDescription = `所有在线设备 (${deviceIds.length}台)`;
                                sendStopCommand(deviceIds, targetDescription);
                            } else {
                                throw new Error('获取在线设备列表失败');
                            }
                        })
                        .catch(error => {
                            showStopResult(false, '获取设备列表失败: ' + error.message);
                            resetStopButton();
                        });
                    break;

                case 'group':
                    const selectedGroup = document.getElementById('stop_group_select').value;
                    if (!selectedGroup) {
                        showStopResult(false, '请选择要停止的分组');
                        resetStopButton();
                        return;
                    }

                    // 获取分组内的在线设备
                    fetch(`monitor.php?action=get_online_devices&group=${encodeURIComponent(selectedGroup)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.devices) {
                                deviceIds = data.devices.map(device => device.device_id);
                                targetDescription = `分组"${selectedGroup}"的在线设备 (${deviceIds.length}台)`;

                                if (deviceIds.length === 0) {
                                    throw new Error(`分组"${selectedGroup}"中没有在线设备`);
                                }

                                sendStopCommand(deviceIds, targetDescription);
                            } else {
                                throw new Error('获取分组设备列表失败');
                            }
                        })
                        .catch(error => {
                            showStopResult(false, error.message);
                            resetStopButton();
                        });
                    break;

                case 'selected':
                    const selectedCheckboxes = document.querySelectorAll('input[name="selected_devices"]:checked');
                    if (selectedCheckboxes.length === 0) {
                        showStopResult(false, '请至少选择一台设备');
                        resetStopButton();
                        return;
                    }

                    deviceIds = Array.from(selectedCheckboxes).map(cb => cb.value);
                    targetDescription = `手动选择的设备 (${deviceIds.length}台)`;
                    sendStopCommand(deviceIds, targetDescription);
                    break;
            }
        }

        // 发送停止指令
        function sendStopCommand(deviceIds, targetDescription) {
            const formData = new FormData();
            formData.append('action', 'send_command');
            formData.append('command', 'stop');
            formData.append('device_ids', JSON.stringify(deviceIds));
            formData.append('created_by', 'admin');

            fetch('api/remote_commands.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('远程指令API响应状态:', response.status);
                console.log('响应头:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }

                return response.text().then(text => {
                    console.log('原始响应内容:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON解析失败:', e);
                        console.error('响应内容:', text);
                        throw new Error(`服务器返回了无效的JSON数据。响应内容: ${text.substring(0, 200)}...`);
                    }
                });
            })
            .then(data => {
                console.log('解析后的数据:', data);
                if (data.success) {
                    const message = `停止指令发送成功！\n目标：${targetDescription}\n成功：${data.success_count}台\n失败：${data.failed_devices.length}台`;
                    showStopResult(true, message, data.failed_devices);
                } else {
                    showStopResult(false, '发送停止指令失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('发送停止指令错误:', error);
                showStopResult(false, '发送停止指令时发生错误: ' + error.message);
            })
            .finally(() => {
                resetStopButton();
            });
        }

        // 重置停止按钮
        function resetStopButton() {
            const confirmBtn = document.getElementById('confirm-stop-btn');
            confirmBtn.disabled = false;
            confirmBtn.textContent = '确认停止';
        }

        // 显示停止结果
        function showStopResult(success, message, failedDevices = []) {
            let alertMessage = message;

            if (failedDevices && failedDevices.length > 0) {
                alertMessage += '\n\n失败的设备：\n' + failedDevices.join('\n');
            }

            if (success) {
                alert('[成功] ' + alertMessage);
                closeStopDevicesModal();
                // 刷新页面数据
                if (typeof refreshHeartbeat === 'function') {
                    setTimeout(refreshHeartbeat, 1000);
                }
            } else {
                alert('[失败] ' + alertMessage);
            }
        }

        // 定时任务管理相关函数
        function createCronTask() {
            if (!confirm('确定要创建定时任务吗？任务将每小时自动执行一次过期设备检查。')) {
                return;
            }

            showCronMessage('正在创建定时任务...', 'info');

            const formData = new FormData();
            formData.append('action', 'cron_create');

            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showCronMessage(data.message, 'success');
                    setTimeout(checkCronStatus, 1000); // 1秒后检查状态
                } else {
                    showCronMessage('创建失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showCronMessage('请求失败: ' + error.message, 'error');
            });
        }

        function deleteCronTask() {
            if (!confirm('确定要删除定时任务吗？删除后将不会自动检查过期设备。')) {
                return;
            }

            showCronMessage('正在删除定时任务...', 'info');

            const formData = new FormData();
            formData.append('action', 'cron_delete');

            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showCronMessage(data.message, 'success');
                    setTimeout(checkCronStatus, 1000); // 1秒后检查状态
                } else {
                    showCronMessage('删除失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showCronMessage('请求失败: ' + error.message, 'error');
            });
        }

        function checkCronStatus() {
            showCronMessage('正在检查任务状态...', 'info');

            const formData = new FormData();
            formData.append('action', 'cron_status');

            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const statusContent = document.getElementById('cron-status-content');

                    if (data.exists) {
                        const status = data.status;
                        statusContent.innerHTML = `
                            <div style="color: #28a745; margin-bottom: 10px;">
                                <strong>✓ 定时任务已创建</strong>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 14px;">
                                <div><strong>任务名称:</strong> ${status['TaskName'] || '设备使用时间限制检查'}</div>
                                <div><strong>状态:</strong> ${status['Status'] || '未知'}</div>
                                <div><strong>下次运行:</strong> ${status['Next Run Time'] || '未知'}</div>
                                <div><strong>上次运行:</strong> ${status['Last Run Time'] || '未运行'}</div>
                            </div>
                        `;
                        showCronMessage('任务状态检查完成', 'success');
                    } else {
                        statusContent.innerHTML = `
                            <div style="color: #dc3545;">
                                <strong>✗ 定时任务不存在</strong>
                            </div>
                            <p style="margin-top: 10px; color: #6c757d;">请点击"创建定时任务"按钮来创建自动检查任务。</p>
                        `;
                        showCronMessage('定时任务不存在', 'warning');
                    }
                } else {
                    showCronMessage('检查失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showCronMessage('请求失败: ' + error.message, 'error');
            });
        }

        function runCronNow() {
            if (!confirm('确定要立即执行一次过期设备检查吗？过期设备将被自动拉黑。')) {
                return;
            }

            showCronMessage('正在执行检查，请稍候...', 'info');

            const formData = new FormData();
            formData.append('action', 'cron_run_now');

            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showCronMessage(data.message, 'success');

                    // 显示执行输出
                    if (data.output) {
                        const logContent = document.getElementById('log-content');
                        logContent.innerHTML = `<pre style="white-space: pre-wrap; margin: 0;">${data.output}</pre>`;
                    }
                } else {
                    showCronMessage('执行失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showCronMessage('请求失败: ' + error.message, 'error');
            });
        }

        function showCronMessage(message, type) {
            const messageArea = document.getElementById('cron-message-area');
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-error' :
                              type === 'warning' ? 'alert-warning' : 'alert-info';

            messageArea.innerHTML = `
                <div class="alert ${alertClass}" style="margin: 15px 0; padding: 15px; border-radius: 4px;">
                    ${message}
                </div>
            `;

            // 3秒后自动隐藏信息消息（但保留错误消息）
            if (type === 'info') {
                setTimeout(() => {
                    messageArea.innerHTML = '';
                }, 3000);
            }
        }

        // 页面加载时自动检查定时任务状态
        if (window.location.search.includes('view=cron_tasks')) {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(checkCronStatus, 500); // 0.5秒后检查状态
            });
        }

    </script>
</body>
</html>
