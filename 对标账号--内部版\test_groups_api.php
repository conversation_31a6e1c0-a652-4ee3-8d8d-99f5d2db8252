<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试分组API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>分组API测试页面</h1>
        <p>用于测试device_usage_limits.php和monitor.php中的分组API功能</p>
        
        <div class="test-section">
            <h3>1. 测试device_usage_limits.php的分组API (GET)</h3>
            <button onclick="testDeviceUsageLimitsGet()">测试GET请求</button>
            <div id="result1" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试device_usage_limits.php的分组API (POST)</h3>
            <button onclick="testDeviceUsageLimitsPost()">测试POST请求</button>
            <div id="result2" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 测试monitor.php的分组API</h3>
            <button onclick="testMonitorGroups()">测试monitor.php</button>
            <div id="result3" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 测试获取分组设备</h3>
            <input type="text" id="groupName" placeholder="输入分组名称" style="padding: 8px; margin-right: 10px;">
            <button onclick="testGetDevicesByGroup()">获取分组设备</button>
            <div id="result4" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 数据库分组数据检查</h3>
            <button onclick="checkDatabaseGroups()">检查数据库</button>
            <div id="result5" class="result"></div>
        </div>
    </div>

    <script>
        function testDeviceUsageLimitsGet() {
            const resultDiv = document.getElementById('result1');
            resultDiv.textContent = '正在测试...';
            resultDiv.className = 'result';
            
            fetch('device_usage_limits.php?action=get_device_groups')
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = 'GET请求结果:\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testDeviceUsageLimitsPost() {
            const resultDiv = document.getElementById('result2');
            resultDiv.textContent = '正在测试...';
            resultDiv.className = 'result';
            
            const formData = new FormData();
            formData.append('action', 'get_device_groups');
            
            fetch('device_usage_limits.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = 'POST请求结果:\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testMonitorGroups() {
            const resultDiv = document.getElementById('result3');
            resultDiv.textContent = '正在测试...';
            resultDiv.className = 'result';
            
            fetch('monitor.php?action=get_device_groups')
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = 'monitor.php结果:\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testGetDevicesByGroup() {
            const resultDiv = document.getElementById('result4');
            const groupName = document.getElementById('groupName').value;
            
            if (!groupName) {
                resultDiv.textContent = '请输入分组名称';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.textContent = '正在获取分组设备...';
            resultDiv.className = 'result';
            
            const formData = new FormData();
            formData.append('action', 'get_devices_by_group');
            formData.append('group_name', groupName);
            
            fetch('device_usage_limits.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = '分组设备结果:\n' + JSON.stringify(data, null, 2);
                    resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function checkDatabaseGroups() {
            const resultDiv = document.getElementById('result5');
            resultDiv.textContent = '正在检查数据库...';
            resultDiv.className = 'result';
            
            fetch('debug_groups.php')
                .then(response => response.text())
                .then(data => {
                    resultDiv.textContent = data;
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
    </script>
</body>
</html>
