<?php
session_start();
require_once 'auth_config.php';
require_once 'auth_middleware.php';

// 记录退出日志
if (isset($_SESSION['username'])) {
    log_user_action('logout', 'User logged out');
}

// 清除记住我cookie
if (isset($_COOKIE['remember_token'])) {
    $secure_cookie = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    setcookie('remember_token', '', time() - 3600, '/', '', $secure_cookie, true);

    // 记录清除记住我cookie的日志
    if (isset($_SESSION['username'])) {
        error_log("[" . date('Y-m-d H:i:s') . "] 用户 " . $_SESSION['username'] . " 退出时清除了记住我cookie");
    }
}

// 销毁会话
session_destroy();

// 重新启动会话以设置退出消息
session_start();
$_SESSION['logout_message'] = '您已成功退出登录';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录 - 设备监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logout-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .logout-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 20px;
        }
        
        .logout-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 15px;
        }
        
        .logout-message {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .logout-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
        }
        
        .countdown {
            margin-top: 20px;
            font-size: 12px;
            color: #999;
        }
        
        .security-info {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 12px;
            color: #666;
        }
        
        @media (max-width: 480px) {
            .logout-container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            .logout-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="logout-container">
                        <div class="logout-icon"></div>
        <h1 class="logout-title">退出成功</h1>
        <p class="logout-message">
            您已安全退出设备监控系统。<br>
            感谢您的使用，期待您的再次访问！
        </p>
        
        <div class="logout-actions">
            <a href="login.php" class="btn btn-primary">重新登录</a>
            <a href="javascript:window.close()" class="btn btn-secondary">关闭窗口</a>
        </div>
        
        <div class="countdown">
            <span id="countdown-text">5秒后自动跳转到登录页面</span>
        </div>
        
        <div class="security-info">
            <p><strong>安全提示：</strong></p>
            <p>• 您的会话已完全清除</p>
            <p>• 记住我功能已取消</p>
            <p>• 建议关闭浏览器以确保安全</p>
        </div>
    </div>
    
    <script>
        // 倒计时自动跳转
        let countdown = 5;
        const countdownElement = document.getElementById('countdown-text');
        
        const timer = setInterval(function() {
            countdown--;
            if (countdown > 0) {
                countdownElement.textContent = countdown + '秒后自动跳转到登录页面';
            } else {
                clearInterval(timer);
                window.location.href = 'login.php';
            }
        }, 1000);
        
        // 点击任意位置取消自动跳转
        document.addEventListener('click', function() {
            clearInterval(timer);
            countdownElement.textContent = '自动跳转已取消';
        });
        
        // 清除可能的敏感数据
        if (window.history && window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
        
        // 防止后退到受保护页面
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                window.location.reload();
            }
        });
    </script>
</body>
</html>
