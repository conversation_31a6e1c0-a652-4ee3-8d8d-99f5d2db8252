<?php
// 快速黑名单检查脚本

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

// 创建连接
$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");

echo "<h2>快速黑名单检查</h2>";
echo "<p>当前时间：" . date('Y-m-d H:i:s') . "</p>";

try {
    // 1. 检查表是否存在
    $check_table_sql = "SHOW TABLES LIKE 'device_blacklist'";
    $table_result = $conn->query($check_table_sql);
    
    if ($table_result && $table_result->num_rows > 0) {
        echo "<p style='color: green;'>device_blacklist 表存在</p>";
        
        // 2. 检查关键字段是否存在
        $check_fields = ['is_active', 'blacklist_end_time'];
        $missing_fields = [];
        
        foreach ($check_fields as $field) {
            $check_field_sql = "SHOW COLUMNS FROM device_blacklist LIKE '{$field}'";
            $field_result = $conn->query($check_field_sql);
            
            if ($field_result && $field_result->num_rows > 0) {
                echo "<p style='color: green;'>字段 {$field} 存在</p>";
            } else {
                echo "<p style='color: red;'>字段 {$field} 不存在</p>";
                $missing_fields[] = $field;
            }
        }
        
        // 3. 如果有缺失字段，提供修复SQL
        if (!empty($missing_fields)) {
            echo "<h3>修复SQL（请在数据库中执行）：</h3>";
            echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
            
            if (in_array('is_active', $missing_fields)) {
                echo "ALTER TABLE device_blacklist ADD COLUMN is_active TINYINT(1) DEFAULT 1;\n";
            }
            if (in_array('blacklist_end_time', $missing_fields)) {
                echo "ALTER TABLE device_blacklist ADD COLUMN blacklist_end_time TIMESTAMP;\n";
            }
            
            echo "\n-- 为旧记录设置默认值\n";
            echo "UPDATE device_blacklist \n";
            echo "SET blacklist_end_time = DATE_ADD(blacklist_time, INTERVAL 24 HOUR),\n";
            echo "    is_active = 1\n";
            echo "WHERE blacklist_end_time IS NULL OR is_active IS NULL;\n";
            echo "</pre>";
        } else {
            // 4. 字段都存在，检查数据
            echo "<h3>数据统计：</h3>";
            
            // 总记录数
            $total_sql = "SELECT COUNT(*) as total FROM device_blacklist";
            $total_result = $conn->query($total_sql);
            $total_count = $total_result ? $total_result->fetch_assoc()['total'] : 0;
            echo "<p>总记录数：{$total_count}</p>";
            
            if ($total_count > 0) {
                // 活跃记录数
                $active_sql = "SELECT COUNT(*) as active_count FROM device_blacklist WHERE is_active = 1";
                $active_result = $conn->query($active_sql);
                $active_count = $active_result ? $active_result->fetch_assoc()['active_count'] : 0;
                echo "<p>🟢 活跃记录数：{$active_count}</p>";
                
                // 有效期内记录数
                $valid_sql = "SELECT COUNT(*) as valid_count FROM device_blacklist WHERE blacklist_end_time > NOW()";
                $valid_result = $conn->query($valid_sql);
                $valid_count = $valid_result ? $valid_result->fetch_assoc()['valid_count'] : 0;
                echo "<p>有效期内记录数：{$valid_count}</p>";
                
                // 应该显示的记录数
                $should_show_sql = "SELECT COUNT(*) as should_show FROM device_blacklist WHERE is_active = 1 AND blacklist_end_time > NOW()";
                $should_show_result = $conn->query($should_show_sql);
                $should_show_count = $should_show_result ? $should_show_result->fetch_assoc()['should_show'] : 0;
                echo "<p style='font-weight: bold; color: blue;'>应该显示的记录数：{$should_show_count}</p>";
                
                if ($should_show_count > 0) {
                    echo "<p style='color: green;'>有记录应该显示，如果界面显示"暂无设备"，可能是前端问题</p>";
                    
                    // 显示具体记录
                    echo "<h3>应该显示的记录：</h3>";
                    $show_records_sql = "SELECT device_id, blacklist_reason, blacklisted_by, blacklist_time, blacklist_end_time, is_active
                                        FROM device_blacklist 
                                        WHERE is_active = 1 AND blacklist_end_time > NOW()
                                        ORDER BY blacklist_time DESC LIMIT 5";
                    $show_records_result = $conn->query($show_records_sql);
                    
                    if ($show_records_result && $show_records_result->num_rows > 0) {
                        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                        echo "<tr style='background: #f0f0f0;'>";
                        echo "<th style='padding: 5px;'>设备ID</th>";
                        echo "<th style='padding: 5px;'>拉黑原因</th>";
                        echo "<th style='padding: 5px;'>操作人</th>";
                        echo "<th style='padding: 5px;'>拉黑时间</th>";
                        echo "<th style='padding: 5px;'>结束时间</th>";
                        echo "<th style='padding: 5px;'>活跃状态</th>";
                        echo "</tr>";
                        
                        while ($row = $show_records_result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td style='padding: 5px; font-family: monospace;'>{$row['device_id']}</td>";
                            echo "<td style='padding: 5px;'>" . substr($row['blacklist_reason'] ?? '', 0, 30) . "...</td>";
                            echo "<td style='padding: 5px;'>{$row['blacklisted_by']}</td>";
                            echo "<td style='padding: 5px;'>{$row['blacklist_time']}</td>";
                            echo "<td style='padding: 5px;'>{$row['blacklist_end_time']}</td>";
                            echo "<td style='padding: 5px;'>{$row['is_active']}</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                } else {
                    echo "<p style='color: orange;'>没有记录应该显示</p>";
                    
                    if ($total_count > 0) {
                        echo "<p>可能的原因：</p>";
                        echo "<ul>";
                        echo "<li>所有记录的 is_active = 0（已被解除拉黑）</li>";
                        echo "<li>所有记录的 blacklist_end_time 已过期</li>";
                        echo "<li>记录的字段值为 NULL</li>";
                        echo "</ul>";
                        
                        // 显示一些样本记录用于分析
                        echo "<h3>样本记录分析：</h3>";
                        $sample_sql = "SELECT device_id, is_active, blacklist_end_time, blacklist_time, 
                                      CASE WHEN blacklist_end_time > NOW() THEN '有效' ELSE '过期' END as time_status
                                      FROM device_blacklist 
                                      ORDER BY blacklist_time DESC LIMIT 3";
                        $sample_result = $conn->query($sample_sql);
                        
                        if ($sample_result && $sample_result->num_rows > 0) {
                            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                            echo "<tr style='background: #f0f0f0;'>";
                            echo "<th style='padding: 5px;'>设备ID</th>";
                            echo "<th style='padding: 5px;'>活跃状态</th>";
                            echo "<th style='padding: 5px;'>结束时间</th>";
                            echo "<th style='padding: 5px;'>时间状态</th>";
                            echo "</tr>";
                            
                            while ($row = $sample_result->fetch_assoc()) {
                                $row_color = ($row['is_active'] == 1 && $row['time_status'] == '有效') ? '#d4edda' : '#f8d7da';
                                echo "<tr style='background: {$row_color};'>";
                                echo "<td style='padding: 5px;'>{$row['device_id']}</td>";
                                echo "<td style='padding: 5px;'>{$row['is_active']}</td>";
                                echo "<td style='padding: 5px;'>{$row['blacklist_end_time']}</td>";
                                echo "<td style='padding: 5px;'>{$row['time_status']}</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                            echo "<p><small>绿色：应该显示，红色：不应该显示</small></p>";
                        }
                    }
                }
            } else {
                echo "<p style='color: orange;'>数据库中没有黑名单记录</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>device_blacklist 表不存在</p>";
        echo "<h3>创建表的SQL：</h3>";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
        echo "CREATE TABLE device_blacklist (\n";
        echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
        echo "    device_id VARCHAR(100) NOT NULL UNIQUE,\n";
        echo "    device_model VARCHAR(255),\n";
        echo "    blacklist_reason TEXT,\n";
        echo "    trigger_count INT DEFAULT 0,\n";
        echo "    blacklist_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
        echo "    blacklist_end_time TIMESTAMP,\n";
        echo "    blacklist_duration_hours INT DEFAULT 24,\n";
        echo "    is_active TINYINT(1) DEFAULT 1,\n";
        echo "    blacklisted_by VARCHAR(100) DEFAULT 'system',\n";
        echo "    blacklist_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
        echo "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n";
        echo "    INDEX idx_device_id (device_id),\n";
        echo "    INDEX idx_is_active (is_active),\n";
        echo "    INDEX idx_blacklist_end_time (blacklist_end_time)\n";
        echo ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n";
        echo "</pre>";
    }
    
    // 5. 测试API调用
    echo "<h3>API测试：</h3>";
    echo "<p><a href='monitor.php?action=get_blacklist' target='_blank' style='color: blue; text-decoration: underline;'>点击测试黑名单API</a></p>";
    echo "<p><small>（会在新窗口打开，显示JSON数据）</small></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误：{$e->getMessage()}</p>";
}

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    line-height: 1.6;
}
table {
    margin: 10px 0;
}
th, td {
    padding: 5px 10px;
    text-align: left;
    border: 1px solid #ddd;
}
th {
    background-color: #f0f0f0;
}
pre {
    overflow-x: auto;
}
</style>
