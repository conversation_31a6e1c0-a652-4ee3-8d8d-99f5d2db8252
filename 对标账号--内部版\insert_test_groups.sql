-- 插入测试分组数据
-- 用于测试设备分组功能

-- 1. 插入设备分组数据到device_groups表
INSERT IGNORE INTO device_groups (group_name, description, created_at) VALUES
('测试分组A', '用于测试的设备分组A', NOW()),
('测试分组B', '用于测试的设备分组B', NOW()),
('生产环境', '生产环境设备分组', NOW()),
('开发环境', '开发环境设备分组', NOW()),
('测试环境', '测试环境设备分组', NOW());

-- 2. 插入测试设备数据到devices表（如果表存在的话）
INSERT IGNORE INTO devices (device_id, device_model, device_group, last_seen, created_at) VALUES
('TEST001', '测试设备型号A', '测试分组A', NOW(), NOW()),
('TEST002', '测试设备型号B', '测试分组A', NOW(), NOW()),
('TEST003', '测试设备型号C', '测试分组B', NOW(), NOW()),
('PROD001', '生产设备型号A', '生产环境', NOW(), NOW()),
('PROD002', '生产设备型号B', '生产环境', NOW(), NOW()),
('DEV001', '开发设备型号A', '开发环境', NOW(), NOW()),
('DEV002', '开发设备型号B', '开发环境', NOW(), NOW());

-- 3. 插入测试心跳数据到device_heartbeat表（如果表存在的话）
INSERT IGNORE INTO device_heartbeat (device_id, device_model, device_group, heartbeat_time, created_at) VALUES
('TEST001', '测试设备型号A', '测试分组A', NOW(), NOW()),
('TEST002', '测试设备型号B', '测试分组A', NOW(), NOW()),
('TEST003', '测试设备型号C', '测试分组B', NOW(), NOW()),
('PROD001', '生产设备型号A', '生产环境', NOW(), NOW()),
('PROD002', '生产设备型号B', '生产环境', NOW(), NOW()),
('DEV001', '开发设备型号A', '开发环境', NOW(), NOW()),
('DEV002', '开发设备型号B', '开发环境', NOW(), NOW());

-- 查询验证数据是否插入成功
SELECT '=== device_groups表数据 ===' as info;
SELECT group_name, description, created_at FROM device_groups ORDER BY group_name;

SELECT '=== devices表中的分组统计 ===' as info;
SELECT device_group, COUNT(*) as device_count FROM devices 
WHERE device_group IS NOT NULL AND device_group != '' 
GROUP BY device_group ORDER BY device_group;

SELECT '=== device_heartbeat表中的分组统计 ===' as info;
SELECT device_group, COUNT(*) as heartbeat_count FROM device_heartbeat 
WHERE device_group IS NOT NULL AND device_group != '' 
GROUP BY device_group ORDER BY device_group;
