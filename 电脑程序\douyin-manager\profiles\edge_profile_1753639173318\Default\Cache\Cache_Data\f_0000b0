!function(){"use strict";var e={};(function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})})(e);var t=function(){return(t=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function n(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)a.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return a}function i(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(n(arguments[t]));return e}var r,o,a,s,c,u=function(){function e(){this._hooks={},this._cache=[],this._hooksCache={}}return e.prototype.on=function(e,t){e&&t&&"function"==typeof t&&(this._hooks[e]||(this._hooks[e]=[]),this._hooks[e].push(t))},e.prototype.once=function(e,t){var n=this;e&&t&&"function"==typeof t&&this.on(e,(function i(r){t(r),n.off(e,i)}))},e.prototype.off=function(e,t){if(e&&this._hooks[e]&&this._hooks[e].length)if(t){var n=this._hooks[e].indexOf(t);-1!==n&&this._hooks[e].splice(n,1)}else this._hooks[e]=[]},e.prototype.emit=function(e,t,n){if(n){if(!e)return;-1!==this._cache.indexOf(n)?this._emit(e,t):(this._hooksCache.hasOwnProperty(n)||(this._hooksCache[n]={}),this._hooksCache[n].hasOwnProperty(e)||(this._hooksCache[n][e]=[]),this._hooksCache[n][e].push(t))}else this._emit(e,t)},e.prototype._emit=function(e,t){e&&this._hooks[e]&&this._hooks[e].length&&i(this._hooks[e]).forEach((function(e){try{e(t)}catch(e){}}))},e.prototype.set=function(e){e&&-1===this._cache.indexOf(e)&&this._cache.push(e)},e}(),l=function(e){return null!=e&&"[object Object]"==Object.prototype.toString.call(e)},h=function(e){return Array.isArray(e)},f=(r=+Date.now()+Number((""+Math.random()).slice(2,8)),function(){return r+=1}),d=function(e){return function(e,t,n){if("string"==typeof e&&"number"==typeof n){var i,r=[];n=n<=25?n:n%25;var o=String.fromCharCode(n+97);i=e.split(o);for(var a=0;a<i.length;a++){var s=parseInt(i[a],n);s=1*s^64;var c=String.fromCharCode(s);r.push(c)}return r.join("")}}(e,0,25)},p=function(){return!!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)},g=function(e){p()?window.addEventListener("pagehide",e,!1):window.addEventListener("beforeunload",e,!1)},_=function(){var e=0;return["hidden","msHidden","webkitHidden"].forEach((function(t){void 0!==document[t]&&(e=1)})),e},v=function(e){var t=document.createElement("a");return t.href=e,t},m=function(e){var t={};try{var n=v(e).search;if(!n)return t;(n=n.slice(1)).split("&").forEach((function(e){var n,i,r=e.split("=");r.length&&(n=r[0],i=r[1]);try{t[n]=decodeURIComponent(void 0===i?"":i)}catch(e){t[n]=i}}))}catch(e){}return t},y=function(e){for(var t=0,n=0,i=(e+="").length,r=0;r<i;r++)((t=31*t+e.charCodeAt(n++))>0x7fffffffffff||t<-0x800000000000)&&(t&=0xffffffffffff);return t<0&&(t+=0x7ffffffffffff),t},b=(function(e){var t;t=function(){function e(){for(var e=0,t={};e<arguments.length;e++){var n=arguments[e];for(var i in n)t[i]=n[i]}return t}function t(e){return e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}return function n(i){function r(){}function o(t,n,o){if("undefined"!=typeof document){"number"==typeof(o=e({path:"/"},r.defaults,o)).expires&&(o.expires=new Date(1*new Date+864e5*o.expires)),o.expires=o.expires?o.expires.toUTCString():"";try{var a=JSON.stringify(n);/^[\{\[]/.test(a)&&(n=a)}catch(e){}n=i.write?i.write(n,t):encodeURIComponent(String(n)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),t=encodeURIComponent(String(t)).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/[\(\)]/g,escape);var s="";for(var c in o)o[c]&&(s+="; "+c,!0!==o[c]&&(s+="="+o[c].split(";")[0]));return document.cookie=t+"="+n+s}}function a(e,n){if("undefined"!=typeof document){for(var r={},o=document.cookie?document.cookie.split("; "):[],a=0;a<o.length;a++){var s=o[a].split("="),c=s.slice(1).join("=");n||'"'!==c.charAt(0)||(c=c.slice(1,-1));try{var u=t(s[0]);if(c=(i.read||i)(c,u)||t(c),n)try{c=JSON.parse(c)}catch(e){}if(r[u]=c,e===u)break}catch(e){}}return e?r[e]:r}}return r.set=o,r.get=function(e){return a(e,!1)},r.getJSON=function(e){return a(e,!0)},r.remove=function(t,n){o(t,"",e(n,{expires:-1}))},r.defaults={},r.withConverter=n,r}((function(){}))},e.exports=t()}(o={exports:{}}),o.exports),z=function(e,t){try{return e?b.get(e):b.get()}catch(e){return""}},w=function(e,t,n,i){try{var r=i||document.domain,o=+new Date+(n||6048e5);b.set(e,t,{expires:new Date(o),path:"/",domain:r})}catch(e){}},E=function(){function e(){this.cache={}}return e.prototype.setItem=function(e,t){this.cache[e]=t},e.prototype.getItem=function(e){return this.cache[e]},e.prototype.removeItem=function(e){this.cache[e]=void 0},e.prototype.getCookie=function(e,t){return z(e)},e.prototype.setCookie=function(e,t,n,i){w(e,t,n,i)},e}(),S={getItem:function(e){try{var t=localStorage.getItem(e),n=t;try{t&&"string"==typeof t&&(n=JSON.parse(t))}catch(e){}return n||{}}catch(e){}return{}},setItem:function(e,t){try{var n="string"==typeof t?t:JSON.stringify(t);localStorage.setItem(e,n)}catch(e){}},removeItem:function(e){try{localStorage.removeItem(e)}catch(e){}},getCookie:function(e,t){return z(e)},setCookie:function(e,t,n,i){w(e,t,n,i)},isSupportLS:function(){try{return localStorage.setItem("_ranger-test-key","hi"),localStorage.getItem("_ranger-test-key"),localStorage.removeItem("_ranger-test-key"),!0}catch(e){return!1}}()},k={getItem:function(e){try{var t=sessionStorage.getItem(e),n=t;try{t&&"string"==typeof t&&(n=JSON.parse(t))}catch(e){}return n||{}}catch(e){}return{}},setItem:function(e,t){try{var n="string"==typeof t?t:JSON.stringify(t);sessionStorage.setItem(e,n)}catch(e){}},removeItem:function(e){try{sessionStorage.removeItem(e)}catch(e){}},getCookie:function(e,t){return z(e)},setCookie:function(e,t,n,i){w(e,t,n,i)},isSupportSession:function(){try{return sessionStorage.setItem("_ranger-test-key","hi"),sessionStorage.getItem("_ranger-test-key"),sessionStorage.removeItem("_ranger-test-key"),!0}catch(e){return!1}}()},x=function(){function e(e,t){this._storage=t&&"session"===t?k:!e&&S.isSupportLS?S:new E}return e.prototype.getItem=function(e){return this._storage.getItem(e)},e.prototype.setItem=function(e,t){this._storage.setItem(e,t)},e.prototype.getCookie=function(e,t){return this._storage.getCookie(e,t)},e.prototype.setCookie=function(e,t,n,i){this._storage.setCookie(e,t,n,i)},e.prototype.removeItem=function(e){this._storage.removeItem(e)},e}(),D=function(){function e(e,t,n){this.appid=e,this.domain=t,this.userAgent=window.navigator.userAgent,this.appVersion=window.navigator.appVersion,this.cookie_expire=n}return e.prototype.init=function(){var e,t=window.navigator.userAgent,n=window.navigator.language,i=document.referrer,r=i?v(i).hostname:"",o=m(window.location.href),a=/Mobile|htc|mini|Android|iP(ad|od|hone)/.test(this.appVersion)?"wap":"web",s=(null===(e=window.navigator.connection)||void 0===e?void 0:e.effectiveType)||"";this.utm=function(e,t,n,i){var r=new x(!1),o=new x(!1,"session"),a=e?"_tea_utm_cache_"+e:"_tea_utm_cache",s=e?"_$utm_from_url_"+e:"_$utm_from_url",c={},u=["tr_shareuser","tr_admaster","tr_param1","tr_param2","tr_param3","tr_param4","$utm_from_url"],l={ad_id:Number(t.ad_id)||void 0,campaign_id:Number(t.campaign_id)||void 0,creative_id:Number(t.creative_id)||void 0,utm_source:t.utm_source,utm_medium:t.utm_medium,utm_campaign:t.utm_campaign,utm_term:t.utm_term,utm_content:t.utm_content,tr_shareuser:t.tr_shareuser,tr_admaster:t.tr_admaster,tr_param1:t.tr_param1,tr_param2:t.tr_param2,tr_param3:t.tr_param3,tr_param4:t.tr_param4};try{var h=!1;for(var f in l)l[f]&&(-1!==u.indexOf(f)?(c.hasOwnProperty("tracer_data")||(c.tracer_data={}),c.tracer_data[f]=l[f]):c[f]=l[f],h=!0);if(h)o.setItem(s,"1"),r.setCookie(a,JSON.stringify(c),i,n);else{var d=r.getCookie(a,n);d&&(c=JSON.parse(d))}o.getItem(s)&&(c.hasOwnProperty("tracer_data")||(c.tracer_data={}),c.tracer_data.$utm_from_url=1)}catch(e){return l}return c}(this.appid,o,this.domain,this.cookie_expire);var c=this.browser(),u=this.os();return{browser:c.browser,browser_version:c.browser_version,platform:a,os_name:u.os_name,os_version:u.os_version,userAgent:t,screen_width:window.screen&&window.screen.width,screen_height:window.screen&&window.screen.height,device_model:this.getDeviceModel(u.os_name),language:n,referrer:i,referrer_host:r,network_type:s,utm:this.utm,latest_data:this.last(i,r)}},e.prototype.last=function(e,t){var n="",i="",r="",o=location.hostname,a=!1;if(e&&t&&o!==t){n=e,i=t,a=!0;var s=m(e);s.keyword&&(r=s.keyword)}return{$latest_referrer:n,$latest_referrer_host:i,$latest_search_keyword:r,isLast:a}},e.prototype.browser=function(){var e,t,n="",i=""+parseFloat(this.appVersion),r=this.userAgent;return-1!==r.indexOf("Edge")||-1!==r.indexOf("Edg")?(n="Microsoft Edge",-1!==r.indexOf("Edge")?(e=r.indexOf("Edge"),i=r.substring(e+5)):(e=r.indexOf("Edg"),i=r.substring(e+4))):-1!==(e=r.indexOf("MSIE"))?(n="Microsoft Internet Explorer",i=r.substring(e+5)):-1!==(e=r.indexOf("MetaSr"))?(n="sougoubrowser",i=r.substring(e+7,e+10)):-1!==r.indexOf("MQQBrowser")||-1!==r.indexOf("QQBrowser")?(n="qqbrowser",-1!==r.indexOf("MQQBrowser")?(e=r.indexOf("MQQBrowser"),i=r.substring(e+11,e+15)):-1!==r.indexOf("QQBrowser")&&(e=r.indexOf("QQBrowser"),i=r.substring(e+10,e+25))):-1!==r.indexOf("Chrome")?-1!==(e=r.indexOf("MicroMessenger"))?(n="weixin",i=r.substring(e+15,e+20)):-1!==(e=r.indexOf("360"))?(n="360browser",i=r.substring(r.indexOf("Chrome")+7)):-1!==r.indexOf("baidubrowser")||-1!==r.indexOf("BIDUBrowser")?(-1!==r.indexOf("baidubrowser")?(e=r.indexOf("baidubrowser"),i=r.substring(e+13,e+16)):-1!==r.indexOf("BIDUBrowser")&&(e=r.indexOf("BIDUBrowser"),i=r.substring(e+12,e+15)),n="baidubrowser"):-1!==(e=r.indexOf("xiaomi"))?-1!==r.indexOf("openlanguagexiaomi")?(n="openlanguage xiaomi",i=r.substring(e+7,e+13)):(n="xiaomi",i=r.substring(e-7,e-1)):-1!==(e=r.indexOf("TTWebView"))?(n="TTWebView",i=r.substring(e+10,e+23)):(-1!==(e=r.indexOf("Chrome"))||-1!==(e=r.indexOf("Chrome")))&&(n="Chrome",i=r.substring(e+7)):-1!==r.indexOf("Safari")?-1!==(e=r.indexOf("QQ"))?(n="qqbrowser",i=r.substring(e+10,e+16)):-1!==(e=r.indexOf("Safari"))&&(n="Safari",i=r.substring(e+7),-1!==(e=r.indexOf("Version"))&&(i=r.substring(e+8))):-1!==(e=r.indexOf("Firefox"))?(n="Firefox",i=r.substring(e+8)):-1!==(e=r.indexOf("MicroMessenger"))?(n="weixin",i=r.substring(e+15,e+20)):-1!==(e=r.indexOf("QQ"))&&(n="qqbrowser",i=r.substring(e+3,e+8)),-1!==(t=i.indexOf(";"))&&(i=i.substring(0,t)),-1!==(t=i.indexOf(" "))&&(i=i.substring(0,t)),-1!==(t=i.indexOf(")"))&&(i=i.substring(0,t)),{browser:n,browser_version:i}},e.prototype.os=function(){for(var e="",t="",n=[{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0|Windows NT 10.1)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Windows",r:/(Windows|Windows NT)/},{s:"Android",r:/Android/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},{s:"chromeOS",r:/CrOS/},{s:"Linux",r:/(Linux|X11)/},{s:"Sun OS",r:/SunOS/}],i=0;i<n.length;i++){var r=n[i];if(r.r.test(this.userAgent)){"Mac OS X"===(e=r.s)&&this.isNewIpad()&&(e="iOS");break}}var o,a,s=function(e,t){var n=e.exec(t);return n&&n[1]?n[1]:""},c=function(e,t){var n=RegExp("(?:^|[^A-Z0-9-_]|[^A-Z0-9-]_|sprd-)(?:"+e+")","i").exec(t);return n?n.slice(1)[0]:""};switch(/Windows/.test(e)&&(t=s(/Windows (.*)/,e),e="windows"),e){case"Mac OS X":t=c("Mac[ +]OS[ +]X(?:[ /](?:Version )?(\\d+(?:[_\\.]\\d+)+))?",this.userAgent),e="mac";break;case"Android":(a=s(/Android ([\.\_\d]+)/,o=this.userAgent))||(a=s(/Android\/([\.\_\d]+)/,o)),t=a,e="android";break;case"iOS":t=this.isNewIpad()?c("Mac[ +]OS[ +]X(?:[ /](?:Version )?(\\d+(?:[_\\.]\\d+)+))?",this.userAgent):(t=/OS (\d+)_(\d+)_?(\d+)?/.exec(this.appVersion))?t[1]+"."+t[2]+"."+(0|t[3]):"",e="ios";break;case"chromeOS":var u=this.userAgent.indexOf("x86_64");t=this.userAgent.substring(u+7,u+16)}return{os_name:e,os_version:t}},e.prototype.getDeviceModel=function(e){var t="";try{if("android"===e)navigator.userAgent.split(";").forEach((function(e){e.indexOf("Build/")>-1&&(t=e.slice(0,e.indexOf("Build/")))}));else if("ios"===e||"mac"===e||"windows"===e)if(this.isNewIpad())t="iPad";else{var n=navigator.userAgent.replace("Mozilla/5.0 (",""),i=n.indexOf(";");t=-1===i?"":n.slice(0,i)}}catch(e){return t.trim()}return t.trim()},e.prototype.isNewIpad=function(){return void 0!==this.userAgent&&"MacIntel"===navigator.platform&&"number"==typeof navigator.maxTouchPoints&&navigator.maxTouchPoints>1},e}(),A={cn:"1fz22z22z1nz21z4mz4bz4bz22z1mz19z1jz1mz1ez4az1az22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k",va:"1fz22z22z1nz21z4mz4bz4bz22z1mz19z1jz1mz1ez4az1gz22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k",sg:"1fz22z22z1nz21z4mz4bz4bz22z1mz19z1jz1mz1ez4az22z1mz19z21z1lz21z21z1bz1iz4az1az1mz1k"},T={cn:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z4az28z1gz1hz1gz1cz18z1nz1gz4az1az1mz1k",sg:"1fz22z22z1nz21z4mz4bz4bz21z1ez18z1jz1gz49z1kz1az21z4az19z27z22z1cz1mz24z1cz20z21z1cz18z4az1az1mz1k",va:"1fz22z22z1nz21z4mz4bz4bz1kz18z1jz1gz24z18z49z1kz1az21z4az19z27z22z1cz1mz24z1cz20z21z1cz18z4az1az1mz1k",my:"1fz22z22z1nz21z4mz4bz4bz1kz1az21z49z19z1bz49z1kz27z4az1jz18z20z1iz1mz1dz1dz1gz1az1cz4az1az1mz1k"},R="5.3.1",G=(d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z1az1bz1lz49z22z1mz21z4az19z27z22z1cz21z1az1kz4az1az1mz1kz4bz1mz19z1hz4bz21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz24z1gz21z23z18z1jz49z1cz1bz1gz22z1mz20z49z20z18z1lz1ez1cz20z21z4az1hz21"),d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z1az1bz1lz49z22z1mz21z4az19z27z22z1cz21z1az1kz4az1az1mz1kz4bz1mz19z1hz4bz21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz24z1gz21z23z18z1jz49z18z19z49z1az1mz20z1cz4az1hz21"),d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z1az1bz1lz49z22z1mz21z4az19z27z22z1cz21z1az1kz4az1az1mz1kz4bz1mz19z1hz4bz21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz24z1gz21z23z18z1jz49z18z19z49z1jz1mz18z1bz1cz20z4az1hz21"),d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z1az1bz1lz49z22z1mz21z4az19z27z22z1cz21z1az1kz4az1az1mz1kz4bz1mz19z1hz4bz21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz1fz1cz18z22z1kz18z1nz49z1az1mz20z1c")),O=(d("1fz22z22z1nz21z4mz4bz4bz1jz1dz4fz49z1bz18z22z18z4az24z1mz1jz1az1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz1bz18z22z18z49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz22z1cz21z22z1cz20z49z1cz24z1cz1lz22z49z1gz1lz21z1nz1cz1az22z1mz20"),function(){function e(e,t){this.collector=e,this.config=t,this.eventNameWhiteList=["__bav_page","__bav_beat","__bav_page_statistics","__bav_click","__bav_page_exposure","__bav_slide"],this.paramsNameWhiteList=["$inactive","$inline","$target_uuid_list","$source_uuid","$is_spider","$source_id","$is_first_time","_staging_flag"],this.regStr=new RegExp("^[a-zA-Z0-9][a-z0-9A-Z_.-]{1,255}$")}return e.prototype.checkVerify=function(e){var t=this;if(!e||!e.length)return!1;var n=e[0];if(!n)return!1;var i=n.events,r=n.header;if(!i||!i.length)return!1;var o=!0;return i.forEach((function(e){if(!e)return o=!1,void(e.checkEvent="事件异常");t.checkEventName(e.event)||(o=!1,e.checkEvent="事件名不能以 $ or __开头"),t.checkEventParams(e.params)||(o=!1,e.checkParams="属性名不能以 $ or __开头")})),this.checkEventParams(r)||(o=!1),o},e.prototype.checkEventName=function(e){return!!e&&this.calculate(e,"event")},e.prototype.checkEventParams=function(e){var t=e;if("string"==typeof e&&(t=JSON.parse(t)),!Object.keys(t).length)return!0;for(var n in t)return!(!this.calculate(n,"params")||"string"==typeof t[n]&&t[n].length>1024&&(console.warn("params: "+n+" can not over 1024 byte, please check;"),1));return!0},e.prototype.calculate=function(e,t){return-1!==("event"===t?this.eventNameWhiteList:this.paramsNameWhiteList).indexOf(e)||!new RegExp("^\\$").test(e)&&!new RegExp("^__").test(e)||(console.warn(("event"===t?"event":"params")+" name: "+e+" can not start with $ or __, pleace check;"),!1)},e}());(c=a||(a={})).Init="init",c.Config="config",c.Start="start",c.Ready="ready",c.UnReady="un-ready",c.TokenComplete="token-complete",c.TokenStorage="token-storage",c.TokenFetch="token-fetch",c.TokenError="token-error",c.ConfigUuid="config-uuid",c.ConfigWebId="config-webid",c.ConfigDiD="config-device-id",c.ConfigDomain="config-domain",c.CustomWebId="custom-webid",c.TokenChange="token-change",c.TokenReset="token-reset",c.ConfigTransform="config-transform",c.EnvTransform="env-transform",c.SessionReset="session-reset",c.SessionResetTime="session-reset-time",c.SetSessionId="set-session-id",c.Event="event",c.Events="events",c.EventNow="event-now",c.CleanEvents="clean-events",c.BeconEvent="becon-event",c.SubmitBefore="submit-before",c.SubmitScuess="submit-scuess",c.SubmitAfter="submit-after",c.SubmitError="submit-error",c.SubmitVerify="submit-verify",c.CustomHeader="custom-request-header",c.SDKInnerError="sdk-inner-error",c.SDKRunTimeError="sdk-run-time-error",c.LogSetting="log-setting",c.LogSettingReady="log-setting-ready",c.Stay="stay",c.ResetStay="reset-stay",c.StayReady="stay-ready",c.SetStay="set-stay",c.RouteChange="route-change",c.RouteReady="route-ready",c.Ab="ab",c.AbVar="ab-var",c.AbAllVars="ab-all-vars",c.AbConfig="ab-config",c.AbExternalVersion="ab-external-version",c.AbVersionChangeOn="ab-version-change-on",c.AbVersionChangeOff="ab-version-change-off",c.AbOpenLayer="ab-open-layer",c.AbCloseLayer="ab-close-layer",c.AbReady="ab-ready",c.AbComplete="ab-complete",c.AbTimeout="ab-timeout",c.Profile="profile",c.ProfileSet="profile-set",c.ProfileSetOnce="profile-set-once",c.ProfileUnset="profile-unset",c.ProfileIncrement="profile-increment",c.ProfileAppend="profile-append",c.ProfileClear="profile-clear",c.Autotrack="autotrack",c.AutotrackReady="autotrack-ready",c.CepReady="cep-ready",c.TracerReady="tracer-ready",c.sessionRecord="session-record",c.SessionRecordStart="session-record-start",c.SessionRecordPause="session-record-pause",c.SessionRecordEnd="session-record-end",c.SessionRecordReport="session-record-report",c.DestoryInstance="destory-instance",c.VisualCollectReady="visual-collect-ready",c.VisualApiReady="visual-api-ready",c.VisualApiUpdate="visual-api-update",function(e){e.DEBUGGER_MESSAGE="debugger-message",e.DEBUGGER_MESSAGE_SDK="debugger-message-sdk",e.DEBUGGER_MESSAGE_FETCH="debugger-message-fetch",e.DEBUGGER_MESSAGE_FETCH_RESULT="debugger-message-fetch-result",e.DEBUGGER_MESSAGE_EVENT="debugger-message-event",e.DEVTOOL_WEB_READY="devtool-web-ready"}(s||(s={}));var M=a,C=void 0,I=(new Date).getTimezoneOffset(),B=parseInt(""+-I/60,10),U=60*I,L=function(){function e(e,t){var n=this;this.is_first_time=!0,this.configPersist=!1,this.initConfig=t,this.collect=e;var i=new D(t.app_id,t.cookie_domain||"",t.cookie_expire||7776e6).init();this.eventCheck=new O(e,t);var r="__tea_cache_first_"+t.app_id;this.configKey="__tea_cache_config_"+t.app_id,this.sessionStorage=new x(!1,"session"),this.localStorage=new x(!1,"local"),t.configPersist&&(this.configPersist=!0,this.storage=1===t.configPersist?this.sessionStorage:this.localStorage);var o=this.localStorage.getItem(r);o&&1==o?this.is_first_time=!1:(this.is_first_time=!0,this.localStorage.setItem(r,"1")),this.envInfo={user:{user_unique_id:C,user_type:C,user_id:C,user_is_auth:C,user_is_login:C,device_id:C,web_id:C,ip_addr_id:C,user_unique_id_type:C},header:{app_id:C,app_name:C,app_install_id:C,install_id:C,app_package:C,app_channel:C,app_version:C,ab_version:C,os_name:i.os_name,os_version:i.os_version,device_model:i.device_model,ab_client:C,traffic_type:C,client_ip:C,device_brand:C,os_api:C,access:C,language:i.language,region:C,app_language:C,app_region:C,creative_id:i.utm.creative_id,ad_id:i.utm.ad_id,campaign_id:i.utm.campaign_id,log_type:C,rnd:C,platform:i.platform,sdk_version:R,sdk_lib:"js",province:C,city:C,timezone:B,tz_offset:U,tz_name:C,sim_region:C,carrier:C,resolution:i.screen_width+"x"+i.screen_height,browser:i.browser,browser_version:i.browser_version,referrer:i.referrer,referrer_host:i.referrer_host,width:i.screen_width,height:i.screen_height,screen_width:i.screen_width,screen_height:i.screen_height,utm_term:i.utm.utm_term,utm_content:i.utm.utm_content,utm_source:i.utm.utm_source,utm_medium:i.utm.utm_medium,utm_campaign:i.utm.utm_campaign,tracer_data:JSON.stringify(i.utm.tracer_data),custom:i.network_type?{network_type:i.network_type}:{},wechat_unionid:C,wechat_openid:C}},this.ab_version="",this.evtParams={},this.reportErrorCallback=function(){},this.isLast=!1,this.setCustom(i),this.initDomain(),this.initABData(),this.collect.on("route-change",(function(e){n.changeReferInfo(e.config)}))}return e.prototype.initDomain=function(){var e=this.initConfig.channel_domain;if(e)this.domain=e;else{var t=this.initConfig.channel;this.domain=d(T[t])}},e.prototype.setDomain=function(e){this.domain=e},e.prototype.getDomain=function(){return this.domain},e.prototype.initABData=function(){var e="__tea_sdk_ab_version_"+this.initConfig.app_id,t=null;if(this.initConfig.ab_cross){var n=this.localStorage.getCookie(e,this.initConfig.ab_cookie_domain);t=n?JSON.parse(n):null}else t=this.localStorage.getItem(e);this.setAbCache(t)},e.prototype.setAbCache=function(e){this.ab_cache=e},e.prototype.getAbCache=function(){return this.ab_cache},e.prototype.setAbVersion=function(e){this.ab_version=e},e.prototype.getAbVersion=function(){return this.ab_version},e.prototype.clearAbCache=function(){this.ab_version="",this.ab_cache={}},e.prototype.getUrl=function(e){var t="";switch(e){case"event":t="/list";break;case"webid":t="/webid";break;case"tobid":t="/tobid"}var n="";return this.initConfig.caller&&(n="?sdk_version=5.3.1&sdk_name=web&app_id="+this.initConfig.app_id+"&caller="+this.initConfig.caller),""+this.getDomain()+t+n},e.prototype.setCustom=function(e){if(e&&e.latest_data&&e.latest_data.isLast)for(var t in delete e.latest_data.isLast,this.isLast=!0,e.latest_data)this.envInfo.header.custom[t]=e.latest_data[t]},e.prototype.changeReferInfo=function(e){var t=e;this.envInfo.header.referrer=t.referrer;var n="";try{n=new URL(t.referrer).host}catch(e){}this.envInfo.header.referrer_host=n||this.envInfo.header.referrer_host},e.prototype.set=function(e){var n=this;Object.keys(e).forEach((function(i){void 0!==e[i]&&null!==e[i]||n.delete(i);try{n.eventCheck.calculate(i,"config")}catch(e){}if("traffic_type"===i&&n.isLast&&(n.envInfo.header.custom.$latest_traffic_source_type=e[i]),"evtParams"===i)n.evtParams=t(t({},n.evtParams||{}),e.evtParams||{});else if("_staging_flag"===i)n.evtParams=t(t({},n.evtParams||{}),{_staging_flag:e._staging_flag});else if("reportErrorCallback"===i&&"function"==typeof e[i])n.reportErrorCallback=e[i];else{var r="",o="";if(i.indexOf(".")>-1){var a=i.split(".");r=a[0],o=a[1]}r?"user"===r||"header"===r?n.envInfo[r][o]=e[i]:(n.envInfo.header.custom[o]=e[i],n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 添加了Custom",level:"info",time:Date.now(),infoType:"sdk",secType:"HEADER",common:"custom"})):Object.hasOwnProperty.call(n.envInfo.user,i)?(["user_type","ip_addr_id"].indexOf(i)>-1?n.envInfo.user[i]=e[i]?Number(e[i]):e[i]:["user_id","web_id","user_unique_id","user_unique_id_type"].indexOf(i)>-1?n.envInfo.user[i]=e[i]?String(e[i]):e[i]:["user_is_auth","user_is_login"].indexOf(i)>-1?n.envInfo.user[i]=Boolean(e[i]):"device_id"===i&&(n.envInfo.user[i]=e[i]),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 修改了公共参数",level:"info",time:Date.now(),infoType:"sdk",secType:"USER",common:i})):Object.hasOwnProperty.call(n.envInfo.header,i)?(n.envInfo.header[i]=e[i],n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 修改了公共参数",level:"info",time:Date.now(),infoType:"sdk",secType:"HEADER",common:i})):(n.envInfo.header.custom[i]=e[i],n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 添加了Custom",level:"info",time:Date.now(),infoType:"sdk",secType:"HEADER",common:"custom"}))}}))},e.prototype.get=function(e){try{return e?"evtParams"===e?this.evtParams:"reportErrorCallback"===e?this[e]:Object.hasOwnProperty.call(this.envInfo.user,e)?this.envInfo.user[e]:Object.hasOwnProperty.call(this.envInfo.header,e)?this.envInfo.header[e]:JSON.parse(JSON.stringify(this.envInfo[e])):JSON.parse(JSON.stringify(this.envInfo))}catch(e){console.log("get config stringify error "),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message})}},e.prototype.setStore=function(e){try{if(!this.configPersist)return;var t=this.storage.getItem(this.configKey)||{};if(t&&Object.keys(e).length){var n=Object.assign(e,t);this.storage.setItem(this.configKey,n)}}catch(e){console.log("setStore error"),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message})}},e.prototype.getStore=function(){try{if(!this.configPersist)return null;var e=this.storage.getItem(this.configKey)||{};return e&&Object.keys(e).length?e:null}catch(e){return this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message}),null}},e.prototype.delete=function(e){try{if(!this.configPersist)return;var t=this.storage.getItem(this.configKey)||{};t&&Object.hasOwnProperty.call(t,e)&&(delete t[e],this.storage.setItem(this.configKey,t))}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message}),console.log("delete error")}},e}(),N=function(){function e(e,t){this.isLog=t||!1,this.name=e||""}return e.prototype.info=function(e){this.isLog&&console.log("%c %s","color: yellow; background-color: black;","[AppLog WEB SDK] [instance: "+this.name+"] "+e)},e.prototype.warn=function(e){this.isLog&&console.warn("[AppLog WEB SDK] [instance: "+this.name+"] "+e)},e.prototype.error=function(e){this.isLog&&console.error("[AppLog WEB SDK] [instance: "+this.name+"] "+e)},e.prototype.throw=function(e){throw this.error(this.name),new Error(e)},e}(),j=function(){function e(){this.spiderBot=["Baiduspider","googlebot","360Spider","haosouspider","YoudaoBot","Sogou News Spider","Yisouspider","Googlebot","Headless","Applebot","Bingbot","PetalBot"]}return e.prototype.checkSpider=function(e){if(!e.enable_spider)return!1;var t=window.navigator.userAgent;if(!t)return!0;var n=!1;return this.spiderBot.forEach((function(e){-1!==t.indexOf(e)&&(n=!0)})),n},e}(),P=function(){function e(e,t){this.collect=e,this.native=t}var t=e.prototype;return t.bridgeInject=function(){try{return!!this.native&&(AppLogBridge?(console.log("AppLogBridge is injected"),!0):(console.log("AppLogBridge is not inject"),!1))}catch(e){return console.log("AppLogBridge is not inject"),!1}},t.bridgeReady=function(){var e=this;return new Promise((function(t,n){try{e.bridgeInject()?AppLogBridge.hasStarted((function(e){console.log("AppLogBridge is started? : "+e),e?t(!0):n(!1)})):n(!1)}catch(e){console.log("AppLogBridge, error:"+JSON.stringify(e.stack)),n(!1)}}))},t.setNativeAppId=function(e){try{AppLogBridge.setNativeAppId(JSON.stringify(e)),console.log("change bridge appid, event report with appid: "+e)}catch(e){console.error("setNativeAppId error")}},t.setConfig=function(e){var t=this;try{Object.keys(e).forEach((function(n){"user_unique_id"===n?t.setUserUniqueId(e[n]):e[n]?t.addHeaderInfo(n,e[n]):t.removeHeaderInfo(n)}))}catch(e){console.error("setConfig error")}},t.setUserUniqueId=function(e){try{AppLogBridge.setUserUniqueId(e)}catch(e){console.error("setUserUniqueId error")}},t.addHeaderInfo=function(e,t){try{AppLogBridge.addHeaderInfo(e,t)}catch(e){console.error("addHeaderInfo error")}},t.setHeaderInfo=function(e){try{AppLogBridge.setHeaderInfo(JSON.stringify(e))}catch(e){console.error("setHeaderInfo error")}},t.removeHeaderInfo=function(e){try{AppLogBridge.removeHeaderInfo(e)}catch(e){console.error("removeHeaderInfo error")}},t.reportPv=function(e){this.onEventV3("predefine_pageview",e)},t.onEventV3=function(e,t){try{AppLogBridge.onEventV3(e,t),this.collect.emit(DebuggerMesssge.DEBUGGER_MESSAGE,{type:DebuggerMesssge.DEBUGGER_MESSAGE_EVENT,info:"bridge埋点上报",time:Date.now(),data:[{events:[{event:e,params:t}]}],code:200,status:"success"})}catch(e){console.error("onEventV3 error")}},t.profileSet=function(e){try{AppLogBridge.profileSet(e)}catch(e){console.error("profileSet error")}},t.profileSetOnce=function(e){try{AppLogBridge.profileSetOnce(e)}catch(e){console.error("profileSetOnce error")}},t.profileIncrement=function(e){try{AppLogBridge.profileIncrement(e)}catch(e){console.error("profileIncrement error")}},t.profileUnset=function(e){try{AppLogBridge.profileUnset(e)}catch(e){console.error("profileUnset error")}},t.profileAppend=function(e){try{AppLogBridge.profileAppend(e)}catch(e){console.error("profileAppend error")}},e}(),K=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},q=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.storage=new x(!1,"session"),this.sessionKey="__tea_session_id_"+t.app_id,this.expireTime=t.expireTime||18e5,this.disableSession=t.disable_session,this.disableSessionTimeCheck=t.disable_session_check,this.disableSession||(this.setSessionId(),this.collect.on(M.SessionReset,(function(e){n.resetSessionId(e)})),this.collect.on(M.SessionResetTime,(function(){n.updateSessionIdTime()})))},e.prototype.updateSessionIdTime=function(){var e=this.storage.getItem(this.sessionKey);if(e&&e.sessionId){var t=e.timestamp;Date.now()-t>this.expireTime?e={sessionId:K(),timestamp:Date.now()}:e.timestamp=Date.now(),this.storage.setItem(this.sessionKey,e),this.resetExpTime()}},e.prototype.setSessionId=function(){var e=this,t=this.storage.getItem(this.sessionKey);t&&t.sessionId?t.timestamp=Date.now():t={sessionId:K(),timestamp:Date.now()},this.storage.setItem(this.sessionKey,t),this.disableSessionTimeCheck||(this.sessionExp=setInterval((function(){e.checkEXp()}),this.expireTime))},e.prototype.getSessionId=function(){var e=this.storage.getItem(this.sessionKey);return this.disableSession?"":e&&e.sessionId?e.sessionId:""},e.prototype.resetExpTime=function(){var e=this;this.sessionExp&&(clearInterval(this.sessionExp),this.sessionExp=setInterval((function(){e.checkEXp()}),this.expireTime))},e.prototype.resetSessionId=function(e){var t={sessionId:e||K(),timestamp:Date.now()};this.storage.setItem(this.sessionKey,t)},e.prototype.checkEXp=function(){var e=this.storage.getItem(this.sessionKey);e&&e.sessionId&&Date.now()-e.timestamp+30>=this.expireTime&&(e={sessionId:K(),timestamp:Date.now()},this.storage.setItem(this.sessionKey,e))},e}(),H=function(){function e(){this.eventLimit=50,this.enable_ttwebid=!1,this.eventCache=[],this.beconEventCache=[]}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.config=t,this.configManager=e.configManager,this.eventCheck=new O(e,t),this.cacheStorgae=new x(!0),this.localStorage=new x(!1),this.maxReport=t.max_report||10,this.reportTime=t.reportTime||t.report_time||30,this.timeout=t.timeout||1e5,this.enable_ttwebid=t.enable_ttwebid,this.reportUrl=t.report_url||this.configManager.getUrl("event"),this.eventKey="__tea_cache_events_"+this.configManager.get("app_id"),this.beconKey="__tea_cache_events_becon_"+this.configManager.get("app_id"),this.abKey="__tea_sdk_ab_version_"+this.configManager.get("app_id"),this.refer_key="__tea_cache_refer_"+this.configManager.get("app_id"),this.pageId=K(),this.collect.on(M.Ready,(function(){n.reportAll(!1)})),this.collect.on(M.ConfigDomain,(function(){n.reportUrl=n.configManager.getUrl("event")})),this.collect.on(M.Event,(function(e){n.event(e)})),this.collect.on(M.BeconEvent,(function(e){n.beconEvent(e)})),this.collect.on(M.CleanEvents,(function(){n.reportAll(!1)}));var i=this.linster();this.collect.on(M.DestoryInstance,(function(){i&&i()}))},e.prototype.linster=function(){var e=this,t=function(){e.reportAll(!0)};window.addEventListener("unload",t,!1),g(t);var n=function(){"hidden"===document.visibilityState&&e.reportAll(!0)};return document.addEventListener("visibilitychange",n,!1),function(){window.removeEventListener("unload",t),window.removeEventListener("pagehide",t,!1),window.removeEventListener("beforeunload",t,!1),document.removeEventListener("visibilitychange",n,!1)}},e.prototype.reportAll=function(e){this.report(e),this.reportBecon()},e.prototype.event=function(e){var t=this;try{if(this.collect.destroy)return;if(this.collect.sdkStop)return;if(this.config.enable_ios_sendbeacon&&p())return void this.beconEvent(e);var n=this.cacheStorgae.getItem(this.eventKey)||[],r=e;this.config.batch_event&&(r=this.batchEvent(e));var o=i(r,n);if(this.cacheStorgae.setItem(this.eventKey,o),this.reportTimeout&&clearTimeout(this.reportTimeout),o.length>=this.maxReport)this.report(!1);else{var a=this.reportTime;this.reportTimeout=setTimeout((function(){t.report(!1),t.reportTimeout=null}),a)}}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 发生了异常",level:"error",time:Date.now(),data:e.message,infoType:"sdk"})}},e.prototype.beconEvent=function(e){if(!this.collect.destroy&&!this.collect.sdkStop){var t=i(e,this.cacheStorgae.getItem(this.beconKey)||[]);this.cacheStorgae.setItem(this.beconKey,t),this.collect.tokenManager.getReady()&&this.collect.sdkReady&&(this.cacheStorgae.removeItem(this.beconKey),this.send(this.split(this.merge(t)),!0))}},e.prototype.reportBecon=function(){if(!this.collect.destroy&&!this.collect.sdkStop&&this.collect.tokenManager.getReady()&&this.collect.sdkReady){var e=this.cacheStorgae.getItem(this.beconKey)||[];e&&e.length&&(this.cacheStorgae.removeItem(this.beconKey),this.send(this.split(this.merge(e)),!0))}},e.prototype.report=function(e){if(!this.collect.destroy&&!this.collect.sdkStop&&this.collect.tokenManager.getReady()&&this.collect.sdkReady){var t=this.cacheStorgae.getItem(this.eventKey)||[];t.length&&(this.cacheStorgae.removeItem(this.eventKey),this.config.batch_event?this.send([t],e):this.sliceEvent(t,e))}},e.prototype.batchEvent=function(e){var t=[];if(e.length>this.eventLimit)for(var n=0;n<e.length;n+=this.eventLimit){var i;i=e.slice(n,n+this.eventLimit);var r=this.merge(i);t.push(r)}else t=r=this.merge(e);return t},e.prototype.sliceEvent=function(e,t){if(e.length>this.eventLimit)for(var n=0;n<e.length;n+=this.eventLimit){var i;i=e.slice(n,n+this.eventLimit);var r=this.split(this.merge(i));this.send(r,t)}else r=this.split(this.merge(e)),this.send(r,t)},e.prototype.handleRefer=function(){var e="";try{if(this.config.spa||this.config.autotrack){var t=this.localStorage.getItem(this.refer_key)||{};e=t.routeChange?t.refer_key:this.configManager.get("referrer")}else e=this.configManager.get("referrer")}catch(t){e=document.referrer}return e},e.prototype.merge=function(e,n){var i=this,r=this.configManager.get(),o=r.header,a=r.user;this.config.enable_pageid&&(o.custom.page_id=this.pageId),o.custom=JSON.stringify(o.custom);var c=this.configManager.get("evtParams"),u=this.configManager.get("user_unique_id_type"),l=[];try{var h=e.filter((function(e){try{Object.keys(c).length&&!n&&(e.params=t(t({},c),e.params)),u&&(e.params.$user_unique_id_type=u);var r=i.configManager.getAbCache(),o=a[i.config.ab_user_mode]||a.user_unique_id;return r&&r.uuid&&r.uuid===o&&i.configManager.getAbVersion()&&(e.ab_sdk_version=i.configManager.getAbVersion()),e.session_id=i.collect.sessionManager.getSessionId(),e.params=JSON.stringify(e.params),e}catch(t){return console.warn("filter merge event: "+(e&&e.event)+" error, pls check",t&&t.message),i.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 处理Event数据异常",level:"error",time:Date.now(),data:t.message,infoType:"sdk"}),!1}})),f=JSON.parse(JSON.stringify({events:h,user:a,header:o}));f.local_time=Math.floor(Date.now()/1e3),f.user_unique_type=this.config.enable_ttwebid?this.config.user_unique_type:void 0,f.verbose=1,l.push(f)}catch(e){console.warn("event merge error, pls check",e&&e.message)}return l},e.prototype.split=function(e){return e.map((function(e){var t=[];return t.push(e),t}))},e.prototype.blockEvent=function(e){try{var t=this.blockList;if(this.config.enable_logsetting){var n=this.blockList,i=this.logSetting.getSettingData("blocklist_events").block_event;t=n.concat(i.filter((function(e){return-1===n.indexOf(e)})))}return!(t&&t.length&&t.includes(e))}catch(e){return!0}},e.prototype.whiteEvent=function(e){var t;try{return null===(t=this.whiteList)||void 0===t||!t.length||!!this.whiteList.includes(e)}catch(e){return!0}},e.prototype.customAllowFilter=function(e,t){try{if(t)return JSON.stringify(e);if(!this.config.enable_logsetting||!this.config.enable_logsetting_header_custom)return JSON.stringify(e);var n=this.logSetting.getSettingData().header_custom_allow,i={};if(n&&n.length&&Object.keys(e).length){var r=new Set(n);for(var o in e)r.has(o)&&(i[o]=e[o])}return JSON.stringify(i)}catch(t){return this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行customAllowFilter发生了异常",level:"error",time:Date.now(),data:t.message,infoType:"sdk"}),JSON.stringify(e)}},e.prototype.paramsAllowFilter=function(e,t){try{if(t)return JSON.stringify(e.params);if(!this.config.enable_logsetting||!this.config.enable_logsetting_params)return JSON.stringify(e.params);var n=this.logSetting.getSettingData().params_block,i=e.event,r=e.params,o={};if(n&&n[i]&&n[i].length){var a=new Set(n[i]);for(var c in r)!this.logSetting.getPredefineParamsList().has(c)&&a.has(c)||(o[c]=r[c]);return JSON.stringify(o)}return JSON.stringify(e.params)}catch(t){return this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行paramsAllowFilter发生了异常",level:"error",time:Date.now(),data:t.message,infoType:"sdk"}),JSON.stringify(e.params)}},e.prototype.send=function(e,t){var n=this;e.length&&(this.config.disable_track_event||e.forEach((function(e){try{var i=JSON.parse(JSON.stringify(e));n.config.filter&&((i=n.config.filter(i))||console.warn("filter must return data !!")),n.collect.eventFilter&&i&&((i=n.collect.eventFilter(i))||console.warn("filterEvent api must return data !!"));var r=i||e,o=JSON.parse(JSON.stringify(r));if(n.eventCheck.checkVerify(o),!r.length)return;var a=!0;if(r.forEach((function(e){e.events.length||(a=!1)})),!a)return;n.collect.emit(M.SubmitBefore,r),n.collect.requestManager.useRequest({url:n.reportUrl,data:r,success:function(e,t){e&&0!==e.e?(n.collect.emit(M.SubmitError,{type:"f_data",eventData:t,errorCode:e.e,response:e}),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_EVENT,info:"埋点上报失败",time:Date.now(),data:o,code:e.e,msg:e.m||"",failType:"数据异常失败",status:"fail"})):(n.collect.emit(M.SubmitScuess,{eventData:t,res:e}),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_EVENT,info:"埋点上报成功",time:Date.now(),data:o,code:200,status:"success"}))},fail:function(e,t){n.configManager.get("reportErrorCallback")(e,t),n.collect.emit(M.SubmitError,{type:"f_net",eventData:e,errorCode:t}),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_EVENT,info:"埋点上报网络异常",time:Date.now(),data:o,code:t,failType:"网络异常失败",status:"fail"})},timeout:n.timeout,useBeacon:t,withCredentials:n.enable_ttwebid,zip:!0}),n.collect.emit(M.SubmitAfter,r)}catch(e){console.warn("something error, "+JSON.stringify(e.stack)),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK发送埋点发生了异常",level:"error",time:Date.now(),data:e.message,infoType:"sdk"})}})))},e}(),V=function(){return function e(t){return t?(t^16*Math.random()>>t/4).toString(10):([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,e)}().replace(/-/g,"").slice(0,19)},F=function(){function e(){this.cacheToken={},this.enableCookie=!1,this.enable_ttwebid=!1,this.enableCustomWebid=!1}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.config=t,this.configManager=this.collect.configManager,this.storage=new x(!1),this.tokenKey="__tea_cache_tokens_"+t.app_id,this.enable_ttwebid=t.enable_ttwebid,this.enableCustomWebid=t.enable_custom_webid,this.collect.on(M.ConfigUuid,(function(e){n.setUuid(e)})),this.collect.on(M.ConfigWebId,(function(e){n.setWebId(e)})),this.collect.on(M.ConfigDiD,(function(e){n.setDeviceId(e)})),this.enableCookie=t.cross_subdomain,this.expiresTime=t.cookie_expire||6048e5,this.cookieDomain=t.cookie_domain||"",this.checkStorage()},e.prototype.checkStorage=function(){var e=this;if(this.enableCookie){var t=this.storage.getCookie(this.tokenKey,this.cookieDomain);this.cacheToken=t&&"string"==typeof t?JSON.parse(t):{}}else this.cacheToken=this.storage.getItem(this.tokenKey)||{};this.tokenType=this.cacheToken&&this.cacheToken._type_?this.cacheToken._type_:"default","custom"!==this.tokenType||this.enableCustomWebid?this.enableCustomWebid?this.collect.on(M.CustomWebId,(function(){e.tokenReady=!0,e.collect.emit(M.TokenComplete)})):this.checkEnv()||(this.enable_ttwebid?this.completeTtWid(this.cacheToken):this.check()):this.remoteWebid()},e.prototype.check=function(){this.cacheToken&&this.cacheToken.web_id?this.complete(this.cacheToken):this.config.disable_webid?this.complete({web_id:V(),user_unique_id:this.configManager.get("user_unique_id")||V()}):this.remoteWebid()},e.prototype.checkEnv=function(){var e=window.navigator.userAgent;if(-1!==e.indexOf("miniProgram")||-1!==e.indexOf("MiniProgram")){var t=m(window.location.href);return!(!t||!t.Web_ID||(this.complete({web_id:""+t.Web_ID,user_unique_id:this.configManager.get("user_unique_id")||""+t.Web_ID}),0))}return!1},e.prototype.remoteWebid=function(){var e=this,t=this.configManager.getUrl("webid"),n={app_key:this.config.app_key,app_id:this.config.app_id,url:location.href,user_agent:window.navigator.userAgent,referer:document.referrer,user_unique_id:""};this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 发起WebID请求",logType:"fetch",level:"info",time:Date.now(),data:n,infoType:"sdk"}),this.collect.requestManager.useRequest({url:t,data:n,success:function(t){var n;if(t&&0===t.e)n=t.web_id,e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"WebID请求成功",logType:"fetch",level:"info",time:Date.now(),data:t,infoType:"sdk"});else{var i=V();n=i,e.collect.configManager.set({localWebId:i}),e.collect.emit(M.TokenError),e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"WebID请求返回值异常，采用本地生成",logType:"fetch",level:"warn",time:Date.now(),data:t,infoType:"sdk"}),e.collect.logger.warn("appid: "+e.config.app_id+" get webid error, use local webid~")}e.complete({web_id:e.configManager.get("web_id")||n,user_unique_id:e.configManager.get("user_unique_id")||n})},fail:function(){var t=V();e.complete({web_id:e.configManager.get("web_id")||t,user_unique_id:e.configManager.get("user_unique_id")||t}),e.collect.configManager.set({localWebId:t}),e.collect.emit(M.TokenError),e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"WebID请求网络异常，采用本地生成",logType:"fetch",level:"error",time:Date.now(),data:{localWebId:t},infoType:"sdk"}),e.collect.logger.warn("appid: "+e.config.app_id+", get webid error, use local webid~")},timeout:3e5})},e.prototype.complete=function(e){var n=e.web_id,i=e.user_unique_id,r=e.device_id;if(e.timestamp=Date.now(),this.collect.configManager.set({web_id:n,user_unique_id:i}),this.config.enable_device_id){var o=this.configManager.get("device_id")||r||n;this.collect.configManager.set({device_id:o}),e=t(t({},e),{device_id:o})}else(null==e?void 0:e.device_id)&&delete e.device_id;this.setStorage(e),this.tokenReady=!0,this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 完成用户信息设置",logType:"info",level:"info",time:Date.now(),data:{web_id:n,user_unique_id:i},infoType:"sdk"}),this.collect.emit(M.TokenComplete)},e.prototype.completeTtWid=function(e){var t=e.user_unique_id||"",n=this.configManager.get("user_unique_id");(n||t)&&this.configManager.set({user_unique_id:n||t}),this.setStorage(e),this.tokenReady=!0,this.collect.emit(M.TokenComplete)},e.prototype.setUuid=function(e){if(e&&-1===["null","undefined","Null","None"].indexOf(e)){var t=String(e),n=this.configManager.get("user_unique_id"),i=this.cacheToken&&this.cacheToken.user_unique_id;if(t===n&&t===i)return;this.configManager.set({user_unique_id:t}),this.cacheToken||(this.cacheToken={}),this.cacheToken.user_unique_id=t,this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken),this.collect.emit(M.TokenChange,"uuid"),this.collect.emit(M.SessionReset)}else this.clearUuid()},e.prototype.clearUuid=function(){this.config.enable_ttwebid||this.configManager.get("web_id")&&this.configManager.get("user_unique_id")!==this.configManager.get("web_id")&&(this.configManager.set({user_unique_id:this.configManager.get("web_id")}),this.cacheToken&&this.cacheToken.web_id&&(this.cacheToken.user_unique_id=this.cacheToken.web_id,this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken)),this.collect.emit(M.TokenReset,"uuid"))},e.prototype.setWebId=function(e){if(e&&!this.config.enable_ttwebid){this.cacheToken&&this.cacheToken.web_id?this.cacheToken.web_id!==e&&(this.cacheToken.user_unique_id=this.cacheToken.web_id===this.cacheToken.user_unique_id?e:this.cacheToken.user_unique_id,this.cacheToken.web_id=e):(this.cacheToken={},this.cacheToken.web_id=e,this.cacheToken.user_unique_id=e),this.cacheToken.timestamp=Date.now();var t=this.configManager.get("web_id"),n=this.configManager.get("user_unique_id");n&&n!==t||(this.configManager.set({user_unique_id:e}),this.collect.emit(M.TokenChange,"uuid")),t!==e&&(this.configManager.set({web_id:e}),this.collect.emit(M.TokenChange,"webid")),this.setStorage(this.cacheToken)}},e.prototype.setDeviceId=function(e){this.configManager.set({device_id:e}),this.config.enable_device_id&&this.cacheToken&&(this.cacheToken.device_id=e,this.cacheToken.timestamp=Date.now(),this.setStorage(this.cacheToken))},e.prototype.setStorage=function(e){e._type_=this.enableCustomWebid?"custom":"default",delete e["diss".split("").reverse().join("")],this.enableCookie||this.enable_ttwebid?(this.storage.setCookie(this.tokenKey,e,this.expiresTime,this.cookieDomain),this.enable_ttwebid&&(delete e.web_id,this.storage.setItem(this.tokenKey,e))):this.storage.setItem(this.tokenKey,e),this.cacheToken=e},e.prototype.getReady=function(){return this.tokenReady},e.prototype.getTobId=function(){var e=this;return new Promise((function(t){var n={app_id:e.config.app_id,user_unique_id:e.configManager.get("user_unique_id"),web_id:e.configManager.get("web_id"),user_unique_id_type:e.configManager.get("user_unique_id_type")};e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 发起GetToken请求",logType:"fetch",level:"info",time:Date.now(),data:n,infoType:"sdk"}),e.collect.requestManager.useRequest({url:e.configManager.getUrl("tobid"),data:n,success:function(n){n&&0===n.e?(t(n.tobid),e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"GetToken请求成功",logType:"fetch",level:"info",time:Date.now(),data:n,infoType:"sdk"})):(t(""),e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"GetToken请求失败",logType:"fetch",level:"error",time:Date.now(),data:n,infoType:"sdk"}))},fail:function(){t(""),e.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"GetToken请求失败",logType:"fetch",level:"error",time:Date.now(),infoType:"sdk"})},time:3e4,withCredentials:e.enable_ttwebid})}))},e}();function W(e){for(var t=e.length;--t>=0;)e[t]=0}var J=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),Z=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),$=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),X=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Y=new Array(576);W(Y);var Q=new Array(60);W(Q);var ee=new Array(512);W(ee);var te=new Array(256);W(te);var ne=new Array(29);W(ne);var ie,re,oe,ae=new Array(30);function se(e,t,n,i,r){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=i,this.max_length=r,this.has_stree=e&&e.length}function ce(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}W(ae);var ue=function(e){return e<256?ee[e]:ee[256+(e>>>7)]},le=function(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},he=function(e,t,n){e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,le(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)},fe=function(e,t,n){he(e,n[2*t],n[2*t+1])},de=function(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1},pe=function(e,t,n){var i,r,o=new Array(16),a=0;for(i=1;i<=15;i++)a=a+n[i-1]<<1,o[i]=a;for(r=0;r<=t;r++){var s=e[2*r+1];0!==s&&(e[2*r]=de(o[s]++,s))}},ge=function(e){var t;for(t=0;t<286;t++)e.dyn_ltree[2*t]=0;for(t=0;t<30;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.sym_next=e.matches=0},_e=function(e){e.bi_valid>8?le(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},ve=function(e,t,n,i){var r=2*t,o=2*n;return e[r]<e[o]||e[r]===e[o]&&i[t]<=i[n]},me=function(e,t,n){for(var i=e.heap[n],r=n<<1;r<=e.heap_len&&(r<e.heap_len&&ve(t,e.heap[r+1],e.heap[r],e.depth)&&r++,!ve(t,i,e.heap[r],e.depth));)e.heap[n]=e.heap[r],n=r,r<<=1;e.heap[n]=i},ye=function(e,t,n){var i,r,o,a,s=0;if(0!==e.sym_next)do{i=255&e.pending_buf[e.sym_buf+s++],i+=(255&e.pending_buf[e.sym_buf+s++])<<8,r=e.pending_buf[e.sym_buf+s++],0===i?fe(e,r,t):(o=te[r],fe(e,o+256+1,t),0!==(a=J[o])&&(r-=ne[o],he(e,r,a)),o=ue(--i),fe(e,o,n),0!==(a=Z[o])&&(i-=ae[o],he(e,i,a)))}while(s<e.sym_next);fe(e,256,t)},be=function(e,t){var n,i,r,o=t.dyn_tree,a=t.stat_desc.static_tree,s=t.stat_desc.has_stree,c=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=573,n=0;n<c;n++)0!==o[2*n]?(e.heap[++e.heap_len]=u=n,e.depth[n]=0):o[2*n+1]=0;for(;e.heap_len<2;)o[2*(r=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[r]=0,e.opt_len--,s&&(e.static_len-=a[2*r+1]);for(t.max_code=u,n=e.heap_len>>1;n>=1;n--)me(e,o,n);r=c;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],me(e,o,1),i=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=i,o[2*r]=o[2*n]+o[2*i],e.depth[r]=(e.depth[n]>=e.depth[i]?e.depth[n]:e.depth[i])+1,o[2*n+1]=o[2*i+1]=r,e.heap[1]=r++,me(e,o,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,i,r,o,a,s,c=t.dyn_tree,u=t.max_code,l=t.stat_desc.static_tree,h=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,d=t.stat_desc.extra_base,p=t.stat_desc.max_length,g=0;for(o=0;o<=15;o++)e.bl_count[o]=0;for(c[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<573;n++)(o=c[2*c[2*(i=e.heap[n])+1]+1]+1)>p&&(o=p,g++),c[2*i+1]=o,i>u||(e.bl_count[o]++,a=0,i>=d&&(a=f[i-d]),s=c[2*i],e.opt_len+=s*(o+a),h&&(e.static_len+=s*(l[2*i+1]+a)));if(0!==g){do{for(o=p-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[p]--,g-=2}while(g>0);for(o=p;0!==o;o--)for(i=e.bl_count[o];0!==i;)(r=e.heap[--n])>u||(c[2*r+1]!==o&&(e.opt_len+=(o-c[2*r+1])*c[2*r],c[2*r+1]=o),i--)}}(e,t),pe(o,u,e.bl_count)},ze=function(e,t,n){var i,r,o=-1,a=t[1],s=0,c=7,u=4;for(0===a&&(c=138,u=3),t[2*(n+1)+1]=65535,i=0;i<=n;i++)r=a,a=t[2*(i+1)+1],++s<c&&r===a||(s<u?e.bl_tree[2*r]+=s:0!==r?(r!==o&&e.bl_tree[2*r]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=0,o=r,0===a?(c=138,u=3):r===a?(c=6,u=3):(c=7,u=4))},we=function(e,t,n){var i,r,o=-1,a=t[1],s=0,c=7,u=4;for(0===a&&(c=138,u=3),i=0;i<=n;i++)if(r=a,a=t[2*(i+1)+1],!(++s<c&&r===a)){if(s<u)do{fe(e,r,e.bl_tree)}while(0!=--s);else 0!==r?(r!==o&&(fe(e,r,e.bl_tree),s--),fe(e,16,e.bl_tree),he(e,s-3,2)):s<=10?(fe(e,17,e.bl_tree),he(e,s-3,3)):(fe(e,18,e.bl_tree),he(e,s-11,7));s=0,o=r,0===a?(c=138,u=3):r===a?(c=6,u=3):(c=7,u=4)}},Ee=!1,Se=function(e,t,n,i){he(e,0+(i?1:0),3),_e(e),le(e,n),le(e,~n),n&&e.pending_buf.set(e.window.subarray(t,t+n),e.pending),e.pending+=n},ke=Se,xe=function(e,t,n,i){for(var r=65535&e,o=e>>>16&65535,a=0;0!==n;){n-=a=n>2e3?2e3:n;do{o=o+(r=r+t[i++]|0)|0}while(--a);r%=65521,o%=65521}return r|o<<16},De=new Uint32Array(function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var i=0;i<8;i++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}()),Ae=function(e,t,n,i){var r=De,o=i+n;e^=-1;for(var a=i;a<o;a++)e=e>>>8^r[255&(e^t[a])];return-1^e},Te={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Re={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},Ge=function(e){Ee||(function(){var e,t,n,i,r,o=new Array(16);for(n=0,i=0;i<28;i++)for(ne[i]=n,e=0;e<1<<J[i];e++)te[n++]=i;for(te[n-1]=i,r=0,i=0;i<16;i++)for(ae[i]=r,e=0;e<1<<Z[i];e++)ee[r++]=i;for(r>>=7;i<30;i++)for(ae[i]=r<<7,e=0;e<1<<Z[i]-7;e++)ee[256+r++]=i;for(t=0;t<=15;t++)o[t]=0;for(e=0;e<=143;)Y[2*e+1]=8,e++,o[8]++;for(;e<=255;)Y[2*e+1]=9,e++,o[9]++;for(;e<=279;)Y[2*e+1]=7,e++,o[7]++;for(;e<=287;)Y[2*e+1]=8,e++,o[8]++;for(pe(Y,287,o),e=0;e<30;e++)Q[2*e+1]=5,Q[2*e]=de(e,5);ie=new se(Y,J,257,286,15),re=new se(Q,Z,0,30,15),oe=new se(new Array(0),$,0,19,7)}(),Ee=!0),e.l_desc=new ce(e.dyn_ltree,ie),e.d_desc=new ce(e.dyn_dtree,re),e.bl_desc=new ce(e.bl_tree,oe),e.bi_buf=0,e.bi_valid=0,ge(e)},Oe=ke,Me=function(e,t,n,i){var r,o,a=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<256;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),be(e,e.l_desc),be(e,e.d_desc),a=function(e){var t;for(ze(e,e.dyn_ltree,e.l_desc.max_code),ze(e,e.dyn_dtree,e.d_desc.max_code),be(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*X[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),r=e.opt_len+3+7>>>3,(o=e.static_len+3+7>>>3)<=r&&(r=o)):r=o=n+5,n+4<=r&&-1!==t?Se(e,t,n,i):4===e.strategy||o===r?(he(e,2+(i?1:0),3),ye(e,Y,Q)):(he(e,4+(i?1:0),3),function(e,t,n,i){var r;for(he(e,t-257,5),he(e,n-1,5),he(e,i-4,4),r=0;r<i;r++)he(e,e.bl_tree[2*X[r]+1],3);we(e,e.dyn_ltree,t-1),we(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,a+1),ye(e,e.dyn_ltree,e.dyn_dtree)),ge(e),i&&_e(e)},Ce=function(e,t,n){return e.pending_buf[e.sym_buf+e.sym_next++]=t,e.pending_buf[e.sym_buf+e.sym_next++]=t>>8,e.pending_buf[e.sym_buf+e.sym_next++]=n,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(te[n]+256+1)]++,e.dyn_dtree[2*ue(t)]++),e.sym_next===e.sym_end},Ie=function(e){he(e,2,3),fe(e,256,Y),function(e){16===e.bi_valid?(le(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)},Be=Re.Z_NO_FLUSH,Ue=Re.Z_PARTIAL_FLUSH,Le=Re.Z_FULL_FLUSH,Ne=Re.Z_FINISH,je=Re.Z_BLOCK,Pe=Re.Z_OK,Ke=Re.Z_STREAM_END,qe=Re.Z_STREAM_ERROR,He=Re.Z_DATA_ERROR,Ve=Re.Z_BUF_ERROR,Fe=Re.Z_DEFAULT_COMPRESSION,We=Re.Z_FILTERED,Je=Re.Z_HUFFMAN_ONLY,Ze=Re.Z_RLE,$e=Re.Z_FIXED,Xe=Re.Z_UNKNOWN,Ye=Re.Z_DEFLATED,Qe=function(e,t){return e.msg=Te[t],t},et=function(e){return 2*e-(e>4?9:0)},tt=function(e){for(var t=e.length;--t>=0;)e[t]=0},nt=function(e){var t,n,i,r=e.w_size;i=t=e.hash_size;do{n=e.head[--i],e.head[i]=n>=r?n-r:0}while(--t);i=t=r;do{n=e.prev[--i],e.prev[i]=n>=r?n-r:0}while(--t)},it=function(e,t,n){return(t<<e.hash_shift^n)&e.hash_mask},rt=function(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+n),e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))},ot=function(e,t){Me(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,rt(e.strm)},at=function(e,t){e.pending_buf[e.pending++]=t},st=function(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},ct=function(e,t,n,i){var r=e.avail_in;return r>i&&(r=i),0===r?0:(e.avail_in-=r,t.set(e.input.subarray(e.next_in,e.next_in+r),n),1===e.state.wrap?e.adler=xe(e.adler,t,r,n):2===e.state.wrap&&(e.adler=Ae(e.adler,t,r,n)),e.next_in+=r,e.total_in+=r,r)},ut=function(e,t){var n,i,r=e.max_chain_length,o=e.strstart,a=e.prev_length,s=e.nice_match,c=e.strstart>e.w_size-262?e.strstart-(e.w_size-262):0,u=e.window,l=e.w_mask,h=e.prev,f=e.strstart+258,d=u[o+a-1],p=u[o+a];e.prev_length>=e.good_match&&(r>>=2),s>e.lookahead&&(s=e.lookahead);do{if(u[(n=t)+a]===p&&u[n+a-1]===d&&u[n]===u[o]&&u[++n]===u[o+1]){o+=2,n++;do{}while(u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&o<f);if(i=258-(f-o),o=f-258,i>a){if(e.match_start=t,a=i,i>=s)break;d=u[o+a-1],p=u[o+a]}}}while((t=h[t&l])>c&&0!=--r);return a<=e.lookahead?a:e.lookahead},lt=function(e){var t,n,i,r=e.w_size;do{if(n=e.window_size-e.lookahead-e.strstart,e.strstart>=r+(r-262)&&(e.window.set(e.window.subarray(r,r+r-n),0),e.match_start-=r,e.strstart-=r,e.block_start-=r,e.insert>e.strstart&&(e.insert=e.strstart),nt(e),n+=r),0===e.strm.avail_in)break;if(t=ct(e.strm,e.window,e.strstart+e.lookahead,n),e.lookahead+=t,e.lookahead+e.insert>=3)for(i=e.strstart-e.insert,e.ins_h=e.window[i],e.ins_h=it(e,e.ins_h,e.window[i+1]);e.insert&&(e.ins_h=it(e,e.ins_h,e.window[i+3-1]),e.prev[i&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=i,i++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<262&&0!==e.strm.avail_in)},ht=function(e,t){var n,i,r,o=e.pending_buf_size-5>e.w_size?e.w_size:e.pending_buf_size-5,a=0,s=e.strm.avail_in;do{if(n=65535,r=e.bi_valid+42>>3,e.strm.avail_out<r)break;if(r=e.strm.avail_out-r,n>(i=e.strstart-e.block_start)+e.strm.avail_in&&(n=i+e.strm.avail_in),n>r&&(n=r),n<o&&(0===n&&t!==Ne||t===Be||n!==i+e.strm.avail_in))break;a=t===Ne&&n===i+e.strm.avail_in?1:0,Oe(e,0,0,a),e.pending_buf[e.pending-4]=n,e.pending_buf[e.pending-3]=n>>8,e.pending_buf[e.pending-2]=~n,e.pending_buf[e.pending-1]=~n>>8,rt(e.strm),i&&(i>n&&(i=n),e.strm.output.set(e.window.subarray(e.block_start,e.block_start+i),e.strm.next_out),e.strm.next_out+=i,e.strm.avail_out-=i,e.strm.total_out+=i,e.block_start+=i,n-=i),n&&(ct(e.strm,e.strm.output,e.strm.next_out,n),e.strm.next_out+=n,e.strm.avail_out-=n,e.strm.total_out+=n)}while(0===a);return(s-=e.strm.avail_in)&&(s>=e.w_size?(e.matches=2,e.window.set(e.strm.input.subarray(e.strm.next_in-e.w_size,e.strm.next_in),0),e.strstart=e.w_size,e.insert=e.strstart):(e.window_size-e.strstart<=s&&(e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,e.insert>e.strstart&&(e.insert=e.strstart)),e.window.set(e.strm.input.subarray(e.strm.next_in-s,e.strm.next_in),e.strstart),e.strstart+=s,e.insert+=s>e.w_size-e.insert?e.w_size-e.insert:s),e.block_start=e.strstart),e.high_water<e.strstart&&(e.high_water=e.strstart),a?4:t!==Be&&t!==Ne&&0===e.strm.avail_in&&e.strstart===e.block_start?2:(r=e.window_size-e.strstart,e.strm.avail_in>r&&e.block_start>=e.w_size&&(e.block_start-=e.w_size,e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,r+=e.w_size,e.insert>e.strstart&&(e.insert=e.strstart)),r>e.strm.avail_in&&(r=e.strm.avail_in),r&&(ct(e.strm,e.window,e.strstart,r),e.strstart+=r,e.insert+=r>e.w_size-e.insert?e.w_size-e.insert:r),e.high_water<e.strstart&&(e.high_water=e.strstart),r=e.bi_valid+42>>3,o=(r=e.pending_buf_size-r>65535?65535:e.pending_buf_size-r)>e.w_size?e.w_size:r,((i=e.strstart-e.block_start)>=o||(i||t===Ne)&&t!==Be&&0===e.strm.avail_in&&i<=r)&&(n=i>r?r:i,a=t===Ne&&0===e.strm.avail_in&&n===i?1:0,Oe(e,e.block_start,n,a),e.block_start+=n,rt(e.strm)),a?3:1)},ft=function(e,t){for(var n,i;;){if(e.lookahead<262){if(lt(e),e.lookahead<262&&t===Be)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=it(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-262&&(e.match_length=ut(e,n)),e.match_length>=3)if(i=Ce(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=it(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=it(e,e.ins_h,e.window[e.strstart+1]);else i=Ce(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(i&&(ot(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,t===Ne?(ot(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(ot(e,!1),0===e.strm.avail_out)?1:2},dt=function(e,t){for(var n,i,r;;){if(e.lookahead<262){if(lt(e),e.lookahead<262&&t===Be)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=it(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-262&&(e.match_length=ut(e,n),e.match_length<=5&&(e.strategy===We||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){r=e.strstart+e.lookahead-3,i=Ce(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=r&&(e.ins_h=it(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,i&&(ot(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((i=Ce(e,0,e.window[e.strstart-1]))&&ot(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(i=Ce(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,t===Ne?(ot(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(ot(e,!1),0===e.strm.avail_out)?1:2};function pt(e,t,n,i,r){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=i,this.func=r}var gt=[new pt(0,0,0,0,ht),new pt(4,4,8,4,ft),new pt(4,5,16,8,ft),new pt(4,6,32,32,ft),new pt(4,4,16,16,dt),new pt(8,16,32,32,dt),new pt(8,16,128,128,dt),new pt(8,32,128,256,dt),new pt(32,128,258,1024,dt),new pt(32,258,258,4096,dt)];function _t(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Ye,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),tt(this.dyn_ltree),tt(this.dyn_dtree),tt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),tt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),tt(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var vt=function(e){if(!e)return 1;var t=e.state;return!t||t.strm!==e||42!==t.status&&57!==t.status&&69!==t.status&&73!==t.status&&91!==t.status&&103!==t.status&&113!==t.status&&666!==t.status?1:0},mt=function(e){var t,n=function(e){if(vt(e))return Qe(e,qe);e.total_in=e.total_out=0,e.data_type=Xe;var t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=2===t.wrap?57:t.wrap?42:113,e.adler=2===t.wrap?0:1,t.last_flush=-2,Ge(t),Pe}(e);return n===Pe&&((t=e.state).window_size=2*t.w_size,tt(t.head),t.max_lazy_match=gt[t.level].max_lazy,t.good_match=gt[t.level].good_length,t.nice_match=gt[t.level].nice_length,t.max_chain_length=gt[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),n},yt=function(e,t,n,i,r,o){if(!e)return qe;var a=1;if(t===Fe&&(t=6),i<0?(a=0,i=-i):i>15&&(a=2,i-=16),r<1||r>9||n!==Ye||i<8||i>15||t<0||t>9||o<0||o>$e||8===i&&1!==a)return Qe(e,qe);8===i&&(i=9);var s=new _t;return e.state=s,s.strm=e,s.status=42,s.wrap=a,s.gzhead=null,s.w_bits=i,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=r+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new Uint8Array(2*s.w_size),s.head=new Uint16Array(s.hash_size),s.prev=new Uint16Array(s.w_size),s.lit_bufsize=1<<r+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Uint8Array(s.pending_buf_size),s.sym_buf=s.lit_bufsize,s.sym_end=3*(s.lit_bufsize-1),s.level=t,s.strategy=o,s.method=n,mt(e)},bt=function(e,t){if(vt(e)||t>je||t<0)return e?Qe(e,qe):qe;var n=e.state;if(!e.output||0!==e.avail_in&&!e.input||666===n.status&&t!==Ne)return Qe(e,0===e.avail_out?Ve:qe);var i=n.last_flush;if(n.last_flush=t,0!==n.pending){if(rt(e),0===e.avail_out)return n.last_flush=-1,Pe}else if(0===e.avail_in&&et(t)<=et(i)&&t!==Ne)return Qe(e,Ve);if(666===n.status&&0!==e.avail_in)return Qe(e,Ve);if(42===n.status&&0===n.wrap&&(n.status=113),42===n.status){var r=Ye+(n.w_bits-8<<4)<<8;if(r|=(n.strategy>=Je||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(r|=32),st(n,r+=31-r%31),0!==n.strstart&&(st(n,e.adler>>>16),st(n,65535&e.adler)),e.adler=1,n.status=113,rt(e),0!==n.pending)return n.last_flush=-1,Pe}if(57===n.status)if(e.adler=0,at(n,31),at(n,139),at(n,8),n.gzhead)at(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),at(n,255&n.gzhead.time),at(n,n.gzhead.time>>8&255),at(n,n.gzhead.time>>16&255),at(n,n.gzhead.time>>24&255),at(n,9===n.level?2:n.strategy>=Je||n.level<2?4:0),at(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(at(n,255&n.gzhead.extra.length),at(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=Ae(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69;else if(at(n,0),at(n,0),at(n,0),at(n,0),at(n,0),at(n,9===n.level?2:n.strategy>=Je||n.level<2?4:0),at(n,3),n.status=113,rt(e),0!==n.pending)return n.last_flush=-1,Pe;if(69===n.status){if(n.gzhead.extra){for(var o=n.pending,a=(65535&n.gzhead.extra.length)-n.gzindex;n.pending+a>n.pending_buf_size;){var s=n.pending_buf_size-n.pending;if(n.pending_buf.set(n.gzhead.extra.subarray(n.gzindex,n.gzindex+s),n.pending),n.pending=n.pending_buf_size,n.gzhead.hcrc&&n.pending>o&&(e.adler=Ae(e.adler,n.pending_buf,n.pending-o,o)),n.gzindex+=s,rt(e),0!==n.pending)return n.last_flush=-1,Pe;o=0,a-=s}var c=new Uint8Array(n.gzhead.extra);n.pending_buf.set(c.subarray(n.gzindex,n.gzindex+a),n.pending),n.pending+=a,n.gzhead.hcrc&&n.pending>o&&(e.adler=Ae(e.adler,n.pending_buf,n.pending-o,o)),n.gzindex=0}n.status=73}if(73===n.status){if(n.gzhead.name){var u,l=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>l&&(e.adler=Ae(e.adler,n.pending_buf,n.pending-l,l)),rt(e),0!==n.pending)return n.last_flush=-1,Pe;l=0}u=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,at(n,u)}while(0!==u);n.gzhead.hcrc&&n.pending>l&&(e.adler=Ae(e.adler,n.pending_buf,n.pending-l,l)),n.gzindex=0}n.status=91}if(91===n.status){if(n.gzhead.comment){var h,f=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>f&&(e.adler=Ae(e.adler,n.pending_buf,n.pending-f,f)),rt(e),0!==n.pending)return n.last_flush=-1,Pe;f=0}h=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,at(n,h)}while(0!==h);n.gzhead.hcrc&&n.pending>f&&(e.adler=Ae(e.adler,n.pending_buf,n.pending-f,f))}n.status=103}if(103===n.status){if(n.gzhead.hcrc){if(n.pending+2>n.pending_buf_size&&(rt(e),0!==n.pending))return n.last_flush=-1,Pe;at(n,255&e.adler),at(n,e.adler>>8&255),e.adler=0}if(n.status=113,rt(e),0!==n.pending)return n.last_flush=-1,Pe}if(0!==e.avail_in||0!==n.lookahead||t!==Be&&666!==n.status){var d=0===n.level?ht(n,t):n.strategy===Je?function(e,t){for(var n;;){if(0===e.lookahead&&(lt(e),0===e.lookahead)){if(t===Be)return 1;break}if(e.match_length=0,n=Ce(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(ot(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===Ne?(ot(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(ot(e,!1),0===e.strm.avail_out)?1:2}(n,t):n.strategy===Ze?function(e,t){for(var n,i,r,o,a=e.window;;){if(e.lookahead<=258){if(lt(e),e.lookahead<=258&&t===Be)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(i=a[r=e.strstart-1])===a[++r]&&i===a[++r]&&i===a[++r]){o=e.strstart+258;do{}while(i===a[++r]&&i===a[++r]&&i===a[++r]&&i===a[++r]&&i===a[++r]&&i===a[++r]&&i===a[++r]&&i===a[++r]&&r<o);e.match_length=258-(o-r),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(n=Ce(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=Ce(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(ot(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===Ne?(ot(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(ot(e,!1),0===e.strm.avail_out)?1:2}(n,t):gt[n.level].func(n,t);if(3!==d&&4!==d||(n.status=666),1===d||3===d)return 0===e.avail_out&&(n.last_flush=-1),Pe;if(2===d&&(t===Ue?Ie(n):t!==je&&(Oe(n,0,0,!1),t===Le&&(tt(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),rt(e),0===e.avail_out))return n.last_flush=-1,Pe}return t!==Ne?Pe:n.wrap<=0?Ke:(2===n.wrap?(at(n,255&e.adler),at(n,e.adler>>8&255),at(n,e.adler>>16&255),at(n,e.adler>>24&255),at(n,255&e.total_in),at(n,e.total_in>>8&255),at(n,e.total_in>>16&255),at(n,e.total_in>>24&255)):(st(n,e.adler>>>16),st(n,65535&e.adler)),rt(e),n.wrap>0&&(n.wrap=-n.wrap),0!==n.pending?Pe:Ke)},zt=function(e){if(vt(e))return qe;var t=e.state.status;return e.state=null,113===t?Qe(e,He):Pe},wt=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},Et=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var n=t.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(var i in n)wt(n,i)&&(e[i]=n[i])}}return e},St=function(e){for(var t=0,n=0,i=e.length;n<i;n++)t+=e[n].length;for(var r=new Uint8Array(t),o=0,a=0,s=e.length;o<s;o++){var c=e[o];r.set(c,a),a+=c.length}return r},kt=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(c){kt=!1}for(var xt=new Uint8Array(256),Dt=0;Dt<256;Dt++)xt[Dt]=Dt>=252?6:Dt>=248?5:Dt>=240?4:Dt>=224?3:Dt>=192?2:1;xt[254]=xt[254]=1;var At=function(e){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);var t,n,i,r,o,a=e.length,s=0;for(r=0;r<a;r++)55296==(64512&(n=e.charCodeAt(r)))&&r+1<a&&56320==(64512&(i=e.charCodeAt(r+1)))&&(n=65536+(n-55296<<10)+(i-56320),r++),s+=n<128?1:n<2048?2:n<65536?3:4;for(t=new Uint8Array(s),o=0,r=0;o<s;r++)55296==(64512&(n=e.charCodeAt(r)))&&r+1<a&&56320==(64512&(i=e.charCodeAt(r+1)))&&(n=65536+(n-55296<<10)+(i-56320),r++),n<128?t[o++]=n:n<2048?(t[o++]=192|n>>>6,t[o++]=128|63&n):n<65536?(t[o++]=224|n>>>12,t[o++]=128|n>>>6&63,t[o++]=128|63&n):(t[o++]=240|n>>>18,t[o++]=128|n>>>12&63,t[o++]=128|n>>>6&63,t[o++]=128|63&n);return t},Tt=function(e,t){var n,i,r=t||e.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(e.subarray(0,t));var o=new Array(2*r);for(i=0,n=0;n<r;){var a=e[n++];if(a<128)o[i++]=a;else{var s=xt[a];if(s>4)o[i++]=65533,n+=s-1;else{for(a&=2===s?31:3===s?15:7;s>1&&n<r;)a=a<<6|63&e[n++],s--;s>1?o[i++]=65533:a<65536?o[i++]=a:(a-=65536,o[i++]=55296|a>>10&1023,o[i++]=56320|1023&a)}}}return function(e,t){if(t<65534&&e.subarray&&kt)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));for(var n="",i=0;i<t;i++)n+=String.fromCharCode(e[i]);return n}(o,i)},Rt=function(e,t){(t=t||e.length)>e.length&&(t=e.length);for(var n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+xt[e[n]]>t?n:t},Gt=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Ot=Object.prototype.toString,Mt=Re.Z_NO_FLUSH,Ct=Re.Z_SYNC_FLUSH,It=Re.Z_FULL_FLUSH,Bt=Re.Z_FINISH,Ut=Re.Z_OK,Lt=Re.Z_STREAM_END,Nt=Re.Z_DEFAULT_COMPRESSION,jt=Re.Z_DEFAULT_STRATEGY,Pt=Re.Z_DEFLATED;function Kt(e){this.options=Et({level:Nt,method:Pt,chunkSize:16384,windowBits:15,memLevel:8,strategy:jt},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Gt,this.strm.avail_out=0;var n=yt(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==Ut)throw new Error(Te[n]);if(t.header&&function(e,t){vt(e)||2!==e.state.wrap||(e.state.gzhead=t)}(this.strm,t.header),t.dictionary){var i;if(i="string"==typeof t.dictionary?At(t.dictionary):"[object ArrayBuffer]"===Ot.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(n=function(e,t){var n=t.length;if(vt(e))return qe;var i=e.state,r=i.wrap;if(2===r||1===r&&42!==i.status||i.lookahead)return qe;if(1===r&&(e.adler=xe(e.adler,t,n,0)),i.wrap=0,n>=i.w_size){0===r&&(tt(i.head),i.strstart=0,i.block_start=0,i.insert=0);var o=new Uint8Array(i.w_size);o.set(t.subarray(n-i.w_size,n),0),t=o,n=i.w_size}var a=e.avail_in,s=e.next_in,c=e.input;for(e.avail_in=n,e.next_in=0,e.input=t,lt(i);i.lookahead>=3;){var u=i.strstart,l=i.lookahead-2;do{i.ins_h=it(i,i.ins_h,i.window[u+3-1]),i.prev[u&i.w_mask]=i.head[i.ins_h],i.head[i.ins_h]=u,u++}while(--l);i.strstart=u,i.lookahead=2,lt(i)}return i.strstart+=i.lookahead,i.block_start=i.strstart,i.insert=i.lookahead,i.lookahead=0,i.match_length=i.prev_length=2,i.match_available=0,e.next_in=s,e.input=c,e.avail_in=a,i.wrap=r,Pe}(this.strm,i))!==Ut)throw new Error(Te[n]);this._dict_set=!0}}function qt(e,t){var n=new Kt(t);if(n.push(e,!0),n.err)throw n.msg||Te[n.err];return n.result}Kt.prototype.push=function(e,t){var n,i,r=this.strm,o=this.options.chunkSize;if(this.ended)return!1;for(i=t===~~t?t:!0===t?Bt:Mt,"string"==typeof e?r.input=At(e):"[object ArrayBuffer]"===Ot.call(e)?r.input=new Uint8Array(e):r.input=e,r.next_in=0,r.avail_in=r.input.length;;)if(0===r.avail_out&&(r.output=new Uint8Array(o),r.next_out=0,r.avail_out=o),(i===Ct||i===It)&&r.avail_out<=6)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else{if((n=bt(r,i))===Lt)return r.next_out>0&&this.onData(r.output.subarray(0,r.next_out)),n=zt(this.strm),this.onEnd(n),this.ended=!0,n===Ut;if(0!==r.avail_out){if(i>0&&r.next_out>0)this.onData(r.output.subarray(0,r.next_out)),r.avail_out=0;else if(0===r.avail_in)break}else this.onData(r.output)}return!0},Kt.prototype.onData=function(e){this.chunks.push(e)},Kt.prototype.onEnd=function(e){e===Ut&&(this.result=St(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var Ht={Deflate:Kt,deflate:qt,deflateRaw:function(e,t){return(t=t||{}).raw=!0,qt(e,t)},gzip:function(e,t){return(t=t||{}).gzip=!0,qt(e,t)},constants:Re},Vt=function(e,t){var n,i,r,o,a,s,c,u,l,h,f,d,p,g,_,v,m,y,b,z,w,E,S,k,x=e.state;n=e.next_in,S=e.input,i=n+(e.avail_in-5),r=e.next_out,k=e.output,o=r-(t-e.avail_out),a=r+(e.avail_out-257),s=x.dmax,c=x.wsize,u=x.whave,l=x.wnext,h=x.window,f=x.hold,d=x.bits,p=x.lencode,g=x.distcode,_=(1<<x.lenbits)-1,v=(1<<x.distbits)-1;e:do{d<15&&(f+=S[n++]<<d,d+=8,f+=S[n++]<<d,d+=8),m=p[f&_];t:for(;;){if(f>>>=y=m>>>24,d-=y,0==(y=m>>>16&255))k[r++]=65535&m;else{if(!(16&y)){if(!(64&y)){m=p[(65535&m)+(f&(1<<y)-1)];continue t}if(32&y){x.mode=16191;break e}e.msg="invalid literal/length code",x.mode=16209;break e}b=65535&m,(y&=15)&&(d<y&&(f+=S[n++]<<d,d+=8),b+=f&(1<<y)-1,f>>>=y,d-=y),d<15&&(f+=S[n++]<<d,d+=8,f+=S[n++]<<d,d+=8),m=g[f&v];n:for(;;){if(f>>>=y=m>>>24,d-=y,!(16&(y=m>>>16&255))){if(!(64&y)){m=g[(65535&m)+(f&(1<<y)-1)];continue n}e.msg="invalid distance code",x.mode=16209;break e}if(z=65535&m,d<(y&=15)&&(f+=S[n++]<<d,(d+=8)<y&&(f+=S[n++]<<d,d+=8)),(z+=f&(1<<y)-1)>s){e.msg="invalid distance too far back",x.mode=16209;break e}if(f>>>=y,d-=y,z>(y=r-o)){if((y=z-y)>u&&x.sane){e.msg="invalid distance too far back",x.mode=16209;break e}if(w=0,E=h,0===l){if(w+=c-y,y<b){b-=y;do{k[r++]=h[w++]}while(--y);w=r-z,E=k}}else if(l<y){if(w+=c+l-y,(y-=l)<b){b-=y;do{k[r++]=h[w++]}while(--y);if(w=0,l<b){b-=y=l;do{k[r++]=h[w++]}while(--y);w=r-z,E=k}}}else if(w+=l-y,y<b){b-=y;do{k[r++]=h[w++]}while(--y);w=r-z,E=k}for(;b>2;)k[r++]=E[w++],k[r++]=E[w++],k[r++]=E[w++],b-=3;b&&(k[r++]=E[w++],b>1&&(k[r++]=E[w++]))}else{w=r-z;do{k[r++]=k[w++],k[r++]=k[w++],k[r++]=k[w++],b-=3}while(b>2);b&&(k[r++]=k[w++],b>1&&(k[r++]=k[w++]))}break}}break}}while(n<i&&r<a);n-=b=d>>3,f&=(1<<(d-=b<<3))-1,e.next_in=n,e.next_out=r,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=r<a?a-r+257:257-(r-a),x.hold=f,x.bits=d},Ft=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),Wt=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Jt=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Zt=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),$t=function(e,t,n,i,r,o,a,s){var c,u,l,h,f,d,p,g,_,v=s.bits,m=0,y=0,b=0,z=0,w=0,E=0,S=0,k=0,x=0,D=0,A=null,T=new Uint16Array(16),R=new Uint16Array(16),G=null;for(m=0;m<=15;m++)T[m]=0;for(y=0;y<i;y++)T[t[n+y]]++;for(w=v,z=15;z>=1&&0===T[z];z--);if(w>z&&(w=z),0===z)return r[o++]=20971520,r[o++]=20971520,s.bits=1,0;for(b=1;b<z&&0===T[b];b++);for(w<b&&(w=b),k=1,m=1;m<=15;m++)if(k<<=1,(k-=T[m])<0)return-1;if(k>0&&(0===e||1!==z))return-1;for(R[1]=0,m=1;m<15;m++)R[m+1]=R[m]+T[m];for(y=0;y<i;y++)0!==t[n+y]&&(a[R[t[n+y]]++]=y);if(0===e?(A=G=a,d=20):1===e?(A=Ft,G=Wt,d=257):(A=Jt,G=Zt,d=0),D=0,y=0,m=b,f=o,E=w,S=0,l=-1,h=(x=1<<w)-1,1===e&&x>852||2===e&&x>592)return 1;for(;;){p=m-S,a[y]+1<d?(g=0,_=a[y]):a[y]>=d?(g=G[a[y]-d],_=A[a[y]-d]):(g=96,_=0),c=1<<m-S,b=u=1<<E;do{r[f+(D>>S)+(u-=c)]=p<<24|g<<16|_}while(0!==u);for(c=1<<m-1;D&c;)c>>=1;if(0!==c?(D&=c-1,D+=c):D=0,y++,0==--T[m]){if(m===z)break;m=t[n+a[y]]}if(m>w&&(D&h)!==l){for(0===S&&(S=w),f+=b,k=1<<(E=m-S);E+S<z&&!((k-=T[E+S])<=0);)E++,k<<=1;if(x+=1<<E,1===e&&x>852||2===e&&x>592)return 1;r[l=D&h]=w<<24|E<<16|f-o}}return 0!==D&&(r[f+D]=m-S<<24|64<<16),s.bits=w,0},Xt=Re.Z_FINISH,Yt=Re.Z_BLOCK,Qt=Re.Z_TREES,en=Re.Z_OK,tn=Re.Z_STREAM_END,nn=Re.Z_NEED_DICT,rn=Re.Z_STREAM_ERROR,on=Re.Z_DATA_ERROR,an=Re.Z_MEM_ERROR,sn=Re.Z_BUF_ERROR,cn=Re.Z_DEFLATED,un=function(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)};function ln(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}var hn,fn,dn=function(e){if(!e)return 1;var t=e.state;return!t||t.strm!==e||t.mode<16180||t.mode>16211?1:0},pn=function(e){if(dn(e))return rn;var t=e.state;return t.wsize=0,t.whave=0,t.wnext=0,function(e){if(dn(e))return rn;var t=e.state;return e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=16180,t.last=0,t.havedict=0,t.flags=-1,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(852),t.distcode=t.distdyn=new Int32Array(592),t.sane=1,t.back=-1,en}(e)},gn=!0,_n=function(e){if(gn){hn=new Int32Array(512),fn=new Int32Array(32);for(var t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for($t(1,e.lens,0,288,hn,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;$t(2,e.lens,0,32,fn,0,e.work,{bits:5}),gn=!1}e.lencode=hn,e.lenbits=9,e.distcode=fn,e.distbits=5},vn=function(e,t,n,i){var r,o=e.state;return null===o.window&&(o.wsize=1<<o.wbits,o.wnext=0,o.whave=0,o.window=new Uint8Array(o.wsize)),i>=o.wsize?(o.window.set(t.subarray(n-o.wsize,n),0),o.wnext=0,o.whave=o.wsize):((r=o.wsize-o.wnext)>i&&(r=i),o.window.set(t.subarray(n-i,n-i+r),o.wnext),(i-=r)?(o.window.set(t.subarray(n-i,n),0),o.wnext=i,o.whave=o.wsize):(o.wnext+=r,o.wnext===o.wsize&&(o.wnext=0),o.whave<o.wsize&&(o.whave+=r))),0},mn=pn,yn=function(e,t){if(!e)return rn;var n=new ln;e.state=n,n.strm=e,n.window=null,n.mode=16180;var i=function(e,t){var n;if(dn(e))return rn;var i=e.state;return t<0?(n=0,t=-t):(n=5+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?rn:(null!==i.window&&i.wbits!==t&&(i.window=null),i.wrap=n,i.wbits=t,pn(e))}(e,t);return i!==en&&(e.state=null),i},bn=function(e,t){var n,i,r,o,a,s,c,u,l,h,f,d,p,g,_,v,m,y,b,z,w,E,S,k,x=0,D=new Uint8Array(4),A=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(dn(e)||!e.output||!e.input&&0!==e.avail_in)return rn;16191===(n=e.state).mode&&(n.mode=16192),a=e.next_out,r=e.output,c=e.avail_out,o=e.next_in,i=e.input,s=e.avail_in,u=n.hold,l=n.bits,h=s,f=c,E=en;e:for(;;)switch(n.mode){case 16180:if(0===n.wrap){n.mode=16192;break}for(;l<16;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(2&n.wrap&&35615===u){0===n.wbits&&(n.wbits=15),n.check=0,D[0]=255&u,D[1]=u>>>8&255,n.check=Ae(n.check,D,2,0),u=0,l=0,n.mode=16181;break}if(n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&u)<<8)+(u>>8))%31){e.msg="incorrect header check",n.mode=16209;break}if((15&u)!==cn){e.msg="unknown compression method",n.mode=16209;break}if(l-=4,w=8+(15&(u>>>=4)),0===n.wbits&&(n.wbits=w),w>15||w>n.wbits){e.msg="invalid window size",n.mode=16209;break}n.dmax=1<<n.wbits,n.flags=0,e.adler=n.check=1,n.mode=512&u?16189:16191,u=0,l=0;break;case 16181:for(;l<16;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(n.flags=u,(255&n.flags)!==cn){e.msg="unknown compression method",n.mode=16209;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=16209;break}n.head&&(n.head.text=u>>8&1),512&n.flags&&4&n.wrap&&(D[0]=255&u,D[1]=u>>>8&255,n.check=Ae(n.check,D,2,0)),u=0,l=0,n.mode=16182;case 16182:for(;l<32;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}n.head&&(n.head.time=u),512&n.flags&&4&n.wrap&&(D[0]=255&u,D[1]=u>>>8&255,D[2]=u>>>16&255,D[3]=u>>>24&255,n.check=Ae(n.check,D,4,0)),u=0,l=0,n.mode=16183;case 16183:for(;l<16;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}n.head&&(n.head.xflags=255&u,n.head.os=u>>8),512&n.flags&&4&n.wrap&&(D[0]=255&u,D[1]=u>>>8&255,n.check=Ae(n.check,D,2,0)),u=0,l=0,n.mode=16184;case 16184:if(1024&n.flags){for(;l<16;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}n.length=u,n.head&&(n.head.extra_len=u),512&n.flags&&4&n.wrap&&(D[0]=255&u,D[1]=u>>>8&255,n.check=Ae(n.check,D,2,0)),u=0,l=0}else n.head&&(n.head.extra=null);n.mode=16185;case 16185:if(1024&n.flags&&((d=n.length)>s&&(d=s),d&&(n.head&&(w=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Uint8Array(n.head.extra_len)),n.head.extra.set(i.subarray(o,o+d),w)),512&n.flags&&4&n.wrap&&(n.check=Ae(n.check,i,d,o)),s-=d,o+=d,n.length-=d),n.length))break e;n.length=0,n.mode=16186;case 16186:if(2048&n.flags){if(0===s)break e;d=0;do{w=i[o+d++],n.head&&w&&n.length<65536&&(n.head.name+=String.fromCharCode(w))}while(w&&d<s);if(512&n.flags&&4&n.wrap&&(n.check=Ae(n.check,i,d,o)),s-=d,o+=d,w)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=16187;case 16187:if(4096&n.flags){if(0===s)break e;d=0;do{w=i[o+d++],n.head&&w&&n.length<65536&&(n.head.comment+=String.fromCharCode(w))}while(w&&d<s);if(512&n.flags&&4&n.wrap&&(n.check=Ae(n.check,i,d,o)),s-=d,o+=d,w)break e}else n.head&&(n.head.comment=null);n.mode=16188;case 16188:if(512&n.flags){for(;l<16;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(4&n.wrap&&u!==(65535&n.check)){e.msg="header crc mismatch",n.mode=16209;break}u=0,l=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=16191;break;case 16189:for(;l<32;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}e.adler=n.check=un(u),u=0,l=0,n.mode=16190;case 16190:if(0===n.havedict)return e.next_out=a,e.avail_out=c,e.next_in=o,e.avail_in=s,n.hold=u,n.bits=l,nn;e.adler=n.check=1,n.mode=16191;case 16191:if(t===Yt||t===Qt)break e;case 16192:if(n.last){u>>>=7&l,l-=7&l,n.mode=16206;break}for(;l<3;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}switch(n.last=1&u,l-=1,3&(u>>>=1)){case 0:n.mode=16193;break;case 1:if(_n(n),n.mode=16199,t===Qt){u>>>=2,l-=2;break e}break;case 2:n.mode=16196;break;case 3:e.msg="invalid block type",n.mode=16209}u>>>=2,l-=2;break;case 16193:for(u>>>=7&l,l-=7&l;l<32;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if((65535&u)!=(u>>>16^65535)){e.msg="invalid stored block lengths",n.mode=16209;break}if(n.length=65535&u,u=0,l=0,n.mode=16194,t===Qt)break e;case 16194:n.mode=16195;case 16195:if(d=n.length){if(d>s&&(d=s),d>c&&(d=c),0===d)break e;r.set(i.subarray(o,o+d),a),s-=d,o+=d,c-=d,a+=d,n.length-=d;break}n.mode=16191;break;case 16196:for(;l<14;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(n.nlen=257+(31&u),u>>>=5,l-=5,n.ndist=1+(31&u),u>>>=5,l-=5,n.ncode=4+(15&u),u>>>=4,l-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=16209;break}n.have=0,n.mode=16197;case 16197:for(;n.have<n.ncode;){for(;l<3;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}n.lens[A[n.have++]]=7&u,u>>>=3,l-=3}for(;n.have<19;)n.lens[A[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,S={bits:n.lenbits},E=$t(0,n.lens,0,19,n.lencode,0,n.work,S),n.lenbits=S.bits,E){e.msg="invalid code lengths set",n.mode=16209;break}n.have=0,n.mode=16198;case 16198:for(;n.have<n.nlen+n.ndist;){for(;v=(x=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,m=65535&x,!((_=x>>>24)<=l);){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(m<16)u>>>=_,l-=_,n.lens[n.have++]=m;else{if(16===m){for(k=_+2;l<k;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(u>>>=_,l-=_,0===n.have){e.msg="invalid bit length repeat",n.mode=16209;break}w=n.lens[n.have-1],d=3+(3&u),u>>>=2,l-=2}else if(17===m){for(k=_+3;l<k;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}l-=_,w=0,d=3+(7&(u>>>=_)),u>>>=3,l-=3}else{for(k=_+7;l<k;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}l-=_,w=0,d=11+(127&(u>>>=_)),u>>>=7,l-=7}if(n.have+d>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=16209;break}for(;d--;)n.lens[n.have++]=w}}if(16209===n.mode)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=16209;break}if(n.lenbits=9,S={bits:n.lenbits},E=$t(1,n.lens,0,n.nlen,n.lencode,0,n.work,S),n.lenbits=S.bits,E){e.msg="invalid literal/lengths set",n.mode=16209;break}if(n.distbits=6,n.distcode=n.distdyn,S={bits:n.distbits},E=$t(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,S),n.distbits=S.bits,E){e.msg="invalid distances set",n.mode=16209;break}if(n.mode=16199,t===Qt)break e;case 16199:n.mode=16200;case 16200:if(s>=6&&c>=258){e.next_out=a,e.avail_out=c,e.next_in=o,e.avail_in=s,n.hold=u,n.bits=l,Vt(e,f),a=e.next_out,r=e.output,c=e.avail_out,o=e.next_in,i=e.input,s=e.avail_in,u=n.hold,l=n.bits,16191===n.mode&&(n.back=-1);break}for(n.back=0;v=(x=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,m=65535&x,!((_=x>>>24)<=l);){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(v&&!(240&v)){for(y=_,b=v,z=m;v=(x=n.lencode[z+((u&(1<<y+b)-1)>>y)])>>>16&255,m=65535&x,!(y+(_=x>>>24)<=l);){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}u>>>=y,l-=y,n.back+=y}if(u>>>=_,l-=_,n.back+=_,n.length=m,0===v){n.mode=16205;break}if(32&v){n.back=-1,n.mode=16191;break}if(64&v){e.msg="invalid literal/length code",n.mode=16209;break}n.extra=15&v,n.mode=16201;case 16201:if(n.extra){for(k=n.extra;l<k;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}n.length+=u&(1<<n.extra)-1,u>>>=n.extra,l-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=16202;case 16202:for(;v=(x=n.distcode[u&(1<<n.distbits)-1])>>>16&255,m=65535&x,!((_=x>>>24)<=l);){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(!(240&v)){for(y=_,b=v,z=m;v=(x=n.distcode[z+((u&(1<<y+b)-1)>>y)])>>>16&255,m=65535&x,!(y+(_=x>>>24)<=l);){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}u>>>=y,l-=y,n.back+=y}if(u>>>=_,l-=_,n.back+=_,64&v){e.msg="invalid distance code",n.mode=16209;break}n.offset=m,n.extra=15&v,n.mode=16203;case 16203:if(n.extra){for(k=n.extra;l<k;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}n.offset+=u&(1<<n.extra)-1,u>>>=n.extra,l-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=16209;break}n.mode=16204;case 16204:if(0===c)break e;if(d=f-c,n.offset>d){if((d=n.offset-d)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=16209;break}d>n.wnext?(d-=n.wnext,p=n.wsize-d):p=n.wnext-d,d>n.length&&(d=n.length),g=n.window}else g=r,p=a-n.offset,d=n.length;d>c&&(d=c),c-=d,n.length-=d;do{r[a++]=g[p++]}while(--d);0===n.length&&(n.mode=16200);break;case 16205:if(0===c)break e;r[a++]=n.length,c--,n.mode=16200;break;case 16206:if(n.wrap){for(;l<32;){if(0===s)break e;s--,u|=i[o++]<<l,l+=8}if(f-=c,e.total_out+=f,n.total+=f,4&n.wrap&&f&&(e.adler=n.check=n.flags?Ae(n.check,r,f,a-f):xe(n.check,r,f,a-f)),f=c,4&n.wrap&&(n.flags?u:un(u))!==n.check){e.msg="incorrect data check",n.mode=16209;break}u=0,l=0}n.mode=16207;case 16207:if(n.wrap&&n.flags){for(;l<32;){if(0===s)break e;s--,u+=i[o++]<<l,l+=8}if(4&n.wrap&&u!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=16209;break}u=0,l=0}n.mode=16208;case 16208:E=tn;break e;case 16209:E=on;break e;case 16210:return an;default:return rn}return e.next_out=a,e.avail_out=c,e.next_in=o,e.avail_in=s,n.hold=u,n.bits=l,(n.wsize||f!==e.avail_out&&n.mode<16209&&(n.mode<16206||t!==Xt))&&vn(e,e.output,e.next_out,f-e.avail_out),h-=e.avail_in,f-=e.avail_out,e.total_in+=h,e.total_out+=f,n.total+=f,4&n.wrap&&f&&(e.adler=n.check=n.flags?Ae(n.check,r,f,e.next_out-f):xe(n.check,r,f,e.next_out-f)),e.data_type=n.bits+(n.last?64:0)+(16191===n.mode?128:0)+(16199===n.mode||16194===n.mode?256:0),(0===h&&0===f||t===Xt)&&E===en&&(E=sn),E},zn=function(e){if(dn(e))return rn;var t=e.state;return t.window&&(t.window=null),e.state=null,en},wn=function(e,t){var n,i=t.length;return dn(e)||0!==(n=e.state).wrap&&16190!==n.mode?rn:16190===n.mode&&xe(1,t,i,0)!==n.check?on:vn(e,t,i,i)?(n.mode=16210,an):(n.havedict=1,en)},En=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},Sn=Object.prototype.toString,kn=Re.Z_NO_FLUSH,xn=Re.Z_FINISH,Dn=Re.Z_OK,An=Re.Z_STREAM_END,Tn=Re.Z_NEED_DICT,Rn=Re.Z_STREAM_ERROR,Gn=Re.Z_DATA_ERROR,On=Re.Z_MEM_ERROR;function Mn(e){this.options=Et({chunkSize:65536,windowBits:15,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&!(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Gt,this.strm.avail_out=0;var n=yn(this.strm,t.windowBits);if(n!==Dn)throw new Error(Te[n]);if(this.header=new En,function(e,t){if(dn(e))return rn;var n=e.state;2&n.wrap&&(n.head=t,t.done=!1)}(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=At(t.dictionary):"[object ArrayBuffer]"===Sn.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=wn(this.strm,t.dictionary))!==Dn))throw new Error(Te[n])}function Cn(e,t){var n=new Mn(t);if(n.push(e),n.err)throw n.msg||Te[n.err];return n.result}Mn.prototype.push=function(e,t){var n,i,r,o=this.strm,a=this.options.chunkSize,s=this.options.dictionary;if(this.ended)return!1;for(i=t===~~t?t:!0===t?xn:kn,"[object ArrayBuffer]"===Sn.call(e)?o.input=new Uint8Array(e):o.input=e,o.next_in=0,o.avail_in=o.input.length;;){for(0===o.avail_out&&(o.output=new Uint8Array(a),o.next_out=0,o.avail_out=a),(n=bn(o,i))===Tn&&s&&((n=wn(o,s))===Dn?n=bn(o,i):n===Gn&&(n=Tn));o.avail_in>0&&n===An&&o.state.wrap>0&&0!==e[o.next_in];)mn(o),n=bn(o,i);switch(n){case Rn:case Gn:case Tn:case On:return this.onEnd(n),this.ended=!0,!1}if(r=o.avail_out,o.next_out&&(0===o.avail_out||n===An))if("string"===this.options.to){var c=Rt(o.output,o.next_out),u=o.next_out-c,l=Tt(o.output,c);o.next_out=u,o.avail_out=a-u,u&&o.output.set(o.output.subarray(c,c+u),0),this.onData(l)}else this.onData(o.output.length===o.next_out?o.output:o.output.subarray(0,o.next_out));if(n!==Dn||0!==r){if(n===An)return n=zn(this.strm),this.onEnd(n),this.ended=!0,!0;if(0===o.avail_in)break}}return!0},Mn.prototype.onData=function(e){this.chunks.push(e)},Mn.prototype.onEnd=function(e){e===Dn&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=St(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var In,Bn={Inflate:Mn,inflate:Cn,inflateRaw:function(e,t){return(t=t||{}).raw=!0,Cn(e,t)},ungzip:Cn,constants:Re},Un={Deflate:Ht.Deflate,deflate:Ht.deflate,deflateRaw:Ht.deflateRaw,gzip:Ht.gzip,Inflate:Bn.Inflate,inflate:Bn.inflate,inflateRaw:Bn.inflateRaw,ungzip:Bn.ungzip,constants:Re},Ln=function(){function e(e,t){var n=this;this.collector=e,this.config=t,this.requestType=t.request_type||"xhr",this.supportBeacon=!(!window.navigator||!window.navigator.sendBeacon),this.errorCode={NO_URL:4001,IMG_ON:4e3,IMG_CATCH:4002,BEACON_FALSE:4003,XHR_ON:500,RESPONSE:5001,TIMEOUT:5005},this.customHeader=t.custom_request_header||{},this.collector.on("custom-request-header",(function(e){n.setRequestHeader(e)}))}return e.prototype.setRequestHeader=function(e){this.customHeader=Object.assign(this.customHeader,e)},e.prototype.useFetch=function(e){var t=e.url,n=e.data,i=e.method,r=e.success,o=e.fail,a={"Content-Type":"application/json; charset=utf-8"};if(Object.keys(this.customHeader).length)for(var s in this.customHeader)a[s]=this.customHeader[s];window.fetch?fetch(t,{method:i||"POST",headers:a,body:JSON.stringify(n)}).then((function(e){return e.json()})).then((function(e){r&&r(e)})).catch((function(e){o&&o(n,e)})):(this.requestType="xhr",console.log("your brwoser not support fetch, use xhr"),this.useRequest({url:t,data:n,method:i,success:r,fail:o}))},e.prototype.useBeacon=function(e){var t=e.url,n=e.data,i=e.success,r=e.fail;window.navigator.sendBeacon(t,JSON.stringify(n))?i&&i():r&&r(n,this.errorCode.BEACON_FALSE)},e.prototype.useRequest=function(e){var t=this,n=e.url,i=e.data,r=e.method,o=e.success,a=e.fail,s=e.timeout,c=e.useBeacon,u=e.withCredentials,l=e.app_key,h=e.forceXhr,f=e.zip;if(c&&this.supportBeacon)this.useBeacon({url:n,data:i,method:r,success:o,fail:a});else if("fetch"!==this.requestType||h)try{var d=new XMLHttpRequest,p=r||"POST";if(d.open(p,""+n,!0),d.setRequestHeader("Content-Type","application/json; charset=utf-8"),l&&d.setRequestHeader("X-MCS-AppKey",""+l),Object.keys(this.customHeader).length)for(var g in this.customHeader)d.setRequestHeader(g,this.customHeader[g]);u&&(d.withCredentials=!0),s&&(d.timeout=s,d.ontimeout=function(){a&&a(i,t.errorCode.TIMEOUT)}),d.onload=function(){if(o){var e=null;if(d.responseText){try{e=JSON.parse(d.responseText)}catch(t){e={}}o(e,i)}}},d.onerror=function(){d.abort(),a&&a(i,t.errorCode.XHR_ON)};try{var _=JSON.stringify(i);if(this.config.need_zip&&f){var v=(new TextEncoder).encode(_),m=Un.gzip(v);d.setRequestHeader("Content-Encoding","gzip"),d.send(m)}else d.send(_)}catch(e){this.collector.emit("sdk-inner-error",{type:"sdk_error_send",eventData:i,errorCode:1e3,msg:e.message||e.stack||"sdk_error_send"})}}catch(e){this.collector.emit("sdk-inner-error",{type:"sdk_error_request",eventData:i,errorCode:1001,msg:e.message||e.stack||"sdk_error_request"})}else this.useFetch({url:n,data:i,method:r,success:o,fail:a})},e}(),Nn=function(){function e(e,t){if(this.devToolReady=!1,this.devToolOrigin="*",this.sendAlready=!1,!t.disable_debug&&"Microsoft Internet Explorer"!==e.configManager.get("browser")){this.collect=e,this.config=t,this.app_id=t.app_id;var n=e.adapters.storage;this.cacheStorgae=new n(!1),this.loadUrl=t.devtool_url||"https://lf-static.applogcdn.com/obj/applog-sdk-static/log-sdk/collect/devtool/debug-web.v2.0.0.js",this.filterEvent=new Set(["__bav_page","__bav_beat","__bav_page_statistics","__bav_click","__bav_page_exposure","bav2b_page","bav2b_beat","bav2b_page_statistics","bav2b_click","bav2b_page_exposure","_be_active","predefine_pageview","__profile_set","__profile_set_once","__profile_increment","__profile_unset","__profile_append","predefine_page_alive","predefine_page_close","abtest_exposure"]),this.load()}}return e.prototype.loadScript=function(e){try{var t=document.createElement("script");t.src=e,t.onerror=function(){console.log("load DevTool render fail")},t.onload=function(){console.log("load DevTool render success")},document.getElementsByTagName("body")[0].appendChild(t)}catch(e){console.log("devTool load fail, "+e.message)}},e.prototype.parseUrl=function(){var e={};try{var t=window.location.href.split("?")[1].split("&");t.length&&t.forEach((function(t){var n=t.split("=");e[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}))}catch(e){}return e},e.prototype.load=function(){try{var e=this.parseUrl();if(e.open_devtool_web&&e.app_id){if(parseInt(e.app_id)!==this.app_id)return}else if(!this.getStorage())return;this.loadBaseInfo(),this.loadHook(),this.setStorage(),this.addLintener(),this.loadDebuggerModule(),this.loadDevTool()}catch(e){console.log("debug fail, "+e.message)}},e.prototype.getStorage=function(){var e=this.cacheStorgae.getItem("__applog_open_devtool_web");return e&&parseInt(e)===this.app_id},e.prototype.setStorage=function(){this.cacheStorgae.setItem("__applog_open_devtool_web",this.app_id)},e.prototype.loadDevTool=function(){this.loadScript(this.loadUrl)},e.prototype.loadBaseInfo=function(){if(this.log=[],this.event=[],this.info=[{title:"基本信息",key:"base",type:1,content:[{name:"app_id",desc:"APP_ID",value:this.config.app_id},{name:"channel",desc:"CHANNEL",value:this.config.channel},{name:"domain",desc:"上报域名",value:this.collect.configManager.getDomain()},{name:"sdk_version",desc:"SDK版本",value:R},{name:"sdk_type",desc:"SDK引入方式",value:"npm"}]},{title:"用户信息",key:"user",type:2,content:[{name:"uuid",desc:"UUID",value:this.collect.configManager.get("user").user_unique_id||""},{name:"web_id",desc:"WEB_ID",value:this.collect.configManager.get("user").web_id||""}]},{title:"公共参数信息",type:2,key:"common",content:[{name:"browser",desc:"浏览器",value:this.collect.configManager.get("browser")},{name:"browser_version",desc:"浏览器版本",value:this.collect.configManager.get("browser_version")},{name:"platform",desc:"平台",value:this.collect.configManager.get("platform")},{name:"device_model",desc:"设备型号",value:this.collect.configManager.get("device_model")},{name:"os_name",desc:"操作系统",value:this.collect.configManager.get("os_name")},{name:"os_version",desc:"操作系统版本",value:this.collect.configManager.get("os_version")},{name:"resolution",desc:"屏幕分辨率",value:this.collect.configManager.get("resolution")},{name:"referrer",desc:"来源",value:this.collect.configManager.get("referrer")},{name:"custom",desc:"自定义信息",value:JSON.stringify(this.collect.configManager.get("custom"))}]},{title:"配置信息",key:"config",type:3,content:[{name:"autotrack",desc:"全埋点",value:!!this.config.autotrack},{name:"stay",desc:"停留时长",value:!!this.config.enable_stay_duration}]},{title:"A/B配置信息",key:"ab",type:4,content:[{name:"ab",desc:"A/B实验",value:!!this.config.enable_ab_test}]}],this.config.enable_ab_test){var e=this.getInfo("ab"),t=[{name:"ab",desc:"A/B实验",value:!!this.config.enable_ab_test},{name:"vid",desc:"已曝光VID",value:this.collect.configManager.getAbVersion()},{name:"ab_domain",desc:"A/B域名",value:this.config.ab_channel_domain||d(A[this.config.channel])},{name:"all_values",desc:"全部配置",value:this.collect.configManager.getAbCache()}];e.content=t,this.setInfo("ab",e)}if(this.config.enable_native||this.config.Native){var n={title:"客户端信息",type:3,content:[{name:"native",desc:"是否打通",value:!!this.collect.bridgeReport}]};this.setInfo("native",n)}if(R.includes("tob")){var i=this.getInfo("user");i.content.push({name:"sid",desc:"DISS".split("").reverse().join(""),value:"点击获取"}),this.setInfo("user",i)}},e.prototype.getInfo=function(e){var t=null;return this.info.forEach((function(n){n.key===e&&(t=n)})),t},e.prototype.setInfo=function(e,t){var n=!1;this.info.forEach((function(i){i.key===e&&(n=!0,i=t)})),n||this.info.push(t)},e.prototype.getSecondInfo=function(e,t){var n=this.getInfo(e),i=null;return n.content.forEach((function(e){e.name===t&&(i=e)})),i},e.prototype.setSecondInfo=function(e,t,n){var i=this.getInfo(e),r=!1;if(i.content.forEach((function(e){e.name===t&&(e.value=n,r=!0)})),!r){var o={name:t,value:n,desc:t.toLocaleUpperCase()};i.content.push(o)}this.setInfo(e,i)},e.prototype.loadHook=function(){var e=this;this.collect.on(s.DEBUGGER_MESSAGE,(function(t){switch(t.type){case s.DEBUGGER_MESSAGE_SDK:var n={time:t.time,type:t.logType||"sdk",infoType:t.infoType||"sdk",level:t.level,name:t.info,show:!0,levelShow:!0,needDesc:!!t.data};if(t.data&&(n.desc={content:JSON.stringify(t.data)}),e.updateLog(n),t.secType&&"AB"===t.secType)e.setSecondInfo("ab","vid",e.collect.configManager.getAbVersion()),e.setSecondInfo("ab","all_values",e.collect.configManager.getAbCache());else if("USER"===t.secType)e.setSecondInfo("user","user_unique_id"===t.common?"uuid":t.common,e.collect.configManager.get(t.common));else if("HEADER"===t.secType){var i=e.collect.configManager.get(t.common);e.setSecondInfo("common",t.common,"custom"===t.common?JSON.stringify(i):i)}return void e.updateInfo();case s.DEBUGGER_MESSAGE_EVENT:if(t.data&&t.data.length){var r=t.data[0],o=r.events;if(!o.length)return;o.forEach((function(n){n.checkShow=!0,n.searchShow=!0,n.focusShow=!0,n.status=t.status,n.type=e.filterEvent.has(n.event)?"sdk":"cus",n.verifyType=t.verifyType?"verify":"no",n.info="","fail"===t.status&&(n.info={message:"code: "+t.code+"， msg: "+(t.msg||t.failType)})})),e.updateEvent(r)}return}}))},e.prototype.addLintener=function(){var e=this;window.addEventListener("message",(function(t){if(t&&t.data&&"devtool:web:ready"===t.data.type){if(e.devToolOrigin=t.origin,e.devToolReady=!0,e.sendAlready)return;e.sendData("devtool:web:init",{info:e.info,log:e.log,event:e.event,sdk_type:R.includes("tob")?"tob":"inner",appId:e.app_id}),e.sendAlready=!0}t&&t.data&&"devtool:web:ssid"===t.data.type&&e.collect.getToken((function(t){e.setSecondInfo("user","sid",t.tobid),e.updateInfo()}))}))},e.prototype.sendData=function(e,t){try{var n={type:e,payload:t};window&&window.postMessage(n,this.devToolOrigin)}catch(e){}},e.prototype.updateInfo=function(){this.devToolReady&&this.sendData("devtool:web:info",this.info)},e.prototype.updateLog=function(e){this.devToolReady?this.sendData("devtool:web:log",e):this.log.push(e)},e.prototype.updateEvent=function(e){this.devToolReady?this.sendData("devtool:web:event",e):this.event.push(e)},e.prototype.loadDebuggerModule=function(){var e="#debugger-applog-web {\n      position: fixed;\n      width: 45px;\n      height: 45px;\n      background-image: url("+d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z1az1bz1lz49z22z1mz21z4az19z27z22z1cz21z1az1kz4az1az1mz1kz4bz1mz19z1hz4bz21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz1bz1cz24z22z1mz1mz1jz4bz18z1nz1nz1jz1mz1ez4az1nz1lz1e")+");\n      bottom: 5%;\n      right: 10%;\n      cursor: pointer;\n      z-index:100;\n      background-size: 45px;\n    }",t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.appendChild(document.createTextNode(e)),t.appendChild(n);var i=document.createElement("div");i.innerHTML='<div id="debugger-applog-web" class="debugger-applog-web"></div>';var r=document.createElement("div");r.innerHTML='<div id="debugger-container" class="debugger-container"></div>',document.getElementsByTagName("body")[0].appendChild(i),document.getElementsByTagName("body")[0].appendChild(r);var o=document.getElementById("debugger-applog-web");o&&o.addEventListener("click",(function(){window&&window.postMessage({type:"devtool:web:open-draw"},location.origin)}))},e}(),jn={autotrack:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz18z23z22z1mz22z20z18z1az1iz4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/autotrack.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/autotrack.js"},object:"LogAutoTrack"},ab:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz18z19z4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/ab.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/ab.js"},object:"LogAb"},stay:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz21z22z18z27z4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/stay.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/stay.js"},object:"LogStay"},route:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz20z1mz23z22z1cz4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/route.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/route.js"},object:"LogRoute"},tracer:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz22z20z18z1az1cz20z4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/tracer.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/tracer.js"},object:"LogTracer"},retry:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz20z1cz22z20z27z4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/retry.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/retry.js"},object:"LogRetry"},visual:{src:{cn:d("1fz22z22z1nz21z4mz4bz4bz1jz1dz49z21z22z18z22z1gz1az4az18z1nz1nz1jz1mz1ez1az1bz1lz4az1az1mz1kz4bz1mz19z1hz4bz18z1nz1nz1jz1mz1ez49z21z1bz1iz49z21z22z18z22z1gz1az4bz1jz1mz1ez49z21z1bz1iz4bz1az1mz1jz1jz1cz1az22z4bz4hz4bz1nz1jz23z1ez1gz1lz4bz24z1gz21z23z18z1jz4az1hz21"),sg:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-sg/log-sdk/collect/5/plugin/visual.js",va:"https://lf-global-static.iapplogcdn.com/obj/applog-sdk-static-us/log-sdk/collect/5/plugin/visual.js"},object:"LogVisual"}},Pn=["ab","autotrack","Stay","store","et","profile","cep","heartbeat","monitor","route","tracer"],Kn=function(){function e(e){this.disableAutoPageView=!1,this.bridgeReport=!1,this.staging=!1,this.pluginInstances=[],this.sended=!1,this.started=!1,this.destroy=!1,this.sdkReady=!1,this.adapters={},this.loadType="base",this.sdkStop=!1,this.name=e,this.hook=new u,this.remotePlugin=new Map,this.Types=M,this.adapters.storage=x,this.loadType="full"}return e.usePlugin=function(t,n,i){if(n){for(var r=!1,o=0,a=e.plugins.length;o<a;o++)if(e.plugins[o].name===n){e.plugins[o].plugin=t,e.plugins[o].options=i||{},r=!0;break}r||e.plugins.push({name:n,plugin:t,options:i})}else e.plugins.push({plugin:t})},e.prototype.usePlugin=function(e,t,n){e&&("full"===this.loadType&&Pn.includes(e)?console.info("your sdk version has "+e+" plugin already ~"):t?"string"==typeof t?this.remotePlugin.get(e)||this.remotePlugin.set(e,{src:t,call:n}):this.remotePlugin.get(e)||this.remotePlugin.set(e,{instance:t}):this.remotePlugin.get(e)||this.remotePlugin.set(e,"sdk"))},e.prototype.init=function(t){var n,i=this;this.logger=new N(this.name,t.log),this.inited?this.logger.warn("[instance: "+this.name+"], every instance's api: init,  can be call only one time!"):t&&l(t)?t.app_id&&"number"==typeof(n=t.app_id)&&!isNaN(n)?!t.app_key||function(e){return"string"==typeof e}(t.app_key)?(t.channel_domain||-1!==["cn","sg","va","my"].indexOf(t.channel)||(this.logger.warn("channel must be `cn`, `sg`,`va`, `my` !"),t.channel="cn"),this.spider=new j,this.spider.checkSpider(t)?this.logger.warn("your env may be a spider, can not report!"):(this.appBridge=new P(this,t.enable_native),this.requestManager=new Ln(this,t),this.bridgeReport=this.appBridge.bridgeInject(),this.configManager=new L(this,t),this.debugger=new Nn(this,t),this.initConfig=t,this.emit(M.Init),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行Init",data:t,level:"info",time:Date.now(),infoType:"cus"}),this.destroy=!1,t.disable_auto_pv&&(this.disableAutoPageView=!0),this.bridgeReport||(this.configManager.set({app_id:t.app_id}),this.eventManager=new H,this.tokenManager=new F,this.sessionManager=new q,Promise.all([new Promise((function(e){i.once(M.TokenComplete,(function(){e(!0)}))})),new Promise((function(e){i.once(M.Start,(function(){e(!0)}))}))]).then((function(){try{e.plugins.reduce((function(e,t){var n=t.plugin,r=t.options,o=Object.assign(i.initConfig,r),a=new n;return a.apply(i,o),e.push(a),i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK加载"+t.name+"插件",level:"info",time:Date.now(),infoType:"sdk"}),e}),i.pluginInstances)}catch(e){console.warn("load plugin error, "+e.message),i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK加载插件发生了异常",level:"error",time:Date.now(),data:e.message,infoType:"sdk"})}i.sdkReady=!0,i.emit(M.Ready),i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 用户信息初始化完成",time:Date.now(),secType:"USER",level:"info",data:i.configManager.get("user"),infoType:"sdk"}),i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 初始化完成",time:Date.now(),level:"info",data:i.configManager.get("user"),infoType:"sdk"}),i.logger.info("appid: "+t.app_id+", userInfo:"+JSON.stringify(i.configManager.get("user"))),i.logger.info("appid: "+t.app_id+", sdk is ready, version type is "+i.loadType+", version is "+R+", you can report now !!!"),t.disable_auto_pv&&(i.disableAutoPageView=!0);try{"full"===i.loadType&&(t.enable_ab_test||t.autotrack)&&(window.opener||window.parent).postMessage("[tea-sdk]ready","*")}catch(e){i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK发生了异常",level:"error",time:Date.now(),data:e.message,infoType:"sdk"})}i.pageView(),i.on(M.TokenChange,(function(e){"webid"===e&&i.pageView(),i.logger.info("appid: "+t.app_id+" token change, new userInfo:"+JSON.stringify(i.configManager.get("user"))),i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 设置了用户信息",time:Date.now(),secType:"USER",level:"info",data:i.configManager.get("user"),infoType:"sdk"})})),i.on(M.TokenReset,(function(){i.logger.info("appid: "+t.app_id+" token reset, new userInfo:"+JSON.stringify(i.configManager.get("user"))),i.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 重置了用户信息",time:Date.now(),secType:"USER",level:"info",data:i.configManager.get("user"),infoType:"sdk"})})),i.on(M.RouteChange,(function(e){e.init||t.disable_route_report||i.pageView()}))})),this.tokenManager.apply(this,t),this.eventManager.apply(this,t),this.sessionManager.apply(this,t)),this.inited=!0)):this.logger.warn("app_key param is error, must be string, please check!"):this.logger.warn("app_id param is error, must be number, please check!"):this.logger.warn("init params error,please check!")},e.prototype.config=function(e){if(!this.inited)return console.warn("config must be use after function init"),void this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK执行Config发生了异常",level:"error",time:Date.now(),data:"config must be use after function init",infoType:"sdk"});if(!e||!l(e))return console.warn("config params is error, please check"),void this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK执行Config发生了异常",level:"error",time:Date.now(),data:"config params is error, please check",infoType:"sdk"});if(this.bridgeReport)this.appBridge.setConfig(e);else{e._staging_flag&&1===e._staging_flag&&(this.staging=!0),e.disable_auto_pv&&(this.disableAutoPageView=!0,delete e.disable_auto_pv);var n=t({},e),i=this.initConfig.configPersist||this.initConfig.config_persist||!1;if(this.initConfig&&i){var r=this.configManager.getStore();r&&(n=Object.assign(r,e)),this.configManager.setStore(e)}n.web_id,n.user_unique_id;var o=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&(n[i[r]]=e[i[r]])}return n}(n,["web_id","user_unique_id"]);this.configManager.set(o),n.hasOwnProperty("web_id")&&this.emit(M.ConfigWebId,n.web_id),n.hasOwnProperty("user_unique_id")&&this.emit(M.ConfigUuid,n.user_unique_id),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行Config",level:"info",time:Date.now(),data:n,infoType:"cus"})}},e.prototype.setUserUniqueID=function(e){this.config({user_unique_id:e})},e.prototype.setHeaderInfo=function(e,t){var n={};n[e]=t,this.config(n)},e.prototype.removeHeaderInfo=function(e){var t={};t[e]=void 0,this.config(t)},e.prototype.setDomain=function(e){this.configManager&&this.configManager.setDomain(e),this.emit(M.ConfigDomain)},e.prototype.getConfig=function(e){var t;return null===(t=this.configManager)||void 0===t?void 0:t.get(e)},e.prototype.send=function(){this.start()},e.prototype.start=function(){this.inited&&!this.sended&&(this.sended=!0,this.started=!0,this.emit(M.Start),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行Start",level:"info",time:Date.now(),infoType:"cus"}),this.bridgeReport&&(this.pageView(),this.emit(M.Ready)))},e.prototype.event=function(e,t){var n=this;try{var i=[];if(Array.isArray(e))e.forEach((function(e){var t=n.processEvent(e[0],e[1]||{});t&&i.push(t)}));else{var r=this.processEvent(e,t);if(!r)return;i.push(r)}this.bridgeReport?i.forEach((function(e){var t=e.event,i=e.params;n.appBridge.onEventV3(t,JSON.stringify(i))})):i.length&&(this.emit(M.Event,i),this.emit(M.SessionResetTime)),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行Event",level:"info",time:Date.now(),data:e,infoType:"cus"})}catch(e){this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行Event发生了异常",level:"error",time:Date.now(),data:e.message,infoType:"sdk"})}},e.prototype.beconEvent=function(e,t){if(Array.isArray(e))console.warn("beconEvent not support batch report, please check");else{var n=[],i=this.processEvent(e,t||{});i&&(n.push(i),n.length&&(this.emit(M.BeconEvent,n),this.emit(M.SessionResetTime)),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行Beacon Event",level:"info",time:Date.now(),data:e,infoType:"sdk"}))}},e.prototype.beaconEvent=function(e,t){this.beconEvent(e,t)},e.prototype.processEvent=function(e,t){void 0===t&&(t={});try{if(!e)return console.warn("eventName is null， please check"),null;var n=e;/^event\./.test(e)&&(n=e.slice(6));var i=t;"object"!=typeof i&&(i={}),i.profile?delete i.profile:i.event_index=f();var r=void 0;return i.local_ms?(r=i.local_ms,delete i.local_ms):r=+new Date,{event:n,params:i,local_time_ms:r,is_bav:this.initConfig&&this.initConfig.autotrack?1:0}}catch(n){return this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK发生了异常",level:"error",time:Date.now(),data:n.message,infoType:"sdk"}),{event:e,params:t}}},e.prototype.filterEvent=function(e){this.eventFilter=e},e.prototype.on=function(e,t){this.hook.on(e,t)},e.prototype.once=function(e,t){this.hook.once(e,t)},e.prototype.off=function(e,t){this.hook.off(e,t)},e.prototype.emit=function(e,t,n){this.hook.emit(e,t,n)},e.prototype.set=function(e){this.hook.set(e)},e.prototype.stop=function(){this.sdkStop=!0},e.prototype.reStart=function(){this.sdkStop=!1},e.prototype.pageView=function(){this.disableAutoPageView||this.predefinePageView()},e.prototype.predefinePageView=function(e){if(void 0===e&&(e={}),this.inited){var n={title:document.title||location.pathname,url:location.href,url_path:this.handlePath(),time:Date.now(),referrer:window.document.referrer,$is_first_time:""+(this.configManager&&this.configManager.is_first_time||!1)},i=t(t({},n),e);this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 上报PV",level:"info",time:Date.now(),infoType:"sdk"}),this.event("predefine_pageview",i)}else console.warn("predefinePageView must be use after function init")},e.prototype.handlePath=function(){var e=location.pathname;try{if(this.initConfig.allow_hash){var t=new URL(location.href).hash.match(/#\/([^?#]+)/i);return t&&t.length?t[1]:location.pathname}}catch(t){e=location.pathname}return e},e.prototype.clearEventCache=function(){this.emit(M.CleanEvents)},e.prototype.setWebIDviaUnionID=function(e){if(e){var t=y(e);this.config({web_id:""+t,wechat_unionid:e}),this.emit(M.CustomWebId)}},e.prototype.setWebId=function(e){this.config({web_id:""+e})},e.prototype.setWebIDviaOpenID=function(e){if(e){var t=y(e);this.config({web_id:""+t,wechat_openid:e}),this.emit(M.CustomWebId)}},e.prototype.setNativeAppId=function(e){this.bridgeReport&&this.appBridge.setNativeAppId(e)},e.prototype.setRequestHeaders=function(e){this.emit(M.CustomHeader,e)},e.prototype.getSessionId=function(){return K()},e.prototype.setSessionId=function(e){this.emit(M.SessionReset,e)},e.prototype.resetStayDuration=function(e,t,n){this.emit(M.ResetStay,{url_path:e,title:t,url:n},M.Stay)},e.prototype.resetStayParams=function(e,t,n){void 0===e&&(e=""),void 0===t&&(t=""),void 0===n&&(n=""),this.emit(M.SetStay,{url_path:e,title:t,url:n},M.Stay)},e.prototype.getToken=function(e,n){var i=this;if(this.inited){var r=!1,o=function(n){if(!r){r=!0;var o=i.configManager.get().user;return n&&(o.tobid=n,o["diss".split("").reverse().join("")]=n),e(t({},o))}},a=function(){i.tokenManager.getTobId().then((function(e){o(e)}))};this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行GetToken",level:"info",time:Date.now(),infoType:"cus"}),this.sdkReady?a():(n&&setTimeout((function(){o()}),n),this.on(M.Ready,(function(){a()})))}else console.warn("getToken must be use after function init")},e.prototype.profileSet=function(e){this.bridgeReport?this.appBridge.profileSet(JSON.stringify(e)):this.emit(M.ProfileSet,e,M.Profile),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileSet",level:"info",time:Date.now(),data:e,infoType:"cus"})},e.prototype.profileSetOnce=function(e){this.bridgeReport?this.appBridge.profileSetOnce(JSON.stringify(e)):this.emit(M.ProfileSetOnce,e,M.Profile),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileSetOnce",level:"info",time:Date.now(),data:e,infoType:"cus"})},e.prototype.profileIncrement=function(e){this.bridgeReport?this.appBridge.profileIncrement(JSON.stringify(e)):this.emit(M.ProfileIncrement,e,M.Profile),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileIncrement",level:"info",time:Date.now(),data:e,infoType:"cus"})},e.prototype.profileUnset=function(e){this.bridgeReport?this.appBridge.profileUnset(e):this.emit(M.ProfileUnset,e,M.Profile),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileUnset",level:"info",time:Date.now(),data:e,infoType:"cus"})},e.prototype.profileAppend=function(e){this.bridgeReport?this.appBridge.profileAppend(JSON.stringify(e)):this.emit(M.ProfileAppend,e,M.Profile),this.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行profileAppend",level:"info",time:Date.now(),data:e,infoType:"cus"})},e.prototype.setExternalAbVersion=function(e){this.emit(M.AbExternalVersion,"string"==typeof e&&e?(""+e).trim():null,M.Ab)},e.prototype.getVar=function(e,t,n){this.emit(M.AbVar,{name:e,defaultValue:t,callback:n},M.Ab)},e.prototype.getABconfig=function(e,t){this.emit(M.AbConfig,{params:e,callback:t},M.Ab)},e.prototype.getAbSdkVersion=function(){return this.configManager.getAbVersion()},e.prototype.onAbSdkVersionChange=function(e){var t=this;return this.emit(M.AbVersionChangeOn,e,M.Ab),function(){t.emit(M.AbVersionChangeOff,e,M.Ab)}},e.prototype.offAbSdkVersionChange=function(e){this.emit(M.AbVersionChangeOff,e,M.Ab)},e.prototype.openOverlayer=function(){this.emit(M.AbOpenLayer,"",M.Ab)},e.prototype.closeOverlayer=function(){this.emit(M.AbCloseLayer,"",M.Ab)},e.prototype.getAllVars=function(e){this.emit(M.AbAllVars,e,M.Ab)},e.prototype.destoryInstace=function(){this.destroy||(this.destroy=!0,this.off(M.TokenComplete),this.emit(M.DestoryInstance))},e.prototype.destroyInstance=function(){this.destroy||(this.destroy=!0,this.off(M.TokenComplete),this.emit(M.DestoryInstance))},e.plugins=[],e}(),qn="__rangers_ab_style__";!function(e){e[e.Var=0]="Var",e[e.All=1]="All"}(In||(In={}));var Hn=function(){function e(){this.fetchStatus="no",this.refreshFetchStatus="complete",this.versions=[],this.extVersions=[],this.mulilinkVersions=[],this.enable_multilink=!1,this.enable_ab_visual=!1,this.editMode=!1,this.callbacks=[],this.data=null,this.changeListener=new Map,this.readyStatus=!1,this.exposureCache=[]}return e.prototype.apply=function(e,t){var n=this;if(t.enable_ab_test){this.collect=e,this.config=t;var i=t.enable_multilink,r=t.ab_channel_domain,o=t.enable_ab_visual,a=t.ab_cross,s=t.ab_cookie_domain,c=t.ab_timeout,u=r||d(A[t.channel||"cn"]),l=e.adapters.storage,h=this.collect.Types;this.types=h,this.cacheStorgae=new l(!1),this.timeout=c||3e3,this.enable_multilink=i,this.enable_ab_visual=o,this.abKey="__tea_sdk_ab_version_"+t.app_id,this.ab_cross=a,this.ab_cookie_domain=s||"",this.fetchUrl=u+"/service/2/abtest_config/",this.reportUrl=""+e.configManager.getUrl("event"),this.exposureLimit=t.exposure_limit||20,this.ab_batch_time=t.ab_batch_time||500,this.ab_user_mode=t.ab_user_mode||"user_unique_id",this.callbackMap=new Map,this.collect.on(h.TokenChange,(function(e){"uuid"===e&&n.readyStatus&&(n.clearCache(),n.fetchAB())})),this.collect.on(h.TokenReset,(function(e){"uuid"===e&&n.readyStatus&&(n.clearCache(),n.fetchAB())})),this.collect.on(h.AbVar,(function(e){var t=e.name,i=e.defaultValue,r=e.callback;n.getVar(t,i,r)})),this.collect.on(h.AbAllVars,(function(e){n.getAllVars(e)})),this.collect.on(h.AbConfig,(function(e){var t=e.params,i=e.callback;n.getABconfig(t,i)})),this.collect.on(h.AbExternalVersion,(function(e){n.setExternalAbVersion(e)})),this.collect.on(h.AbOpenLayer,(function(){n.openOverlayer()})),this.collect.on(h.AbCloseLayer,(function(){n.closeOverlayer()})),this.collect.on(h.AbVersionChangeOn,(function(e){n.changeListener.set(e,e)})),this.collect.on(h.AbVersionChangeOff,(function(e){n.changeListener.get(e)&&n.changeListener.delete(e)})),this.loadMode(),(this.enable_ab_visual||this.enable_multilink)&&this.openOverlayer(this.config.multilink_timeout_ms||500),this.checkLocal(),this.ready("ab"),this.readyStatus||(this.fetchAB(),this.readyStatus=!0),this.collect.emit(h.AbReady)}},e.prototype.ready=function(e){var t=this;if(this.collect.set(e),this.collect.hook._hooksCache.hasOwnProperty(e)){var n=this.collect.hook._hooksCache[e];if(!Object.keys(n).length)return;var i=function(e){n[e].length&&n[e].forEach((function(n){t.collect.hook.emit(e,n)}))};for(var r in n)i(r)}},e.prototype.loadMode=function(){var e=function(){try{return JSON.parse(atob(window.name))||void 0}catch(e){return}}(),t="";if(e){var n=e.scenario,i=e.href;n?(this.editMode=!0,t=n):!i||-1===i.indexOf("datatester")&&-1===i.indexOf("visual-editor")||(this.editMode=!0,t="visual-editor")}this.enable_ab_visual&&"visual-editor"===t&&this.collect.destoryInstace()},e.prototype.checkLocal=function(){var e=this.getABCache(),t=e.ab_version,n=e.ab_ext_version,i=e.ab_version_multilink,r=e.data,o=this.checkFromUrl();o?this.mulilinkVersions.push(o):this.mulilinkVersions=i||[],this.extVersions=n||[],this.versions=t||[],this.data=r;var a=this.versions.concat(this.extVersions);this.enable_multilink&&(a=a.concat(this.mulilinkVersions)),this.configVersions(a.join(","))},e.prototype.checkFromUrl=function(){var e=m(window.location.href);return e&&e.vid?e.vid:""},e.prototype.updateVersions=function(){var e=this.extVersions.length?this.versions.concat(this.extVersions):this.versions;this.enable_multilink&&(e=e.concat(this.mulilinkVersions)),this.configVersions(e.join(",")),this.updateABCache(),this.changeListener.size>0&&this.changeListener.forEach((function(t){"function"==typeof t&&t(e)}))},e.prototype.configVersions=function(e){this.collect.configManager.setAbVersion(e)},e.prototype.getVar=function(e,t,n){if(!e)throw new Error("variable must not be empty");if(void 0===t)throw new Error("variable no default value");if("function"!=typeof n)throw new Error("callback must be a function");var i={name:e,defaultValue:t,callback:n,type:In.Var};this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK 调用GetVar",level:"info",time:Date.now(),data:e,infoType:"cus"}),"complete"===this.fetchStatus&&"complete"===this.refreshFetchStatus?this.getRealVar(i,e):(this.callbackMap.get(e),this.callbackMap.set(e,i))},e.prototype.getRealVar=function(e,t){var n=e.name,i=e.defaultValue,r=e.callback,o=this.data;if(o){var a=o[n];if(a&&l(a)){var c=o[n].vid;return"$ab_url"===t?this.mulilinkVersions.includes(c)||this.mulilinkVersions.push(c):this.versions.includes(c)||this.versions.push(c),this.updateVersions(),this.fechEvent(c,t||n,i,r,a.val),void this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK执行GetVar，并曝光了实验"+n,level:"info",time:Date.now(),data:o[n],infoType:"sdk"})}this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK执行GerVar默认回调",level:"info",time:Date.now(),data:this.data,infoType:"sdk"}),r(i)}else r(i)},e.prototype.getAllVars=function(e){if("function"!=typeof e)throw new Error("callback must be a function");var t={callback:e,type:In.All};this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK 调用GetAllVars",level:"info",time:Date.now(),infoType:"cus"}),"complete"===this.fetchStatus&&"complete"===this.refreshFetchStatus?this.getRealAllVars(t):this.callbackMap.set("allVars",t)},e.prototype.getRealAllVars=function(e){(0,e.callback)(this.data?JSON.parse(JSON.stringify(this.data)):{}),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"SDK 执行GetAllVars",level:"info",time:Date.now(),data:this.data,infoType:"sdk"})},e.prototype.fechEvent=function(e,t,n,i,r){try{if(this.config.disable_track_event)return;if(!e)return;var o=this.collect.configManager.get(),a=o.header,c=o.user,u=this.getABCache(),l=c[this.ab_user_mode]||c.user_unique_id;if(u&&u.uuid&&u.uuid!==l)return;var h={event:"abtest_exposure",ab_sdk_version:""+e,params:JSON.stringify({app_id:this.config.appId,ab_url:"$ab_url"===t?n:window.location.href}),local_time_ms:Date.now()};a.custom=JSON.stringify(a.custom);var f={events:[h],user:c,header:a};this.reportExposure(f,t),i&&i(r)}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 发生了异常",level:"error",time:Date.now(),data:e.message,infoType:"sdk"})}},e.prototype.reportExposure=function(e,t){var n=this;this.exposureCache.push(e),this.reportTimeout&&clearTimeout(this.reportTimeout),this.exposureCache.length>=this.exposureLimit?this.report(t):this.reportTimeout=setTimeout((function(){n.report(t),clearTimeout(n.reportTimeout)}),this.ab_batch_time)},e.prototype.report=function(e){var t=this;this.collect.requestManager.useRequest({url:this.reportUrl,data:this.exposureCache,timeout:2e4,useBeacon:"$ab_url"===e,zip:!0}),this.exposureCache.forEach((function(e){t.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_EVENT,info:"曝光埋点上报成功",time:Date.now(),data:[e],code:200,status:"success",infoType:"sdk"})})),this.exposureCache=[]},e.prototype.setExternalAbVersion=function(e){this.extVersions=[e],this.updateVersions()},e.prototype.getABconfig=function(e,t){var n=Object.keys(e);n&&n.length&&this.collect.configManager.set(e),this.fetchAB(t)},e.prototype.get=function(e){if(this.ab_cross){var t=this.cacheStorgae.getCookie(e,this.ab_cookie_domain);return t?JSON.parse(t):{}}return this.cacheStorgae.getItem(e)},e.prototype.set=function(e,t){this.ab_cross?this.cacheStorgae.setCookie(e,t,2592e6,this.ab_cookie_domain):this.cacheStorgae.setItem(e,t),this.collect.configManager.setAbCache(t)},e.prototype.getABCache=function(e){var t={ab_version:[],ab_ext_version:[],ab_version_multilink:[],data:null,timestamp:+new Date,uuid:""};return t=this.get(this.abKey)||t,e?t[e]:t},e.prototype.updateABCache=function(){var e=this.getABCache();e.ab_version_multilink=this.mulilinkVersions,e.ab_ext_version=this.extVersions,e.ab_version=this.versions,e.timestamp=Date.now(),this.set(this.abKey,e)},e.prototype.setAbCache=function(e){var t=this.getABCache();t.data=this.data,t.uuid=e,t.timestamp=Date.now(),this.set(this.abKey,t)},e.prototype.clearCache=function(){this.refreshFetchStatus="ing",this.data={},this.extVersions=[],this.mulilinkVersions=[],this.versions=[],this.collect.configManager.clearAbCache()},e.prototype.openOverlayer=function(e){var t=this;if(function(){if(!document.getElementById(qn)){var e="body { opacity: 0 !important; }",t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.id=qn,n.type="text/css",n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e)),t.appendChild(n)}}(),e)var n=setTimeout((function(){t.closeOverlayer(),clearTimeout(n)}),e)},e.prototype.closeOverlayer=function(){var e;(e=document.getElementById(qn))&&e.parentElement.removeChild(e)},e.prototype.fetchComplete=function(e,t){var n=this;try{if(e&&"[object Object]"==Object.prototype.toString.call(e)){var i=this.collect.configManager.get().user;if(t&&t!==i.user_unique_id)return;this.data=e,this.setAbCache(t);var r=[];Object.keys(e).forEach((function(t){var n=e[t].vid;n&&r.push(n)})),this.versions=this.versions.filter((function(e){return r.includes(e)}));var o=e.$ab_url,a=e.$ab_modification;if(a&&a.val&&this.enable_ab_visual){if(this.collect.destroy)return;this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行可视化实验",level:"info",logType:"fetch",time:Date.now(),infoType:"sdk",data:{$ab_modification:a}})}else if(o&&this.enable_multilink){this.mulilinkVersions=this.mulilinkVersions.filter((function(e){return r.includes(e)})),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行多链接实验",level:"info",logType:"fetch",time:Date.now(),infoType:"sdk",data:{$ab_url:o}});var c=o.val,u=o.vid;c&&u&&this.getVar("$ab_url",c,(function(){n.editMode||(c!==window.location.href?setTimeout((function(){if(!n.collect.destroy){var e=""+c;e=-1===e.indexOf("http")?"https://"+e:e,v(e).host!==location.host?e=e+"&vid="+u:window.history.replaceState("","",e),window.location.href=e}}),100):n.closeOverlayer())}))}this.updateVersions()}else this.closeOverlayer();this.callbackMap.forEach((function(e){return n[e.type===In.Var?"getRealVar":"getRealAllVars"](e,"")}))}catch(e){}},e.prototype.fetchAB=function(e){var n=this,i=this.collect.configManager.get(),r={header:t(t(t({aid:this.config.app_id},i.user||{}),i.header||{}),{ab_sdk_version:this.collect.configManager.getAbVersion(),ab_url:window.location.href})};this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 发起AB实验请求",level:"info",logType:"fetch",time:Date.now(),data:r,infoType:"sdk"});var o=i.user[this.ab_user_mode]||i.user.user_unique_id;this.collect.requestManager.useRequest({url:this.fetchUrl,data:r,success:function(t){n.fetchStatus="complete",n.refreshFetchStatus="complete";var i=t.data;"success"===t.message?(n.fetchComplete(i,o),e&&e(i)):(n.fetchComplete(null,o),e&&e(null)),n.collect.emit(n.types.AbComplete,i),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"AB实验请求成功",level:"info",logType:"fetch",time:Date.now(),data:i,infoType:"sdk"})},fail:function(){n.fetchStatus="complete",n.refreshFetchStatus="complete",n.fetchComplete(null,o),n.collect.emit(n.types.AbTimeout),e&&e(null),n.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,secType:"AB",info:"AB实验请求网络异常",level:"error",logType:"fetch",time:Date.now(),infoType:"sdk"})},timeout:this.timeout})},e}(),Vn=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;t.event_verify_url&&("string"==typeof t.event_verify_url?(this.url=t.event_verify_url+"/v1/list_test",e.on(e.Types.SubmitBefore,(function(t){e.requestManager.useBeacon({url:n.url,data:t}),e.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_EVENT,info:"SDK Report ByteIo Verify Request",time:Date.now(),data:t,code:200,status:"success",verifyType:!0})}))):console.log("please use correct et_test url"))},e}();function Fn(e,t,n){if(void 0===t&&(t="list"),!e)return!1;if(t&&"list"===t){if(["LI","TR","DL"].includes(e.nodeName))return!0;if(e.dataset&&e.dataset.hasOwnProperty("teaIdx"))return!0;if(e.hasAttribute&&e.hasAttribute("data-tea-idx"))return!0}else{if(["A","BUTTON"].includes(e.nodeName))return!0;if(e.dataset&&e.dataset.hasOwnProperty("teaContainer"))return!0;if(e.hasAttribute&&e.hasAttribute("data-tea-container"))return!0;if(n&&e.hasAttribute&&e.hasAttribute(n))return!0}return!1}var Wn=function(e,t){return!!Xn(e,t)};function Jn(e,t){for(var n=e;n&&!Fn(n,"container",t.container_attr||"");){if("HTML"===n.nodeName||"BODY"===n.nodeName)return e;n=n.parentElement}return n||e}function Zn(e){for(var t=e;t&&t.parentNode;){if(t.hasAttribute("data-tea-ignore"))return!0;if(t.className&&t.className.includes("applog-web-devtools-con"))return!0;if("HTML"===t.nodeName||"body"===t.nodeName)return!1;t=t.parentNode}return!1}var $n=function(e,t){return e.hasAttribute?e.hasAttribute(t):e.attributes?!(!e.attributes[t]||!e.attributes[t].specified):void 0},Xn=function(e,t){if("string"==typeof t)return $n(e,t);if(h(t)){for(var n=!1,i=0;i<t.length;i++)if($n(e,t[i])){n=!0;break}return n}},Yn=function(e,t){var n={};if("string"==typeof t)$n(e,t)&&(n[t]=e.getAttribute(t));else if(h(t))for(var i=0;i<t.length;i++)$n(e,t[i])&&(n[t[i]]=e.getAttribute(t[i]));return n};function Qn(e){var t=[];if(!e)return{element_path:"",positions:[]};for(;null!==e.parentElement;)t.push(e),e=e.parentElement;var n=[],i=[];return t.forEach((function(e){var t=function(e){if(null===e)return{str:"",index:0};var t=0,n=e.parentElement;if(n)for(var i=n.children,r=0;r<i.length&&i[r]!==e;r++)i[r].nodeName===e.nodeName&&t++;return{str:[e.nodeName.toLowerCase(),Fn(e,"list")?"[]":""].join(""),index:t}}(e),r=t.str,o=t.index;n.unshift(r),i.unshift(o)})),{element_path:"/"+n.join("/"),positions:i}}var ei=function(){function e(e,t){this.maxDuration=432e5,this.aliveDTime=6e4,this.options={aliveName:"predefine_page_alive",params:{},container:null},this.focusState=!0,this.collect=e,this.config=t,this.pageStartTime=Date.now(),this.sessionStartTime=this.pageStartTime,this.timerHandler=null,l(t.enable_stay_duration)&&(this.options=Object.assign(this.options,t.enable_stay_duration)),this.hard=this.options.mode&&"hard"===this.options.mode,this.focusState=document.hasFocus(),this.aliveDTime=this.options.aliveTime||6e4}return e.prototype.setParams=function(e,t,n){this.set_path=e,this.set_url=n,this.set_title=t},e.prototype.enable=function(e,t,n){this.url_path=e,this.url=n,this.title=t,this.disableCallback=this.enablePageAlive(),this.hard&&(this.removeMode=this.hardMode())},e.prototype.disable=function(){this.disableCallback(),this.pageStartTime=Date.now(),this.hard&&this.removeMode&&this.removeMode()},e.prototype.sendEvent=function(e,n){void 0===n&&(n=!1);var i=n?this.aliveDTime:Date.now()-this.sessionStartTime;if(!(i<0||i>this.aliveDTime||Date.now()-this.pageStartTime>this.maxDuration)&&(!n||"hidden"!==document.visibilityState)&&(!this.hard||this.focusState)){var r=t({url_path:this.getParams("url_path"),title:this.getParams("title"),url:this.getParams("url"),duration:i,is_support_visibility_change:_(),startTime:this.sessionStartTime,hidden:document.visibilityState,leave:e,mode:this.hard?"hard":"normal",focusState:this.focusState,aliveType:this.options.container?"dom":"page"},this.options.params);if(this.options.container){var o=Qn(this.options.container).element_path;r=Object.assign(r,{element_path:o,aliveType:this.options.container?"dom":"page"})}this.collect.beconEvent(this.options.aliveName,r),this.sessionStartTime=Date.now()}},e.prototype.getParams=function(e){switch(e){case"url_path":return this.set_path||this.url_path||location.pathname;case"title":return this.set_title||this.title||document.title||location.pathname;case"url":return this.set_url||this.url||location.href}},e.prototype.setUpTimer=function(){var e=this;return this.timerHandler&&clearInterval(this.timerHandler),setInterval((function(){Date.now()-e.sessionStartTime>e.aliveDTime&&e.sendEvent(!1,!0)}),1e3)},e.prototype.visibilitychange=function(){"hidden"===this.getDisplayStatus()?this.timerHandler&&(clearInterval(this.timerHandler),this.sendEvent(!1)):"visible"===this.getDisplayStatus()&&(this.sessionStartTime=Date.now(),this.timerHandler=this.setUpTimer())},e.prototype.getDisplayStatus=function(){if(!this.options.container)return document.visibilityState;try{var e=window.getComputedStyle(this.options.container);return"none"!==e.display&&"hidden"!==e.visibility&&"0"!==e.opacity?"visible":"hidden"}catch(e){return"visible"}},e.prototype.beforeunload=function(){document.hidden||this.sendEvent(!0)},e.prototype.muition=function(){var e=this;new MutationObserver((function(t){t.forEach((function(t){e.visibilitychange()}))})).observe(this.options.container,{attributes:!0,attributeFilter:["style"]})},e.prototype.enablePageAlive=function(){var e=this;this.timerHandler=this.setUpTimer();var t=this.visibilitychange.bind(this),n=this.beforeunload.bind(this);return this.options.container?this.muition():document.addEventListener("visibilitychange",t),window.addEventListener("pagehide",n),function(){e.beforeunload(),document.removeEventListener("visibilitychange",t),window.removeEventListener("pagehide",n)}},e.prototype.hardMode=function(){var e=this,t=function(){e.timerHandler&&(clearInterval(e.timerHandler),e.sendEvent(!1)),e.focusState=!1},n=function(){e.focusState=!0,e.sessionStartTime=Date.now(),e.timerHandler=e.setUpTimer()};return window.addEventListener("blur",t),window.addEventListener("focus",n),function(){window.removeEventListener("blur",t),window.removeEventListener("focus",n)}},e}(),ti=function(){function e(e,t){var n=this;this.maxDuration=432e5,this.aliveDTime=6e4,this.options={closeName:"predefine_page_close",params:{}},this.focusState=!0,this.visibilitychange=function(){"hidden"===document.visibilityState?n.activeEndTime=Date.now():"visible"===document.visibilityState&&(n.activeEndTime&&(n.totalTime+=n.activeEndTime-n.activeStartTime,n.activeTimes+=1),n.activeEndTime=void 0,n.activeStartTime=Date.now())},this.beforeunload=function(){if(n.totalTime+=(n.activeEndTime||Date.now())-n.activeStartTime,n.config.autotrack)try{window.sessionStorage.setItem("_tea_cache_duration",JSON.stringify({duration:n.totalTime,page_title:document.title||location.pathname}))}catch(e){}n.sendEventPageClose()},this.collect=e,this.config=t,this.maxDuration=t.maxDuration||t.max_duration||864e5,this.pageStartTime=Date.now(),l(t.enable_stay_duration)&&(this.options=Object.assign(this.options,t.enable_stay_duration)),this.hard=this.options.mode&&"hard"===this.options.mode,this.focusState=document.hasFocus(),this.resetData()}return e.prototype.setParams=function(e,t,n){this.set_path=e,this.set_url=n,this.set_title=t},e.prototype.resetParams=function(e,t,n){this.url_path=e,this.url=n,this.title=t},e.prototype.enable=function(e,t,n){this.url_path=e,this.url=n,this.title=t,this.disableCallback=this.enablePageClose(),this.hard&&(this.removeMode=this.hardMode())},e.prototype.disable=function(){this.disableCallback(),this.hard&&this.removeMode&&this.removeMode(),this.resetData()},e.prototype.resetData=function(){this.activeStartTime=void 0===this.activeStartTime?this.pageStartTime:Date.now(),this.activeEndTime=void 0,this.activeTimes=1,this.totalTime=0,this.resetParams(location.pathname,document.title,location.href)},e.prototype.sendEventPageClose=function(){var e=Date.now()-this.pageStartTime;this.totalTime<0||e<0||this.totalTime>=this.maxDuration||this.hard&&!this.focusState||(this.collect.beconEvent(this.options.closeName,t({url_path:this.getParams("url_path"),title:this.getParams("title"),url:this.getParams("url"),active_times:this.activeTimes,duration:this.totalTime,total_duration:e,is_support_visibility_change:_(),mode:this.hard?"hard":"normal",focusState:this.focusState},this.options.params)),this.pageStartTime=Date.now(),this.resetData())},e.prototype.getParams=function(e){switch(e){case"url_path":return this.set_path||this.url_path||location.pathname;case"title":return this.set_title||this.title||document.title||location.pathname;case"url":return this.set_url||this.url||location.href}},e.prototype.enablePageClose=function(){var e=this,t=this.visibilitychange.bind(this),n=this.beforeunload.bind(this);return document.addEventListener("visibilitychange",t),window.addEventListener("pagehide",n),function(){e.beforeunload(),document.removeEventListener("visibilitychange",t),window.removeEventListener("pagehide",n)}},e.prototype.hardMode=function(){var e=this,t=function(){e.focusState=!1},n=function(){e.focusState=!0};return window.addEventListener("blur",t),window.addEventListener("focus",n),function(){window.removeEventListener("blur",t),window.removeEventListener("focus",n)}},e}(),ni=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;if(this.collect=e,this.config=t,this.config.enable_stay_duration){this.title=document.title||location.pathname,this.url=location.href,this.url_path=location.pathname,this.pageAlive=new ei(e,t),this.pageClose=new ti(e,t);var i=this.collect.Types;this.collect.on(i.DestoryInstance,(function(){n.disable()})),this.collect.on(i.ResetStay,(function(e){var t=e.url_path,i=e.title,r=e.url;n.resetStayDuration(t,i,r)})),this.collect.on(i.RouteChange,(function(e){e.init||t.disable_route_report||n.resetStayDuration()})),this.collect.on(i.SetStay,(function(e){var t=e.url_path,i=e.title,r=e.url;n.setStayParmas(t,i,r)})),this.enable(this.url_path,this.title,this.url),this.ready(i.Stay),this.collect.emit(i.StayReady)}},e.prototype.ready=function(e){var t=this;if(this.collect.set(e),this.collect.hook._hooksCache.hasOwnProperty(e)){var n=this.collect.hook._hooksCache[e];if(!Object.keys(n).length)return;var i=function(e){n[e].length&&n[e].forEach((function(n){t.collect.hook.emit(e,n)}))};for(var r in n)i(r)}},e.prototype.enable=function(e,t,n){this.pageAlive.enable(e,t,n),this.pageClose.enable(e,t,n)},e.prototype.disable=function(){this.pageAlive.disable(),this.pageClose.disable()},e.prototype.setStayParmas=function(e,t,n){void 0===e&&(e=""),void 0===t&&(t=""),void 0===n&&(n=""),this.pageAlive.setParams(e,t,n),this.pageClose.setParams(e,t,n),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行 resetStayParams",level:"info",time:Date.now(),data:{url_path:e,title:t,url:n}})},e.prototype.reset=function(e,t,n){this.disable(),this.enable(e,t,n)},e.prototype.resetStayDuration=function(e,t,n){this.reset(e,t,n),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"SDK 执行 resetStayDuration",level:"info",time:Date.now(),data:{url_path:e,title:t,url:n}})},e}(),ii=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;this.collect=e,this.config=t,this.duration=6e4,this.reportUrl=e.configManager.getDomain()+"/profile/list";var i=e.Types;this.eventCheck=new O(e,t),this.cache={},this.collect.on(i.ProfileSet,(function(e){n.setProfile(e)})),this.collect.on(i.ProfileSetOnce,(function(e){n.setOnceProfile(e)})),this.collect.on(i.ProfileUnset,(function(e){n.unsetProfile(e)})),this.collect.on(i.ProfileIncrement,(function(e){n.incrementProfile(e)})),this.collect.on(i.ProfileAppend,(function(e){n.appendProfile(e)})),this.collect.on(i.ProfileClear,(function(){n.cache={}})),this.ready(i.Profile)},e.prototype.ready=function(e){var t=this;if(this.collect.set(e),this.collect.hook._hooksCache.hasOwnProperty(e)){var n=this.collect.hook._hooksCache[e];if(!Object.keys(n).length)return;var i=function(e){n[e].length&&n[e].forEach((function(n){t.collect.hook.emit(e,n)}))};for(var r in n)i(r)}},e.prototype.report=function(e,t){void 0===t&&(t={});try{if(this.config.disable_track_event)return;var n=[];n.push(this.collect.processEvent(e,t));var i=this.collect.eventManager.merge(n);this.collect.requestManager.useRequest({url:this.reportUrl,data:i}),this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_EVENT,info:"埋点上报成功",time:Date.now(),data:i,code:200,status:"success"})}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message})}},e.prototype.setProfile=function(e){var n=this.formatParams(e);n&&Object.keys(n).length&&(this.pushCache(n),this.report("__profile_set",t(t({},n),{profile:!0})))},e.prototype.setOnceProfile=function(e){var n=this.formatParams(e,!0);n&&Object.keys(n).length&&(this.pushCache(n),this.report("__profile_set_once",t(t({},n),{profile:!0})))},e.prototype.incrementProfile=function(e){e?this.report("__profile_increment",t(t({},e),{profile:!0})):console.warn("please check the params, must be object!!!")},e.prototype.unsetProfile=function(e){if(e){var n={};n[e]="1",this.report("__profile_unset",t(t({},n),{profile:!0}))}else console.warn("please check the key, must be string!!!")},e.prototype.appendProfile=function(e){if(e){var n={};for(var i in e)"string"==typeof e[i]||"Array"===Object.prototype.toString.call(e[i]).slice(8,-1)?n[i]=e[i]:console.warn("please check the value of param: "+i+", must be string or array !!!");Object.keys(n).length&&this.report("__profile_append",t(t({},n),{profile:!0}))}else console.warn("please check the params, must be object!!!")},e.prototype.pushCache=function(e){var t=this;Object.keys(e).forEach((function(n){t.cache[n]={val:t.clone(e[n]),timestamp:Date.now()}}))},e.prototype.formatParams=function(e,t){var n=this;void 0===t&&(t=!1);try{if(!e||"[object Object]"!==Object.prototype.toString.call(e))return void console.warn("please check the params type, must be object !!!");var i={};for(var r in e)"string"==typeof e[r]||"number"==typeof e[r]||"Array"===Object.prototype.toString.call(e[r]).slice(8,-1)?i[r]=e[r]:console.warn("please check the value of params:"+r+", must be string,number,Array !!!");var o=Object.keys(i);if(!o.length)return;if(!this.eventCheck.checkEventParams(i))return;var a=Date.now();return o.filter((function(i){var r=n.cache[i];return t?!r:!(r&&n.compare(r.val,e[i])&&a-r.timestamp<n.duration)})).reduce((function(e,t){return e[t]=i[t],e}),{})}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message}),console.log("error")}},e.prototype.compare=function(e,t){try{return JSON.stringify(e)===JSON.stringify(t)}catch(e){return this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message}),!1}},e.prototype.clone=function(e){try{return JSON.parse(JSON.stringify(e))}catch(t){return this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:t.message}),e}},e.prototype.unReady=function(){console.warn("sdk is not ready, please use this api after start")},e}(),ri=function(){function e(){var e=this;this.setInterval=function(){e.clearIntervalFunc=function(e,t){void 0===e&&(e=function(){}),void 0===t&&(t=1e3);var n,i=Date.now()+t;return n=window.setTimeout((function r(){var o=Date.now()-i;e(),i+=t,n=window.setTimeout(r,Math.max(0,t-o))}),t),function(){window.clearTimeout(n)}}((function(){e.isSessionhasEvent&&e.endCurrentSession()}),e.sessionInterval)},this.clearInterval=function(){e.clearIntervalFunc&&e.clearIntervalFunc()}}return e.prototype.apply=function(e,t){var n=this;if(this.collect=e,!t.disable_heartbeat){this.sessionInterval=6e4,this.startTime=0,this.lastTime=0,this.setInterval();var i=this.collect.Types;this.collect.on(i.SessionResetTime,(function(){n.process()}))}},e.prototype.endCurrentSession=function(){this.collect.event("_be_active",{start_time:this.startTime,end_time:this.lastTime,url:window.location.href,referrer:window.document.referrer,title:document.title||location.pathname}),this.isSessionhasEvent=!1,this.startTime=0},e.prototype.process=function(){this.isSessionhasEvent||(this.isSessionhasEvent=!0,this.startTime=+new Date);var e=this.lastTime||+new Date;this.lastTime=+new Date,this.lastTime-e>this.sessionInterval&&(this.clearInterval(),this.endCurrentSession(),this.setInterval())},e}(),oi=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;t.channel_domain||t.disable_track_event||t.disable_sdk_monitor||(this.collect=e,this.config=t,this.url=e.configManager.getUrl("event"),this.collect.on(e.Types.Ready,(function(){n.sdkOnload()})),this.collect.on(e.Types.SubmitError,(function(e){var t=e.type,i=e.eventData,r=e.errorCode,o=e.response;"f_data"===t&&n.sdkError(i,r,o)})),this.collect.on(e.Types.SDKInnerError,(function(e){var t=e.type,i=e.eventData,r=e.errorCode,o=e.msg;n.sdkInnerError(t,i,r,o)})))},e.prototype.sdkOnload=function(){var e=this;try{var t=this.collect.configManager.get(),n=t.header,i=t.user,r=n.app_id,o=n.app_name,a=n.sdk_version,s=i.web_id,c={events:[{event:"onload",params:JSON.stringify({app_id:r,app_name:o||"",sdk_version:a,sdk_type:"npm",sdk_config:this.config,sdk_desc:"TOC",url:location.href}),local_time_ms:Date.now()}],user:{user_unique_id:s},header:{}};setTimeout((function(){e.collect.requestManager.useRequest({url:e.url,data:[c],timeout:3e4,app_key:"566f58151b0ed37e",forceXhr:!0})}),16)}catch(e){}},e.prototype.sdkError=function(e,t,n){var i=this;try{var r=e[0],o=r.user,a=r.header,s=[];e.forEach((function(e){e.events.forEach((function(e){s.push(e)}))}));var c={events:s.map((function(e){return{event:"on_error",params:JSON.stringify({type:"sdk_event_error",error_code:t,app_id:a.app_id,app_name:a.app_name||"",error_event:e.event,sdk_version:a.sdk_version,local_time_ms:e.local_time_ms,tea_event_index:Date.now(),params:e.params,header:a,user:o,err_msg:n.m||""}),local_time_ms:Date.now()}})),user:{user_unique_id:o.user_unique_id},header:{}};setTimeout((function(){i.collect.requestManager.useRequest({url:i.url,data:[c],timeout:3e4,app_key:"566f58151b0ed37e",forceXhr:!0})}),16)}catch(e){}},e.prototype.sdkInnerError=function(e,t,n,i){var r=this;try{var o=t[0],a=o.user,s=o.header,c=[];t.forEach((function(e){e.events.forEach((function(e){c.push(e)}))}));var u={events:c.map((function(t){return{event:"on_error",params:JSON.stringify({type:e,error_code:n,app_id:s.app_id,app_name:s.app_name||"",error_event:t.event,sdk_version:s.sdk_version,local_time_ms:t.local_time_ms,tea_event_index:Date.now(),params:t.params,header:s,user:a,ua:window.navigator.userAgent,err_msg:i}),local_time_ms:Date.now()}})),user:a,header:{}};setTimeout((function(){r.collect.requestManager.useRequest({url:r.url,data:[u],timeout:3e4,app_key:"566f58151b0ed37e",forceXhr:!0})}),16)}catch(e){}},e}(),ai=function(){function e(e,n,i){var r=this;this.statistics=!1,this.pageView=!1,this.clickEvent=function(e){(function(e,t){if(window.innerHeight,window.innerWidth,1!==e.nodeType)return!1;if(!t.svg&&function(e){if("svg"===e.tagName.toLowerCase())return!0;for(var t=e.parentElement,n=!1;t;)"svg"===t.tagName.toLowerCase()?(t=null,n=!0):t=t.parentElement;return n}(e))return!1;if(["HTML","BODY"].includes(e.tagName.toUpperCase()))return!1;var n=e;return!("none"===n.style.display||!Fn(n,"container",t.container_attr||"")&&!(function(e,t){for(var n=e;n&&!Fn(n,"container",t.container_attr||"");){if("HTML"===n.nodeName||"BODY"===n.nodeName)return false;n=n.parentElement}return!0}(n,t)||function(e){if(e.children.length){var t=e.children;return![].slice.call(t).some((function(e){return e.children.length>0}))}return!0}(n)||t.svg))})(e.target,r.options)&&r.eventHandel({eventType:"dom",eventName:"click"},e)},this.changeEvent=function(e){r.eventHandel({eventType:"dom",eventName:"change"},e)},this.submitEvent=function(e){r.eventHandel({eventType:"dom",eventName:"submit"},e)},this.getPageViewEvent=function(e,n){n&&"pushState"===n&&r.eventHandel({eventType:"dom",eventName:"beat"},t({beat_type:0},e)),r.eventHandel({eventType:"dom",eventName:"page_view"},e)},this.getPageLoadEvent=function(e){r.eventHandel({eventType:"dom",eventName:"page_statistics"},{lcp:e})},this.config=i.getConfig().eventConfig,this.collect=n,this.options=e,this.beatTime=e.beat,this.domContainer=e.container||document}return e.prototype.init=function(e){this.eventHandel=e;var t=this.config.mode;this.addListener(t)},e.prototype.addListener=function(e){var t=this;if("proxy-capturing"===e){if(this.config.click&&(h(this.domContainer)?this.domContainer.forEach((function(e){e.addEventListener("click",t.clickEvent,!0)})):this.domContainer.addEventListener("click",this.clickEvent,!0)),this.config.pv){this.collect.on("route-change",(function(e){var n=e.config,i=e.name;t.getPageViewEvent(n,i),t.pageView=!0}));var n=setTimeout((function(){t.pageView||(t.getPageViewEvent(t.getDefaultConfig()),t.pageView=!0,clearTimeout(n))}),2e3)}if(this.config.beat){try{"complete"===document.readyState?this.beatEvent(this.beatTime):window.addEventListener("load",(function(){t.beatEvent(t.beatTime)}));var i=0,r=null;window.addEventListener("scroll",(function(){clearTimeout(r),r=setTimeout(o,500),i=document.documentElement.scrollTop||document.body.scrollTop}));var o=function(){(document.documentElement.scrollTop||document.body.scrollTop)==i&&t.eventHandel({eventType:"dom",eventName:"beat"},{beat_type:1})}}catch(e){}try{var a=window.performance&&window.performance.getEntriesByType("paint");if(a&&a.length){new PerformanceObserver((function(e){var n=e.getEntries(),i=n[n.length-1],r=i.renderTime||i.loadTime;t.statistics||(t.getPageLoadEvent(r),t.statistics=!0)})).observe({entryTypes:["largest-contentful-paint"]});var s=setTimeout((function(){t.statistics||(t.getPageLoadEvent(a[0].startTime||0),t.statistics=!0,clearTimeout(s))}),2e3)}else this.getPageLoadEvent(0)}catch(e){this.getPageLoadEvent(0)}}}},e.prototype.removeListener=function(){var e=this;h(this.domContainer)?this.domContainer.forEach((function(t){t.removeEventListener("click",e.clickEvent,!0)})):this.domContainer.removeEventListener("click",this.clickEvent,!0)},e.prototype.beatEvent=function(e){var t=this;try{var n;this.eventHandel({eventType:"dom",eventName:"beat"},{beat_type:3}),this.beatTime&&(n=setInterval((function(){t.eventHandel({eventType:"dom",eventName:"beat"},{beat_type:2})}),e)),g((function(){t.eventHandel({eventType:"dom",eventName:"beat",eventSend:"becon"},{beat_type:0}),t.beatTime&&clearInterval(n)}))}catch(e){}},e.prototype.getDefaultConfig=function(){return{is_html:1,url:location.href,referrer:document.referrer,page_key:location.href,refer_page_key:document.referrer,page_title:document.title||location.pathname,page_manual_key:"",refer_page_manual_key:"",refer_page_title:"",page_path:location.pathname,page_host:location.host}},e}(),si={eventConfig:{mode:"proxy-capturing",submit:!1,click:!0,change:!1,pv:!0,beat:!0,hashTag:!1,impr:!1},scoutConfig:{mode:"xpath"}},ci=function(){function e(e,t){this.config=e,this.config.eventConfig=Object.assign(this.config.eventConfig,t)}return e.prototype.getConfig=function(){return this.config},e.prototype.setConfig=function(e){return this.config=e},e}(),ui=function(e,n,i,r,o){return t(t({event:e},function(e,t,n,i){var r={},o=function(e){if(e){var t=e.getBoundingClientRect(),n=t.width,i=t.height;return{left:t.left,top:t.top,element_width:n,element_height:i}}}(t=Jn(t,n)),a=function(e,t){void 0===e&&(e={}),void 0===t&&(t={});var n=e.clientX,i=e.clientY,r=t.left,o=t.top,a=n-r>=0?n-r:0,s=i-o>=0?i-o:0;return{touch_x:Math.floor(a),touch_y:Math.floor(s)}}(e,o),s=o.element_width,c=o.element_height,u=a.touch_x,l=a.touch_y,h=Qn(t),f=h.element_path,d=h.positions,p=function(e,t){var n=Jn(e,t),i=[];return function e(t){var n=function(e){var t="";return 3===e.nodeType?t=e.textContent.trim():e.dataset&&e.dataset.hasOwnProperty("teaTitle")||e.hasAttribute("ata-tea-title")?t=e.getAttribute("data-tea-title"):"INPUT"===e.nodeName&&["button","submit"].includes(e.getAttribute("type"))?t=e.getAttribute("value"):"IMG"===e.nodeName&&e.getAttribute("alt")&&(t=e.getAttribute("alt")),t.slice(0,200)}(t);if(n&&-1===i.indexOf(n)&&i.push(n),t.childNodes.length>0)for(var r=t.childNodes,o=0;o<r.length;o++)8!==r[o].nodeType&&e(r[o])}(n),i}(t,n),g=window.performance.timing.navigationStart,_=Date.now()-g,v=d.map((function(e){return""+e})),m=null;if(window.TEAVisualEditor&&window.TEAVisualEditor.getOriginXpath&&(m=window.TEAVisualEditor.getOriginXpath({xpath:f,positions:v})),r.element_path=m&&m.xpath||f,r.positions=m&&m.positions||v,i&&!i.text&&(r.texts=p,r.element_title=t.getAttribute("data-tea-title")||t.getAttribute("title")||function(e){if(!e)return"";var t="";try{e.textContent?t=e.textContent.trim().replace(/\s+/g,","):e.innerText&&(t=e.innerText.trim().replace(/\s+/g,",")),"input"!==e.tagName&&"INPUT"!==e.tagName||(t=e.value||"")}finally{return t}}(t)),r.element_id=t.getAttribute("id")||"",r.element_class_name=t.getAttribute("class")||"",r.element_type=t.nodeType,r.element_width=Math.floor(s),r.element_height=Math.floor(c),r.touch_x=u,r.touch_y=l,r.page_manual_key="",r.elememt_manual_key="",r.since_page_start_ms=_,r.page_start_ms=g,r.page_path=location.pathname,r.page_host=location.host,n.track_attr&&Wn(t,n.track_attr)){var y=Yn(t,n.track_attr);for(var b in y)r[b]=y[b]}if(n.custom_attr)try{if(Wn(t,n.custom_attr)){y=Yn(t,n.custom_attr),b=decodeURIComponent(y[n.custom_attr]);var z=JSON.parse(b);if(Object.keys(z))for(var w in z)r[w]=z[w]}}catch(e){console.log("custom attr error")}var E=Jn(t,n);return"A"===E.tagName&&(r.href=E.getAttribute("href")),"IMG"===t.tagName&&(r.src=t.getAttribute("src")),r}(n,i,r,o)),{is_html:1,page_key:window.location.href,page_title:document.title})},li=function(){function e(e,t){this.ignore={text:!1},this.initConfig=e,this.options=t,this.eventName=t&&"tea"===t.custom?{click:"__bav_click",page:"__bav_page",beat:"__bav_beat",static:"__bav_page_statistics",exposure:"__bav_page_exposure",scroll:"__bav_slide"}:{click:"bav2b_click",page:"bav2b_page",beat:"bav2b_beat",static:"bav2b_page_statistics",exposure:"bav2b_exposure",scroll:"$bav2b_slide"},t&&!1===t.text&&(this.ignore.text=!0),t&&t.exposure&&t.exposure.eventName&&(this.eventName.exposure=t.exposure.eventName)}return e.prototype.handleEvent=function(e,n){try{if(Zn(e.target))return null;var i="bav2b_click";switch(n){case"click":return i=e.target.getAttribute("data-applog-click-event")||this.eventName.click||"",ui(i,e,e.target,this.options,this.ignore);case"exposure":return i=e.target.getAttribute("data-applog-exposure-event")||this.eventName.exposure||"",ui(i,e,e.target||e,this.options,this.ignore);case"change":return t(t({},ui(i="bav2b_change",e,e.target,this.options)),function(e,t){try{if("bav2b_change"===e)return t.hasAttribute("data-tea-track")?{value:t.value}:{}}catch(e){return{}}}(i,e.target));case"submit":return ui(i="bav2b_submit",e,e.target,this.options)}}catch(e){return console.error(e),null}},e.prototype.handleViewEvent=function(e){e.event=this.eventName.page,e.page_title=document.title,e.page_total_width=window.innerWidth,e.page_total_height=window.innerHeight;try{var t=window.sessionStorage.getItem("_tea_cache_duration");if(t){var n=JSON.parse(t);e.refer_page_duration_ms=n?n.duration:""}e.scroll_width=document.documentElement.scrollLeft?document.documentElement.scrollLeft+window.innerWidth:window.innerWidth,e.scroll_height=document.documentElement.scrollTop?document.documentElement.scrollTop+window.innerHeight:window.innerHeight,e.page_start_ms=window.performance.timing.navigationStart}catch(e){console.log("page event error "+JSON.stringify(e))}return e},e.prototype.handleStatisticsEvent=function(e){var t={};t.event=this.eventName.static,t.is_html=1,t.page_key=location.href,t.refer_page_key=document.referrer||"",t.page_title=document.title,t.page_manual_key=this.initConfig.autotrack.page_manual_key||"",t.refer_page_manual_key="";try{var n=e.lcp,i=window.performance.timing,r=i.loadEventEnd-i.navigationStart;t.page_init_cost_ms=parseInt(n||(r>0?r:0)),t.page_start_ms=i.navigationStart}catch(e){console.log("page_statistics event error "+JSON.stringify(e))}return t},e.prototype.handleBeadtEvent=function(e){e.event=this.eventName.beat,e.page_key=window.location.href,e.is_html=1,e.page_title=document.title,e.page_manual_key=this.initConfig.autotrack.page_manual_key||"";try{e.page_viewport_width=window.innerWidth,e.page_viewport_height=window.innerHeight,e.page_total_width=document.documentElement.scrollWidth,e.page_total_height=document.documentElement.scrollHeight,e.scroll_width=document.documentElement.scrollLeft+window.innerWidth,e.scroll_height=document.documentElement.scrollTop+window.innerHeight,e.since_page_start_ms=Date.now()-window.performance.timing.navigationStart,e.page_start_ms=window.performance.timing.navigationStart}catch(e){console.log("beat event error "+JSON.stringify(e))}return e},e.prototype.handleExposureEvent=function(e,t){if(Zn(t.target))return null;var n=ui(e.event||this.eventName.exposure,t,t.target||t,this.options,this.ignore);if(n.$exposure_type=e.exposureType,this.options.exposure.callback){var i=this.options.exposure.callback(n);return i||Object.keys(i).length?i:(console.warn("exposure callback must return data!"),n)}return n},e.prototype.handleScrollEvent=function(e,t){var n=ui(e.event||this.eventName.scroll,t,t.target||t,this.options,this.ignore);if(n=Object.assign(n,e.params),!this.options.scroll.callback||(n=this.options.scroll.callback(n))||Object.keys(n).length)return n;console.warn("scroll callback must return data!")},e}(),hi=function(){function e(e){this.collect=e,this.eventNameList=["report_click_event","report_change_event","report_submit_event","report_exposure_event","report_page_view_event","report_page_statistics_event","report_beat_event"]}return e.prototype.send=function(e,t){var n=t.event;delete t.event,e&&"becon"===e.eventSend?this.collect.beconEvent(n,t):this.collect.event(n,t)},e.prototype.get=function(e,t){var n=Object.assign({headers:{"content-type":"application/json"},method:"GET"},t);fetch(e,n)},e.prototype.post=function(e,t){var n=Object.assign({headers:{"content-type":"application/json"},method:"POST"},t);fetch(e,n)},e}(),fi={},di=[];function pi(e,t,n,i){var r=e&&e.source||window.opener||window.parent,o=e&&e.origin||i||"*",a={type:t,payload:n};r.postMessage(JSON.stringify(a),o)}function gi(e){if(di.some((function(e){return"*"===e}))||di.some((function(t){return e.origin.indexOf(t)>-1}))){var t=e.data;if("string"==typeof e.data)try{t=JSON.parse(e.data)}catch(e){t=void 0}if(!t)return;var n=t.type,i=t.payload;fi[n]&&fi[n].forEach((function(t){"function"==typeof t&&t(e,i)}))}}var _i=!1;var vi,mi=function(){function e(e,t){this._instance=null,this._intersection=e,this.config=t,this._intersection&&this.init()}return e.prototype.init=function(){var e=this;if(window.MutationObserver)try{if(this._instance=new MutationObserver((function(t){t.forEach((function(t){"attributes"===t.type&&e.attributeChangeObserve(t),"childList"===t.type&&e.modifyNodeObserve(t)}))})),!document||!document.body)return void console.warn("please use sdk api init after body element");var t=!1!==this.config.autotrack.exposure.attributes;this._instance.observe(document.body,{childList:!0,attributes:t,subtree:!0,attributeOldValue:!1})}catch(e){console.log("your browser cannot support MutationObserver，so cannot report exposure event, please update")}else console.log("your browser cannot support MutationObserver，so cannot report exposure event, please update")},e.prototype.attributeChangeObserve=function(e){e.target.hasAttribute("data-exposure")?this.exposureAdd(e,"mutation"):this.exposureRemove(e)},e.prototype.modifyNodeObserve=function(e){var t=this;Array.prototype.forEach.call(e.addedNodes,(function(e){1===e.nodeType&&e.hasAttribute("data-exposure")&&t.exposureAdd(e,"intersect"),t.mapChild(e,t.exposureAdd.bind(t))})),Array.prototype.forEach.call(e.removedNodes,(function(e){1===e.nodeType&&e.hasAttribute("data-exposure")&&t.exposureRemove(e),t.mapChild(e,t.exposureRemove.bind(t))}))},e.prototype.mapChild=function(e,t){var n=this;1===e.nodeType&&e.children.length&&Array.prototype.forEach.call(e.children,(function(e){1===e.nodeType&&e.hasAttribute("data-exposure")&&t(e),n.mapChild(e,t)}))},e.prototype.exposureAdd=function(e,t){try{this._intersection&&this._intersection.exposureAdd(e,t)}catch(e){console.log("intersection error",JSON.stringify(e.message))}},e.prototype.exposureRemove=function(e){try{this._intersection&&this._intersection.exposureRemove(e)}catch(e){console.log("intersection error",JSON.stringify(e.message))}},e._exposure_observer=null,e}();!function(e){e[e.EXPOSURE_ONCE=0]="EXPOSURE_ONCE",e[e.LIFECYCLE_SHOW_NEW=3]="LIFECYCLE_SHOW_NEW",e[e.RESUME_FORM_PAGE=6]="RESUME_FORM_PAGE",e[e.RESUME_FORM_BACK=7]="RESUME_FORM_BACK",e[e.NOT_EXPOSURE=-1]="NOT_EXPOSURE"}(vi||(vi={}));var yi,bi=function(){function e(t,n,i){var r=this;this.backStatus=!1,this.instance=this.buildObserver(),this.collect=t,this.observeMap=e._observer_map,n.autotrack.exposure.ratio?this.ratio=n.autotrack.exposure.ratio:0===n.autotrack.exposure.ratio?this.ratio=0:this.ratio=.5,this.timeLimit=n.autotrack.exposure.stay||0,this.exposureType=vi.NOT_EXPOSURE,this.eventHandle=i,this.hashMap=new Map,this.backStatus=!1,this.lastState=null,this.collect.on("re-start-sdk",(function(){r.visibilitychange()})),this.addListen()}return e.prototype.buildObserver=function(){var t=this;if(e._observer_instance)return console.log("your browser cannot support IntersectionObserver， so cannot report exposure event, please update"),null;if(window.IntersectionObserver){for(var n=[],i=0;i<=1;i+=.01)n.push(i);n.push(1);try{e._observer_instance=new IntersectionObserver((function(e){e.forEach((function(e){var n=e.target.observeId,i=t.observeMap.get(n),r=(e.target,e.isIntersecting,e.intersectionRatio);if(i.intersectionRatio=r,r<=.1&&(e.target.backStatus=!1),t.observeMap.set(n,i),r<t.ratio)return i.isIntersecting=!1,i.added=!1,i.exposured=!1,i.startTime=Date.now(),void t.observeMap.set(n,i);if(!i.added){if(i.startTime=Date.now(),i.added=!0,i.isIntersecting=!0,t.timeLimit){var o=setTimeout((function(){try{if(i.isIntersecting&&!i.exposured&&t.observeMap.get(n)){var r=t.observeMap.get(n).intersectionRatio;t.exposureEvent(e,r,n,i)}}catch(e){console.log("IntersectionObserver setTimeout error，msg: "+JSON.stringify(e))}}),t.timeLimit);i.wait=o}t.observeMap.set(n,i)}Date.now()-i.startTime>=t.timeLimit&&!i.exposured&&t.exposureEvent(e,r,n,i)}))}),{threshold:n})}catch(e){console.log("IntersectionObserver error，msg: "+JSON.stringify(e))}return e._observer_instance}return console.log("your browser cannot support IntersectionObserver， so cannot report exposure event, please update"),null},e.prototype.exposureAdd=function(e,t){var n="mutation"===t?e.target:e,i=n.observeId,r=V();if(i||this.observeMap.has(i)){var o=this.observeMap.get(i);if(o&&n!==o.instance)this.unobserve(n),this.observeMap.delete(i),n.observeId=r,n.visible=!1,this.observeMap.set(r,{instance:n.cloneNode(!0),isIntersecting:!1}),this.observe(n);else{if(!0===n.visible)return;this.handleBoundingExposure(n)}}else n.observeId=r,n.visible=!1,this.observeMap.set(r,{instance:n.cloneNode(!0),isIntersecting:!1}),this.observe(n)},e.prototype.exposureRemove=function(e){this.observeMap.has(e.observeId)&&(this.observeMap.delete(e.observeId),this.unobserve(e),e.observeId="",e.visible=!1)},e.prototype.exposureEvent=function(e,t,n,i){if(t>=this.ratio){if("0"===e.target.style.opacity||"hidden"===e.target.style.visibility)return;if(!0===i.exposured)return;i.startWait&&clearTimeout(i.startWait);var r=e.target.getAttribute("data-exposure-event");this.eventHandle({eventType:"dom",eventName:"exposure",event:r,exposureType:this.getExposureType(e.target)},e),e.target.visible=!0,e.target.hasExposure=!0,i.startTime=Date.now(),i.exposured=!0}else i.startTime=Date.now(),i.exposured=!1;this.observeMap.set(n,i)},e.prototype.observe=function(e){this.instance&&this.instance.observe(e)},e.prototype.unobserve=function(e){this.instance&&this.instance.unobserve(e)},e.prototype.getExposureDomToExposure=function(){var e=this;Array.prototype.forEach.call(document.querySelectorAll("[data-exposure]"),(function(t){e.handleBoundingExposure(t)}))},e.prototype.visibilitychange=function(){var e=this,t=null;"visible"===document.visibilityState?this.timeLimit?t=setTimeout((function(){e.getExposureDomToExposure()}),this.timeLimit):this.getExposureDomToExposure():(clearTimeout(t),this.customType=void 0)},e.prototype.handleBoundingExposure=function(e){if(this.getBoundingRatio(e)>=this.ratio){e.visible=!0;var t=e.getAttribute&&e.getAttribute("data-exposure-event");this.eventHandle({eventType:"dom",eventName:"exposure",event:t,exposureType:this.customType||vi.RESUME_FORM_BACK},e)}},e.prototype.getBoundingRatio=function(e){var t=e.getBoundingClientRect(),n=t.top,i=t.left,r=t.width,o=t.height,a=window.innerWidth||document.documentElement.clientWidth,s=window.innerHeight||document.documentElement.clientHeight;return n>=0&&i>=0&&n<=s&&i<=a?(r>a?a:r)*(s-n)/(r*o):0},e.prototype.getExposureType=function(e){return e.visible?!1!==e.backStatus&&(this.backStatus||this.customType||2===window.performance.navigation.type)?this.exposureType=vi.RESUME_FORM_PAGE:this.exposureType=vi.LIFECYCLE_SHOW_NEW:(e.hasExposure,!1!==e.backStatus&&(this.backStatus||this.customType||2===window.performance.navigation.type)?this.exposureType=vi.RESUME_FORM_PAGE:this.exposureType=vi.EXPOSURE_ONCE),this.exposureType},e.prototype.addListen=function(){var e=this;this.collect.on("set-exposure-type",(function(t){e.customType=t}));var t,n,r,o=this.visibilitychange.bind(this);if(t=document,n="visibilitychange",r=o,t.addEventListener?t.addEventListener(n,r,!1):t.attachEven?t.attachEven("on"+n,r):t["on"+n]=r,window.addEventListener("hashchange",(function(t){var n=e.hashMap.get(t.oldURL);n&&location.href===n.prev?(e.backStatus=!0,e.exposureType=vi.RESUME_FORM_PAGE):(e.backStatus=!1,e.hashMap.set(location.href,{current:t.newURL,prev:t.oldURL}))})),this.collect.bridgeReport){var a=window.history.pushState;history.pushState=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];"function"==typeof history.onpushstate&&history.onpushstate({state:t});var o=a.call.apply(a,i([history,t],n));return e.lastState=t,o};var s=history.replaceState;history.replaceState=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];"function"==typeof history.onreplacestate&&history.onreplacestate({state:t});var o=s.call.apply(s,i([history,t],n));return e.lastState=t,o}}else this.collect.on("STATE_CHANGE",(function(t){e.lastState=t}));return window.addEventListener("popstate",(function(t){var n=t.state;n&&n.current&&e.lastState&&e.lastState.back&&n.current===e.lastState.back?(e.backStatus=!0,e.exposureType=vi.RESUME_FORM_PAGE):e.backStatus=!1})),function(){!function(e,t,n){e.addEventListener?e.removeEventListener(t,n,!1):e.attachEven&&e.detachEven("on"+t,n)}(document,"visibilitychange",o)}},e._observer_instance=null,e._observer_map=new Map,e}(),zi=function(){function e(e,t,n){t.autotrack&&t.autotrack.exposure&&(this._intersection=new bi(e,t,n),this._observer=new mi(this._intersection,t),this._intersection&&this._observer?this.initObserver():console.log("your browser version cannot support exposure event, please update~"))}return e.prototype.initObserver=function(){var e=this;Array.prototype.forEach.call(document.querySelectorAll("[data-exposure]"),(function(t){e._intersection.exposureAdd(t,"intersect")}))},e}();!function(e){e[e.SCROLL_UP=1]="SCROLL_UP",e[e.SCROLL_DOWN=2]="SCROLL_DOWN",e[e.SCROLL_LEFT=3]="SCROLL_LEFT",e[e.SCROLL_RIGHT=4]="SCROLL_RIGHT",e[e.NOT_SCROLL=-1]="NOT_SCROLL"}(yi||(yi={}));var wi,Ei=function(){function e(e,t){this.distance=30,e.autotrack&&e.autotrack.scroll&&(e.autotrack.scroll.distance&&(this.distance=e.autotrack.scroll.distance),this.eventHandle=t,this.addLinstenr(),this.mutation())}return e.prototype.addLinstenr=function(){var e=this;Array.prototype.forEach.call(document.querySelectorAll("[data-scroll]"),(function(t){e.scrollHandle(t)}))},e.prototype.mutation=function(){var e=this;if(window.MutationObserver)try{var t=new MutationObserver((function(t){t.forEach((function(t){"childList"===t.type&&e.modifyNodeObserve(t)}))}));if(!document||!document.body)return void console.warn("please use sdk api init after body element");t.observe(document.body,{childList:!0,attributes:!0,subtree:!0,attributeOldValue:!1})}catch(e){console.log("your browser cannot support MutationObserver")}else console.log("your browser cannot support MutationObserver")},e.prototype.modifyNodeObserve=function(e){var t=this;Array.prototype.forEach.call(e.addedNodes,(function(e){1===e.nodeType&&e.hasAttribute("data-scroll")&&t.scrollHandle(e),t.mapChild(e,t.scrollHandle.bind(t))})),Array.prototype.forEach.call(e.removedNodes,(function(e){1===e.nodeType&&e.hasAttribute("data-scroll")&&t.scrollHandle(e,"remove"),t.mapChild(e,t.scrollHandle.bind(t))}))},e.prototype.mapChild=function(e,t){var n=this;1===e.nodeType&&e.children.length&&Array.prototype.forEach.call(e.children,(function(e){1===e.nodeType&&e.hasAttribute("data-scroll")&&t(e),n.mapChild(e,t)}))},e.prototype.scrollHandle=function(e,t){var n=this;try{var i=e.scrollTop,r=e.scrollLeft,o=null,a=0,s=0,c=function(t){clearTimeout(o),o=setTimeout((function(){var o=e.scrollTop,h=e.scrollLeft,f=t.target&&t.target.getAttribute("data-scroll-event")||"";1===l||2===l?o===c&&(s=c-i,Math.abs(s)>=n.distance&&n.distance&&n.eventHandle({eventType:"dom",eventName:"scroll",event:f,params:{$direction:l,$offsetY:s,$offsetX:a}},t)):3!==l&&4!==l||h===u&&(a=u-r,Math.abs(a)>=n.distance&&n.distance&&n.eventHandle({eventType:"dom",eventName:"scroll",event:f,params:{$direction:l,$offsetY:s,$offsetX:a}},t)),i=c,r=u}),100);var c=e.scrollTop,u=e.scrollLeft,l=yi.NOT_SCROLL;c>i?l=yi.SCROLL_DOWN:c<i?l=yi.SCROLL_UP:u>r?l=yi.SCROLL_LEFT:u<r&&(l=yi.SCROLL_RIGHT)};return"remove"===t?e.removeEventListener("scroll",c,!1):e.addEventListener("scroll",c,!1),function(){e.removeEventListener("scroll",c,!1)}}catch(e){console.warn("scroll event error",JSON.stringify(e))}},e}(),Si={hashTag:!1,impr:!1},ki=function(){function e(){}return e.prototype.apply=function(e,t){if(this.autoTrackStart=!1,this.collect=e,this.config=t,t.autotrack){var n=e.Types;this.ready(n.Autotrack),this.collect.emit(n.AutotrackReady)}},e.prototype.ready=function(e){this.collect.set(e);var t=this.config.autotrack;t="object"==typeof t?t:{},t=Object.assign(Si,t),this.destroyed=!1,this.options=t,this.Config=new ci(si,this.options),this.Exposure=new zi(this.collect,this.config,this.handle.bind(this)),this.Scroll=new Ei(this.config,this.handle.bind(this)),this.Listener=new ai(t,this.collect,this.Config),this.EventHandle=new li(this.config,t),this.Request=new hi(this.collect),this.autoTrackStart=!0,this.init(),function(e,t){window.TEAVisualEditor=window.TEAVisualEditor||{},window.TEAVisualEditor.appId=t.app_id;var n=t.channel_domain,i="";if(function(e){e.length&&e.forEach((function(e){di.push(e)}))}(["*"]),n){var r,o="";try{var a=window.performance.getEntriesByType("resource");if(a&&a.length&&(a.forEach((function(e){"script"===e.initiatorType&&e.name&&-1!==e.name.indexOf("collect")&&(o=e.name)})),o&&(r=o.split("/"))&&r.length)){i="https:/";for(var s=2;s<r.length&&s!==r.length-1;s++)i=i+"/"+r[s];if(i&&i.indexOf("/5.0")){var c=i.split("/5.0");i=c[0]||i}}}catch(e){}}!function(e,t){var n={};if(Object.keys(e).length)for(var i in e){if("filter"===i||"autotrack"===i||"enable_stay_duration"===i)break;n[i]=e[i]}var r=e.domain;(window.opener||window.parent).postMessage({type:"tea:sdk:info",config:n,version:t},r),window.addEventListener("message",gi,!1)}(t,R);try{!function(e,t){fi[e]=fi[e]||[],fi[e].push(t)}("tea:openHeatMapCore",(function(n){var i=G,r=n.data.app_id;r&&r!==t.app_id||function(e){var t=e.event,n=e.autoTrackInstance;_i||(_i=!0,function(e,t,n){var i=document.createElement("script");i.src=e,i.onerror=function(){n()},i.onload=function(){t()},document.getElementsByTagName("head")[0].appendChild(i)}(e.editorUrl,(function(){pi(t,"editorScriptloadSuccess"),n.destroy()}),(function(){t&&pi(t,"editorScriptloadError"),_i=!1})))}({event:n,editorUrl:i+".js?query="+Date.now(),autoTrackInstance:e})}))}catch(e){console.log("openHeatMapCore error")}}(this,this.config)},e.prototype.init=function(){this.Listener.init(this.handle.bind(this));try{"base"===this.collect.loadType&&(window.opener||window.parent).postMessage("[tea-sdk]ready","*")}catch(e){}},e.prototype.handle=function(e,t){try{if(this.config.autotrack.collect_url&&!this.config.autotrack.collect_url())return}catch(e){}"dom"===e.eventType&&this.handleDom(e,t)},e.prototype.handleDom=function(e,t){try{var n=e.eventName;if("click"===n||"change"===n||"submit"===n)(a=this.EventHandle.handleEvent(t,n))&&this.Request.send({eventType:"custom"},a);else if("page_view"===n||"page_statistics"===n){var i;(i="page_view"===n?this.EventHandle.handleViewEvent(t):this.EventHandle.handleStatisticsEvent(t))&&this.Request.send({eventType:"custom"},i)}else if("beat"===n){var r=this.EventHandle.handleBeadtEvent(t),o=e.eventSend;r&&this.Request.send({eventType:"custom",eventSend:o},r)}else if("exposure"===n)(a=this.EventHandle.handleExposureEvent(e,t))&&this.Request.send({eventType:"custom"},a);else if("scroll"===n){var a;(a=this.EventHandle.handleScrollEvent(e,t))&&this.Request.send({eventType:"custom"},a)}}catch(e){console.log("handel dom event error "+JSON.stringify(e))}},e.prototype.destroy=function(){if(!this.autoTrackStart)return console.warn("engine is undefined, make sure you have called autoTrack.start()");this.autoTrackStart=!1,this.Listener.removeListener()},e}();!function(e){e.Net="net",e.FailNet="f_net",e.FailData="f_data"}(wi||(wi={}));var xi={pv:["predefine_pageview"],sdk:["_be_active","predefine_page_alive","predefine_page_close","__profile_set","__profile_set_once","__profile_increment","__profile_unset","__profile_append"],autotrack:["bav2b_click","bav2b_page","bav2b_beat","bav2b_page_statistics","__bav_click","__bav_page","__bav_beat","__bav_page_statistics"]},Di=function(){function e(){}return e.prototype.apply=function(e,t){var n=this;if(t.app_id&&t.enable_tracer&&!t.disable_track_event){this.limit={pv:1,sdk:3,autotrack:3,log:3},this.errorCode={f_net:0,f_data:0},this.tracerCache=new Map,this.collect=e,this.appid=t.app_id,this.reportUrl=e.configManager.getUrl("event");var i=this.collect.Types;this.collect.on(i.Event,(function(){n.addCount("log")})),this.collect.on(i.SubmitError,(function(e){var t=e.type,i=e.eventDate,r=e.errorCode,o=e.response;n.addErrorCount(i,t,r,o)})),this.listener(),this.collect.emit(i.TracerReady)}},e.prototype.addCount=function(e,t,n){void 0===t&&(t="net"),void 0===n&&(n=1);try{this.tracerCache||(this.tracerCache=new Map);var i=void 0;if(this.tracerCache.has(e))(r=this.tracerCache.get(e)).has(t)?(i=r.get(t).params.count,i++,r.set(t,this.processTracer(i,e,t))):(i=n,r.set(t,this.processTracer(n,e,t)));else{var r=new Map;i=n,r.set(t,this.processTracer(n,e,t)),this.tracerCache.set(e,r)}"net"===t&&i>=this.limit[e]&&this.report(!1)}catch(e){console.log(e)}},e.prototype.addErrorCount=function(e,t,n,i){var r=this;try{if(e&&e.length){var o=e[0].events;o&&o.length&&("f_data"===t?(i&&i.hasOwnProperty("sc")?this.addCount("log",t,o.length-i.sc):this.addCount("log",t,o.length),this.errorCode[t]=n):o.forEach((function(e){var i="log";for(var o in xi)if(-1!==xi[o].indexOf(e.event)){i=o;break}r.addCount(i,t,1),r.errorCode[t]=n})))}}catch(e){}},e.prototype.report=function(e){if(this.tracerCache){var t=[];this.tracerCache.forEach((function(e){console.log(e),e.forEach((function(e){t.push(e)}))})),t&&t.length&&this.sendTracer(t,e)}},e.prototype.sendTracer=function(e,t){try{var n=this.collect.eventManager.merge(e);this.collect.requestManager.useRequest({url:this.reportUrl,data:n,useBeacon:t}),this.tracerCache=null}catch(e){}},e.prototype.processTracer=function(e,t,n){var i={count:e,state:n,key:t,params_for_special:"applog_trace",aid:this.appid,platform:"web",_staging_flag:1,sdk_version:R};"f_net"!==n&&"f_data"!==n||(i.errorCode=this.errorCode[n]);var r=this.collect.processEvent("applog_trace",i);if(r&&r.event)return delete r.is_bav,r},e.prototype.listener=function(){var e=this;document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e.leavePage()})),g((function(){e.leavePage()}))},e.prototype.leavePage=function(){this.report(!0)},e}(),Ai=function(){function e(){this.retryWaitTime=3e3,this.retryStatus=!1,this.retryCacheStatus=!1}return e.prototype.apply=function(e,t){var n=this;if(t.enable_storage&&!t.disable_storage&&(this.collect=e,this.config=t,!this.collect.destroy)){var i=e.Types,r=e.adapters.storage;this.storage=new r(!1),this.eventUrl=this.collect.configManager.getUrl("event"),this.eventKey="__tea_cache_events_"+t.app_id,this.storageNum=t.storage_num||50,this.retryNum=t.retry_num||3,this.retryInterval=1e3,e.on(i.SubmitError,(function(e){"f_data"!==e.type&&n.storeData(e)})),e.on(i.Ready,(function(){n.checkStorage()}))}},e.prototype.retryRightNow=function(e){var t=this;if(this.retryStatus)this.errorCache.push(e);else{var n=0;this.retryStatus=!0;var i=setInterval((function(){if(3===n)return t.storeData(t.errorCache),t.retryStatus=!1,void clearInterval(i);var r=e.eventData;t.fetchData(r,(function(){t.retryStatus=!1,clearInterval(i),t.retryCacheStatus&&t.errorCache.splice(0,1),t.errorCache.length&&(t.retryCacheStatus=!0,t.retryRightNow(t.errorCache[0]))}),(function(){n++}))}),this.retryInterval)}},e.prototype.storeData=function(e){var t=this.storage.getItem(this.eventKey),n=e.eventData;Object.keys(t).length!==this.storageNum&&(t[Date.now()]=n,this.storage.setItem(this.eventKey,t))},e.prototype.checkStorage=function(){var e=this;try{if(!window.navigator.onLine)return;var t=this.storage.getItem(this.eventKey);if(!t||!Object.keys(t).length)return;var n={events:[{event:"ontest",params:{app_id:this.config.app_id},local_time_ms:Date.now()}],user:{user_unique_id:this.collect.configManager.get("web_id")},header:{}};this.fetchData([n],(function(){var n=JSON.parse(JSON.stringify(t)),i=function(i){e.fetchData(t[i],(function(){delete n[i],e.storage.setItem(e.eventKey,n)}),(function(){}),!1)};for(var r in t)i(r)}),(function(){console.log("network error，compensate report fail")}),!0)}catch(e){console.warn("error check storage")}},e.prototype.fetchData=function(e,t,n,i){this.collect.requestManager.useRequest({url:this.eventUrl,data:e,timeout:3e4,success:t,fail:n,app_key:i?"566f58151b0ed37e":""})},e}(),Ti="undefined"!=typeof window?(window.LogPluginObject||(window.LogPluginObject={}),window.LogPluginObject):null,Ri=function(){function e(){}return e.prototype.apply=function(e,t){this._plugin={},this.config=t,this.collect=e,this.channel=t.channel||"cn",this.loadExtend()},e.prototype.loadExtend=function(){var e=this;try{this.collect.remotePlugin.forEach((function(t,n){if("sdk"===t)if(jn.hasOwnProperty(n)){var i=jn[n].object,r=jn[n].src[e.channel]+"?query="+Date.now();e.exist(n,i,r)}else console.warn("your "+n+" is not exist，please check plugin name");else"object"==typeof t&&(t.src?e.exist(n,t.call,t.src):e.process(n,t.instance,"INSTANCE"))}))}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message}),console.log("load extend error")}},e.prototype.exist=function(e,t,n){var i=this;Ti[t]?(this.process(e,Ti[t]),console.log("已有"+e+"插件，避免重复加载~")):this.loadPlugin(e,n,(function(){i.process(e,Ti[t]),console.log(" %c %s %s %s","color: yellow; background-color: black;","–","load plugin:"+e+" success","-")}),(function(){console.log(" %c %s %s %s","color: red; background-color: yellow;","–","load plugin:"+e+" error","-")}))},e.prototype.process=function(e,t,n){try{if(n){var i=new t;i.apply&&i.apply(this.collect,this.config),console.log("excude "+e+" success")}else t&&t(this.collect,this.config)}catch(t){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:t.message}),console.log("excude "+e+" error, message:"+t.message)}},e.prototype.loadPlugin=function(e,t,n,i){var r=this;try{var o=document.createElement("script");o.src=t,this._plugin[e]||(this._plugin[e]=[]),this._plugin[e].push(n),o.onerror=function(){i(t)},o.onload=function(){r._plugin[e].forEach((function(e){e()}))},document.getElementsByTagName("head")[0].appendChild(o)}catch(e){this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:e.message})}},e}(),Gi=function(){function e(){this.autotrack=!1,this.spa=!1,this.cache={},this.allowHash=!1}return e.prototype.apply=function(e,t){if(t.spa||t.autotrack){var n=e.Types;this.collect=e,this.config=t,this.appid=t.app_id,this.allowHash=t.allow_hash,this.fncArray=new Map,this.setKey(),this.setLocation(),this.hack(),this.init(),this.listener(),e.emit(n.RouteReady)}},e.prototype.setKey=function(){var e=this.collect.adapters.storage;this.storage=new e(!1),this.cache_key="__tea_cache_refer_"+this.appid,this.cache={refer_key:"",refer_title:document.title||location.pathname,refer_manual_key:"",routeChange:!1},this.config.autotrack&&"object"==typeof this.config.autotrack&&this.config.autotrack.page_manual_key&&(this.cache.refer_manual_key=this.config.autotrack.page_manual_key),this.storage.setItem(this.cache_key,this.cache)},e.prototype.hack=function(){var e=this,t=window.history.pushState;history.pushState=function(n){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];"function"==typeof history.onpushstate&&history.onpushstate({state:n});var a=t.call.apply(t,i([history,n],r));if(e.collect.sdkStop||e.collect.destroy)return a;if(!(e.lastLocation===location.href||e.config.disable_spa_query&&e.pathEquel())){e.setReferCache(e.lastLocation);var s=e.getPopStateChangeEventData();return e.lastLocation=location.href,e.sendPv(s,"pushState"),a}};var n=history.replaceState;history.replaceState=function(t){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];"function"==typeof history.onreplacestate&&history.onreplacestate({state:t});var a=n.call.apply(n,i([history,t],r));if(e.collect.sdkStop||e.collect.destroy)return a;if(!(e.lastLocation===location.href||e.config.disable_spa_query&&e.pathEquel())){e.setReferCache(e.lastLocation);var s=e.getPopStateChangeEventData();return e.lastLocation=location.href,e.sendPv(s),a}}},e.prototype.setLocation=function(){"undefined"!=typeof window&&(this.lastLocation=window.location.href)},e.prototype.getLocation=function(){return this.lastLocation},e.prototype.pathEquel=function(){try{return new URL(this.lastLocation).origin+new URL(this.lastLocation).pathname===new URL(location.href).origin+new URL(location.href).pathname}catch(e){return!1}},e.prototype.init=function(){var e=this.getPopStateChangeEventData();this.collect.emit("route-change",{config:e,init:!0})},e.prototype.listener=function(){var e=this;window.addEventListener("hashchange",(function(t){if(!e.collect.sdkStop&&!e.collect.destroy&&e.lastLocation!==window.location.href&&(!e.config.disable_spa_query||!e.pathEquel())&&e.allowHash){e.setReferCache(e.lastLocation),e.lastLocation=window.location.href;var n=e.getPopStateChangeEventData();e.sendPv(n)}})),window.addEventListener("popstate",(function(t){if(!e.collect.sdkStop&&!e.collect.destroy&&!(e.lastLocation===window.location.href||e.config.disable_spa_query&&e.pathEquel())){e.setReferCache(e.lastLocation),e.lastLocation=window.location.href;var n=e.getPopStateChangeEventData();e.sendPv(n)}}))},e.prototype.getPopStateChangeEventData=function(){var e=this.pageConfig();return e.is_back=0,e},e.prototype.pageConfig=function(){try{var e,t=this.storage.getItem(this.cache_key)||{},n=this.storage.getItem("__tea_cache_first_"+this.appid);return e=!n||1!=n,{is_html:1,url:location.href,referrer:this.handleRefer(),page_key:location.href,refer_page_key:this.handleRefer(),page_title:document.title||location.pathname,page_manual_key:this.config.autotrack&&this.config.autotrack.page_manual_key||"",refer_page_manual_key:t&&t.refer_manual_key||"",refer_page_title:t&&t.refer_title||"",page_path:location.pathname,page_host:location.host,is_first_time:""+e}}catch(t){return this.collect.emit(s.DEBUGGER_MESSAGE,{type:s.DEBUGGER_MESSAGE_SDK,info:"发生了异常",level:"error",time:Date.now(),data:t.message}),{}}},e.prototype.sendPv=function(e,t){this.collect.emit("route-change",{config:e,init:!1})},e.prototype.handleRefer=function(){var e="";try{var t=this.storage.getItem(this.cache_key)||{};e=t.routeChange?t.refer_key:this.collect.configManager.get("referrer")}catch(t){e=document.referrer}return e},e.prototype.setReferCache=function(e){var t=this.storage.getItem(this.cache_key)||{};t.refer_key=e,t.routeChange=!0,this.storage.setItem(this.cache_key,t)},e}();Kn.usePlugin(Ri,"extend"),Kn.usePlugin(Hn,"ab"),Kn.usePlugin(ni,"stay"),Kn.usePlugin(ki,"autotrack"),Kn.usePlugin(Vn,"et"),Kn.usePlugin(ii,"profile"),Kn.usePlugin(Ai,"retry"),Kn.usePlugin(ri,"heartbeat"),Kn.usePlugin(oi,"monitor"),Kn.usePlugin(Gi,"route"),Kn.usePlugin(Di,"tracer"),new Kn("default");var Oi=Kn,Mi=function(){return Mi=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},Mi.apply(this,arguments)};function Ci(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)a.push(i.value)}catch(e){r={error:e}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return a}function Ii(e,t,n){if(n||2===arguments.length)for(var i,r=0,o=t.length;r<o;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Bi=function(e){return JSON.stringify({ev_type:"batch",list:e})},Ui=["init","start","config","beforeDestroy","provide","beforeReport","report","beforeBuild","build","beforeSend","send","beforeConfig"],Li=function(){return{}};function Ni(e){return e}function ji(e){return"object"==typeof e&&null!==e}var Pi=Object.prototype;function Ki(e){if(ji(e)){if("function"==typeof Object.getPrototypeOf){var t=Object.getPrototypeOf(e);return t===Pi||null===t}return"[object Object]"===Pi.toString.call(e)}return!1}function qi(e){return"[object Array]"===Pi.toString.call(e)}function Hi(e){return"function"==typeof e}function Vi(e){return"number"==typeof e}function Fi(e){return"string"==typeof e}function Wi(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Ji(e,t){var n=Mi({},e);for(var i in t)Wi(t,i)&&void 0!==t[i]&&(ji(t[i])&&Ki(t[i])?n[i]=Ji(ji(e[i])?e[i]:{},t[i]):qi(t[i])&&qi(e[i])?n[i]=Zi(e[i],t[i]):n[i]=t[i]);return n}function Zi(e,t){var n=qi(e)?e:[],i=qi(t)?t:[];return Array.prototype.concat.call(n,i).map((function(e){return e instanceof RegExp?e:ji(e)&&Ki(e)?Ji({},e):qi(e)?Zi([],e):e}))}function $i(e,t){if(!qi(e))return!1;if(0===e.length)return!1;for(var n=0;n<e.length;){if(e[n]===t)return!0;n++}return!1}var Xi=function(e,t){if(!qi(e))return e;var n=e.indexOf(t);if(n>=0){var i=e.slice();return i.splice(n,1),i}return e},Yi=function(e,t,n){for(var i,r=Ci(t.split(".")),o=r[0],a=r.slice(1);e&&a.length>0;)e=e[o],o=(i=Ci(a))[0],a=i.slice(1);if(e)return n(e,o)};var Qi=function(e,t){var n,i=qi(n=e||[])&&n.length?function(e){for(var t=[],n=e.length,i=0;i<n;i++){var r=e[i];Fi(r)?t.push(r.replace(/([.*+?^=!:${}()|[\]/\\])/g,"\\$1")):r&&r.source&&t.push(r.source)}return new RegExp(t.join("|"),"i")}(n):null;return!!i&&i.test(t)};function er(e){try{return Fi(e)?e:JSON.stringify(e)}catch(e){return"[FAILED_TO_STRINGIFY]:"+String(e)}}var tr=function(e,t,n,i){return void 0===i&&(i=!0),function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];if(!e)return Li;var a=e[t],s=n.apply(void 0,Ii([a],Ci(r),!1)),c=s;return Hi(c)&&i&&(c=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return s.apply(this,e)}catch(t){return Hi(a)&&a.apply(this,e)}}),e[t]=c,function(n){n||(c===e[t]?e[t]=a:s=a)}}},nr=function(e,t,n){return function(){for(var i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];if(!e)return Li;var o=e[t],a=n.apply(void 0,Ii([o],Ci(i),!1)),s=a;return Hi(s)&&(s=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return a.apply(this,e)}),e[t]=s,function(){s===e[t]?e[t]=o:a=o}}},ir="".padStart?function(e,t){return void 0===t&&(t=8),e.padStart(t," ")}:function(e){return e},rr=0,or=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];console.error.apply(console,Ii(["[SDK]",Date.now(),ir(""+rr++)],Ci(e),!1))},ar=0,sr=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];console.warn.apply(console,Ii(["[SDK]",Date.now(),ir(""+ar++)],Ci(e),!1))},cr=function(e){return Math.random()<Number(e)},ur=function(e,t){return e<Number(t)},lr=function(e){return function(t){for(var n=t,i=0;i<e.length&&n;i++)try{n=e[i](n)}catch(e){or(e)}return n}};function hr(){var e=function(){for(var e=new Array(16),t=0,n=0;n<16;n++)3&n||(t=4294967296*Math.random()),e[n]=t>>>((3&n)<<3)&255;return e}();return e[6]=15&e[6]|64,e[8]=63&e[8]|128,function(e){for(var t=[],n=0;n<256;++n)t[n]=(n+256).toString(16).substr(1);var i=0,r=t;return[r[e[i++]],r[e[i++]],r[e[i++]],r[e[i++]],"-",r[e[i++]],r[e[i++]],"-",r[e[i++]],r[e[i++]],"-",r[e[i++]],r[e[i++]],"-",r[e[i++]],r[e[i++]],r[e[i++]],r[e[i++]],r[e[i++]],r[e[i++]]].join("")}(e)}var fr=function(e,t){var n=[];try{n=t.reduce((function(t,n){try{var i=n(e);"function"==typeof i&&t.push(i)}catch(e){}return t}),[])}catch(e){}return function(e){return fr(e,n)}},dr=function(e){void 0===e&&(e=3e5);var t,n=[],i=[],r=!1,o=function(e,t,i){var r=0;return-1===i?Li:function(){if(n.length)return r&&clearTimeout(r),void(r=0);0===r&&(r=setTimeout(t,i))}}(0,(function(){r=!0,t&&t[0](),i.forEach((function(e){return e()})),i.length=0,t=void 0}),e),a=function(e){n=Xi(n,e),!r&&o()};return{next:function(e){return fr(e,n)},complete:function(e){i.push(e)},attach:function(e,n){t=[e,n]},subscribe:function(e){if(r)throw new Error("Observer is closed");return n.push(e),t&&t[1]&&t[1](e),o(),function(){return a(e)}},unsubscribe:a}},pr=function(e,t,n){var i=dr(n);try{e(i.next,i.attach),t&&i.complete(t)}catch(e){}return[i.subscribe,i.unsubscribe]},gr=function(e,t){var n=Ci(e,1)[0];return function(e,i){var r=n((function(n){var i,r=(i=t,function(e){for(var t=!0,n=0;n<i.length&&t;n++)try{t=i[n](e)}catch(e){or(e)}return t})(n);return r?e(n):Li}));i((function(){r()}))}},_r=function(e,t,n,i){return e.destroyAgent.set(t,n,i)};function vr(e,t){return e.initSubject(t)}function mr(e,t,n){var i=Ci(t,2),r=i[0],o=i[1],a=e.privateSubject||{};return a[r]||(a[r]=pr(o,(function(){a[r]=void 0}),n)),a[r]}var yr=function(){return Date.now()};function br(){if("object"==typeof window&&ji(window))return window}function zr(){if("object"==typeof document&&ji(document))return document}function wr(){return br()&&window.location}function Er(){return br()&&window.history}function Sr(){if("function"==typeof XMLHttpRequest&&Hi(XMLHttpRequest))return XMLHttpRequest}function kr(e){var t=zr();if(!t||!e)return"";var n=t.createElement("a");return n.href=e,n.href}function xr(e){var t=zr();if(!t||!e)return{url:e,protocol:"",domain:"",query:"",path:"",hash:""};var n=t.createElement("a");n.href=e;var i=n.pathname||"/";return"/"!==i[0]&&(i="/"+i),{url:n.href,protocol:n.protocol.slice(0,-1),domain:n.hostname,query:n.search.substring(1),path:i,hash:n.hash}}function Dr(){var e=br()&&wr();return e?e.href:""}var Ar=function(e){var t,n={pid:(t=e.config()).pid,view_id:t.viewId,url:Dr()};return n.context=e.context?e.context.toString():{},n},Tr=function(e,t){void 0===t&&(t=!1);var n=Ar(e);return t&&(n.timestamp=yr()),function(t){e.report(Mi(Mi({},t),{overrides:n}))}},Rr=function(e){return function(t,n){var i=Ar(e);n(Li,(function(e){i&&e(i)}))}},Gr=function(e){if(e)return e.__SLARDAR_REGISTRY__||(e.__SLARDAR_REGISTRY__={Slardar:{plugins:[],errors:[],subject:{}}}),e.__SLARDAR_REGISTRY__.Slardar},Or=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Gr(br());n&&(n.errors||(n.errors=[]),n.errors.push(e))},Mr=function(e){var t={url:Dr(),timestamp:yr()},n=e.config();return(null==n?void 0:n.pid)&&(t.pid=n.pid),(null==e?void 0:e.context)&&(t.context=e.context.toString()),t},Cr=function(e,t){return function(n){var i=function(e){return e.overrides=t,e};e.on("report",i),n(),e.off("report",i)}},Ir=function(e,t,n,i){return void 0===i&&(i=!1),e.addEventListener(t,n,i),function(){e.removeEventListener(t,n,i)}},Br=function(e,t,n,i){return void 0===i&&(i=!1),e.addEventListener(t,n,i),function(){e.removeEventListener(t,n,i)}},Ur=function(e){var t=!1;return[function(n){t||(t=!0,e&&e(n))}]},Lr=function(e,t){var n,i=zr();if(i){var r=i.createElement("script");r.src=e,r.crossOrigin="anonymous",r.onload=t,null===(n=i.head)||void 0===n||n.appendChild(r)}},Nr=function(e,t){return ji(e)?Mi(Mi({},t),e):!!e&&t},jr=function(){return!!btoa&&!!atob},Pr=function(e,t,n){var i;if(!(n<=0))try{localStorage.setItem(e,(i=JSON.stringify(Mi(Mi({},t),{expires:yr()+n})),jr()?btoa(encodeURI(i)):i))}catch(e){}},Kr=function(e){return!1===e?0:!0!==e&&void 0!==e&&Vi(e)?e:7776e6},qr=function(){var e=new RegExp("\\/monitor_web\\/collect|\\/monitor_browser\\/collect\\/batch","i");return function(t){return e.test(t)}},Hr=function(e){return function(){for(var t,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return t=Ci(n,2),this._method=t[0],this._url=t[1],e.apply(this,n)}},Vr=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._reqHeaders=this._reqHeaders||{};var i=Ci(t,2),r=i[0],o=i[1];return this._reqHeaders[r]=o,e&&e.apply(this,t)}},Fr=function(e,t){var n=qr();return function(){for(var i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];var o,a;return this._start=yr(),this._data=null==i?void 0:i[0],n(this._url)||(o=this,a=t([this._method,this._url,this._start,this]),nr(o,"onreadystatechange",(function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return 4===this.readyState&&a(o),e&&e.apply(this,t)}})))(),e.apply(this,i)}},Wr=function(e){return function(t,n){if(e){var i=[];i.push(nr(e,"open",Hr)()),i.push(nr(e,"setRequestHeader",Vr)()),i.push(nr(e,"send",Fr)(t)),n((function(){i.forEach((function(e){return e()}))}))}}},Jr=function(e,t){return function(n,i){void 0===i&&(i={});var r=t([n,i]),o=e(n,i);return o.then((function(e){r(e)}),(function(){r(void 0)})),o}},Zr=["fetch_0",function(e,t){var n=br();if(n&&fetch){var i=[];i.push(nr(n,"fetch",Jr)(e)),t((function(){i.forEach((function(e){return e()}))}))}}],$r=["resource"],Xr=["resource_0",function(e,t){var n=function(){if(br()&&Hi(window.PerformanceObserver))return window.PerformanceObserver}();if(n){var i=qr();t(function(t,n,r){var o=Ci(function(e,t,n){var i=e&&new e((function(e,n){e.getEntries&&e.getEntries().forEach((function(e,i,r){return t(e,i,r,n)}))}));return[function(t){if(!e||!i)return n;try{i.observe({entryTypes:t})}catch(e){return n}},function(t,r){if(!e||!i)return n;try{var o={type:t,buffered:!0};!function(e){return void 0===e}(r)&&(o.durationThreshold=r),i.observe(o)}catch(e){return n}i.observe({type:t,buffered:!1})},function(){return i&&i.disconnect()}]}(t,(function(t){!i(t.name)&&e(t)})),3),a=o[0],s=o[2];return a(r),s}(n,0,$r))}}],Yr="pageview",Qr="session",eo="js_error",to="http",no="custom",io="action",ro={sampleRate:1,origins:[]},oo=function(){var e=window&&(window.crypto||window.msCrypto);if(void 0!==e&&e.getRandomValues){var t=new Uint16Array(8);e.getRandomValues(t);var n=function(e){for(var t=e.toString(16);t.length<4;)t="0"+t;return t};return n(t[0])+n(t[1])+n(t[2])+n(t[3])+n(t[4])+n(t[5])+n(t[6])+n(t[7])}return"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx".replace(/[x]/g,(function(){return(16*Math.random()|0).toString(16)}))},ao=function(e){var t=Nr(e,ro);if(t&&cr(t.sampleRate))return function(e,n){var i=t.origins;i.length&&Qi(i,e)&&n("traceparent","03-"+oo()+"-"+oo().substring(16)+"-01")}},so=new RegExp("(cookie|auth|jwt|token|key|ticket|secret|credential|session|password)","i"),co=new RegExp("(bearer|session)","i"),uo=function(e,t){return!e||!t||so.test(e)||co.test(t)},lo=function(e,t){try{if(t){var n=e.request.url;e.request.url=t(n),e.extra=Mi(Mi({},e.extra),{original_url:n})}}catch(e){}},ho=function(e,t,n){var i=Ci(t,2),r=i[0],o=i[1],a=n.setTraceHeader,s=n.ignoreUrls,c=n.setContextAtReq,u=n.extractUrl;e.push(r[0]((function(e){var t=Ci(e,4);t[0];var i=t[1];t[2];var r=t[3];if(!i)return Li;var l=kr(i);if(Qi(s,l))return Li;a&&a(l,(function(e,t){return r.setRequestHeader(e,t)}));var h=c(),f=void 0,d=o()[0]((function(e){l===e.name&&!f&&(f=e)}));return function(e){var t=fo(e,n);setTimeout((function(){f&&(t.response.timing=f),lo(t,u),h&&h({ev_type:to,payload:t}),d()}),100)}})))},fo=function(e,t){var n,i=e._method,r=e._reqHeaders,o=e._url,a=e._start,s=e._data,c={api:"xhr",request:{url:kr(o),method:(i||"").toLowerCase(),headers:r&&(n=r,Object.keys(n).reduce((function(e,t){return!uo(t,n[t])&&(e[t.toLowerCase()]=n[t]),e}),{})),timestamp:a},response:{status:e.status||0,is_custom_error:!1,timestamp:yr()},duration:yr()-a};"function"==typeof e.getAllResponseHeaders&&(c.response.headers=function(e){return Fi(e)&&e?e.split("\r\n").reduce((function(e,t){if(Fi(t)){var n=Ci(t.split(": "),2),i=n[0],r=n[1];!uo(i,r)&&(e[i.toLowerCase()]=r)}return e}),{}):{}}(e.getAllResponseHeaders()));var u=c.response.status,l=t.collectBodyOnError,h=t.extraExtractor;try{var f=null==h?void 0:h(e.response,c,s);f&&(c.extra=f),f&&(c.response.is_custom_error=!0),l&&(f||u>=400)&&(c.request.body=s?""+s:void 0,c.response.body=e.response?""+e.response:void 0)}catch(e){}return c},po="ajax",go={autoWrap:!0,setContextAtReq:function(){return Ni},ignoreUrls:[],collectBodyOnError:!1},_o=function(e,t,n){var i=Ci(t,2),r=i[0],o=i[1],a=n.setTraceHeader,s=n.ignoreUrls,c=n.setContextAtReq,u=n.extractUrl,l=window.Headers,h=window.Request;h&&l&&e.push(r[0]((function(e){var t=Ci(e,2),i=t[0],r=t[1],f=kr(i instanceof h?i.url:i);if(!vo(f)||Qi(s,f))return Li;a&&a(f,(function(e,t){return yo(e,t,i,r,h,l)}));var d=c(),p=yr(),g=void 0,_=o()[0]((function(e){f===e.name&&!g&&(g=e)}));return function(e){var t,o,a=Eo(i,r,e,h,l,n,p),s=(t=function(e){g&&(e.response.timing=g),lo(e,u),d&&d({ev_type:to,payload:e}),_()},o=!1,function(e){o||(o=!0,t(e))});setTimeout((function(){s(a)}),1e3)}})))},vo=function(e){if(!Fi(e))return!1;var t=Ci(e.split(":"),2),n=t[0];return!t[1]||"http"===n||"https"===n},mo=function(e,t){return e instanceof t},yo=function(e,t,n,i,r,o){var a;mo(n,r)?n.headers.set(e,t):i.headers instanceof o?i.headers.set(e,t):i.headers=Mi(Mi({},i.headers),((a={})[e]=t,a))},bo=function(e,t,n){var i=t&&t.method||"get";return mo(e,n)&&(i=e.method||i),i.toLowerCase()},zo=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];try{return t.reduce((function(t,n){return new e(n||{}).forEach((function(e,n){return!uo(n,e)&&(t[n]=e)})),t}),{})}catch(e){return{}}},wo=function(e,t,n){return mo(e,n)?e.body:null==t?void 0:t.body},Eo=function(e,t,n,i,r,o,a){var s={api:"fetch",request:{method:bo(e,t,i),timestamp:a,url:kr(e instanceof i?e.url:e),headers:zo(r,e.headers,t.headers)},response:{status:n&&n.status||0,is_custom_error:!1,timestamp:yr()},duration:yr()-a},c=o.collectBodyOnError,u=o.extraExtractor,l=function(){var n;c&&(s.request.body=null===(n=wo(e,t,i))||void 0===n?void 0:n.toString())};if(n)try{var h=zo(r,n.headers);s.response.headers=h;try{-1!==(h["content-type"]||"").indexOf("application/json")&&u&&n.clone().json().then((function(n){var r,o=u(n,s,null===(r=wo(e,t,i))||void 0===r?void 0:r.toString());o&&(s.extra=o,s.response.is_custom_error=!0,l())})).catch(Li)}catch(e){}n.status>=400&&l()}catch(e){}else l();return s},So="fetch",ko={autoWrap:!0,setContextAtReq:function(){return Ni},ignoreUrls:[],collectBodyOnError:!1},xo=["name","message","stack","filename","lineno","colno"],Do=function(e){var t,n,i;return function(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMError]":case"[object DOMException]":return!0;default:return e instanceof Error}}(e)?(i=xo,t=(n=e)&&ji(n)?i.reduce((function(e,t){return e[t]=n[t],e}),{}):n):(Ki(e)||"undefined"!=typeof Event&&function(e,t){try{return e instanceof t}catch(e){return!1}}(e,Event)||Fi(e))&&(t={message:er(e)}),t},Ao="jsError",To=function(e){return"hidden"===e.visibilityState},Ro=["hidden_3",function(e,t){var n=zr(),i=br();if(n&&i){var r=function(t){e("pagehide"===t.type||To(n))},o=Br(n,"visibilitychange",r,!0),a=Ir(i,"pagehide",r,!0);t((function(){o(),a()}),(function(e){e(To(n))}))}}],Go=["unload_0",function(e,t){var n=br();if(n){var i=Ci(Ur(e),1)[0],r=function(){i()},o=[];["unload","beforeunload","pagehide"].forEach((function(e){o.push(Ir(n,e,r,!1))})),t((function(){o.forEach((function(e){return e()}))}))}}],Oo=["hash_0",function(e,t){var n=br();if(n){var i=Ir(n,"hashchange",(function(){return e(location.href)}),!0);t((function(){i()}))}}],Mo=["history_0",function(e,t){var n=Er(),i=br();if(n&&i){var r=[],o=function(){return e(location.href)},a=function(e){return function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];try{e.apply(n,t)}finally{o()}}};r.push(tr(n,"pushState",a)(),tr(n,"replaceState",a)()),r.push(Ir(i,"popstate",o,!0)),t((function(){r.forEach((function(e){return e()}))}))}}],Co=function(e){return Io(e,yr())},Io=function(e,t){return e+"_"+t},Bo=function(e){return"manual"===e},Uo="error_weight",Lo="duration_apdex",No="perf_apdex",jo=function(e,t){var n=e[0]+e[1]+e[2],i=e[0]/n;return e[2]/n>t.frustrating_threshold?2:i>t.satisfying_threshold||0===n?0:1},Po=function(e,t){return function(n,i){var r=n.payload;switch(n.ev_type){case"performance":var o=r.name;r.isSupport&&e(i[No],o,r.value);break;case io:e(i[No],"action",r.duration||0);break;case eo:t(i[Uo],0);break;case to:if(r.response.is_custom_error||r.response.status>=400)t(i[Uo],1);else{var a=r.response.timing;a&&e(i[Lo],0,a.duration)}break;case"resource_error":t(i[Uo],2);break;case"blank_screen":t(i[Uo],3);break;case"resource":e(i[Lo],1,r.duration);break;case"performance_longtask":r.longtasks.forEach((function(t){e(i[Lo],2,t.duration)}))}}},Ko=function(){var e,t,n=function(){e=[0,0,0],t=function(){var e;return(e={error_count:[0,0,0,0],duration_count:[0,0,0]})[No]={},e}()};return n(),[function(n,i,r){var o=n&&n[i];if(o&&!(r<=0)){var a=r<(o[0].threshold||0)?0:r>(o[1].threshold||0)?2:1;if(e[a]+=o[a].weight,"string"==typeof i){var s=Io(i,a),c=t[No][s];t[No][s]=(c||0)+1}else 2===a&&(t.duration_count[i]+=1)}},function(n,i){n&&(e[2]+=n[i],t.error_count[i]+=1)},function(){return[e,t]},n]},qo=function(e,t,n,i){var r,o,a=i.sendInit,s=i.initPid,c=i.routeMode,u=i.extractPid,l=i.onPidUpdate,h=Bo(c)?function(){return""}:function(e){return function(t){var n;return"hash"===e?(null===(n=xr(t).hash)||void 0===n?void 0:n.replace(/^#/,""))||"/":xr(t).path}}(c),f=u||function(){},d=Ci(function(e,t,n,i){var r=n,o=t;return i&&i(t),[function(t,n,a){"user_set"!==t&&n!==r?(r=n,o=null!=a?a:r,i&&i(o),e(t,o)):"user_set"===t&&n!==o&&(o=n,i&&i(o),e(t,o))},function(){t&&e("init",t)}]}(function(e){return function(t,n){e(function(e,t){return{ev_type:Yr,payload:{pid:t,source:e}}}(t,n))}}(e),s||function(e){var t;return null!==(t=f(e))&&void 0!==t?t:h(e)}(location.href),h(location.href),l),2),p=d[0],g=d[1];if(!Bo(c)){var _=Ci((r=function(e,t){return p(e,h(t),f(t))},o="",[function(e,t){t!==o&&r(e,o=t)}]),1)[0];n.length&&n.forEach((function(e){return t.push(e[0]((function(e){return _(c,e)})))}))}return a&&g(),[p.bind(null,"user_set")]},Ho="pageview",Vo={sendInit:!0,routeMode:"history",apdex:2};function Fo(e){return(null==e?void 0:e.effectiveType)||(null==e?void 0:e.type)||""}var Wo=function(e,t){var n=e.common||{};return n.sample_rate=t,e.common=n,e},Jo=function(e,t,n,i,r){return e?function(e){return function(){return e}}(r(i,t)):function(){return n(t)}},Zo=function(e,t){try{return"rule"===t.type?function(e,t,n,i){var r=Yi(e,t,(function(e,t){return e[t]}));if(void 0===r)return!1;var o=function(e){return"boolean"==typeof e}(r)?"bool":Vi(r)?"number":"string";return function(e,t,n){switch(n){case"eq":return $i(t,e);case"neq":return!$i(t,e);case"gt":return e>t[0];case"gte":return e>=t[0];case"lt":return e<t[0];case"lte":return e<=t[0];case"regex":return Boolean(e.match(new RegExp(t.join("|"))));case"not_regex":return!e.match(new RegExp(t.join("|")));default:return!1}}(r,function(e,t){return e.map((function(e){switch(t){case"number":return Number(e);case"boolean":return"1"===e;default:return String(e)}}))}(i,o),n)}(e,t.field,t.op,t.values):"and"===t.type?t.children.every((function(t){return Zo(e,t)})):t.children.some((function(t){return Zo(e,t)}))}catch(e){return Or(e),!1}},$o=function(e,t,n){var i=t.url,r=t.data,o=t.success,a=void 0===o?Li:o,s=t.fail,c=void 0===s?Li:s,u=t.getResponseText,l=void 0===u?Li:u,h=t.withCredentials,f=void 0!==h&&h,d=new n;d.withCredentials=f,d.open(e,i,!0),d.setRequestHeader("Content-Type","application/json"),d.onload=function(){null==l||l(this.responseText);try{if(this.status>=400)c(new Error(this.responseText||this.statusText));else if(this.responseText){var e=JSON.parse(this.responseText);a(e)}else a({})}catch(e){c(e)}},d.onerror=function(){c(new Error("Network request failed"))},d.onabort=function(){c(new Error("Network request aborted"))},d.send(r)},Xo=function(){var e=Sr();return e?{useBeacon:!0,get:function(t){$o("GET",t,e)},post:function(t){$o("POST",t,e)}}:{get:Li,post:Li}};function Yo(e){var t=function(e){var t,n=e.transport,i=e.endpoint,r=e.size,o=void 0===r?10:r,a=e.wait,s=void 0===a?1e3:a,c=[],u=0,l={getSize:function(){return o},getWait:function(){return s},setSize:function(e){o=e},setWait:function(e){s=e},getEndpoint:function(){return i},setEndpoint:function(e){i=e},send:function(e){c.push(e),c.length>=o&&h.call(this),clearTimeout(u),u=setTimeout(h.bind(this),s)},flush:function(){clearTimeout(u),h.call(this)},getBatchData:function(){return c.length?Bi(c):""},clear:function(){clearTimeout(u),c=[]},fail:function(e){t=e}};function h(){if(c.length){var e=this.getBatchData();n.post({url:i,data:e,fail:function(n){t&&t(n,e)}}),c=[]}}return l}(e),n=t.send;return function(){var i=br();if(i){var r=Ci(Ur((function(){if(e.transport.useBeacon){var i=function(){var e=br();return e&&e.navigator.sendBeacon?{get:function(){},post:function(t,n){e.navigator.sendBeacon(t,n)}}:{get:Li,post:Li}}(),r=t.getBatchData();r&&(i.post(t.getEndpoint(),r),t.clear()),t.send=function(e){i.post(t.getEndpoint(),Bi([e]))},function(e){var t=zr(),n=br();if(t&&n){var i=Li;i=Br(t,"visibilitychange",(function(){"visible"===t.visibilityState&&(e(),i())}),!0)}}((function(){t.send=n}))}else t.flush()})),1)[0];["unload","beforeunload","pagehide"].forEach((function(e){Ir(i,e,r,!1)}))}}(),t}var Qo="1.14.1",ea="/monitor_web/settings/browser-settings",ta="/monitor_browser/collect/batch/",na=["/log/sentry/",ta,ea],ia="session",ra=["blankScreen","action"],oa={sample_rate:1,include_users:[],sample_granularity:ia,rules:{}};var aa=function(e,t,n,i){void 0===i&&(i=la);var r=t.config(),o=r.plugins,a=r.pluginBundle,s=e.filter((function(e){return o[e]&&!t.destroyAgent.has(e)})),c=function(){return s.forEach((function(e){return ha(t,e,n)}))};s.every((function(e){return da(e,n)}))?c():i(t,{name:a.name},c)},sa=function(e,t,n,i){void 0===i&&(i=la);var r=t.config().plugins;e.filter((function(e){return r[e]&&!t.destroyAgent.has(e)})).forEach((function(e){da(e,n)?ha(t,e,n):i(t,{name:e,config:r[e]},(function(){return ha(t,e,n)}))}))},ca=function(e){return function(t,n){var i,r=e.config().pluginBundle;e.destroyAgent.has(t)&&e.destroyAgent.remove(t),void 0!==n&&e.set({plugins:Mi(Mi({},e.config().plugins),(i={},i[t]=n,i))}),r&&~r.plugins.indexOf(t)?aa([t],e):sa([t],e)}};function ua(e,t,n){void 0===n&&(n=la);var i=e.config().pluginBundle,r=i?i.plugins:[];aa(r,e,t,n),sa(ra,e,t,n),e.provide("reloadPlugin",ca(e))}function la(e,t,n,i){var r=t.name,o=t.config;void 0===i&&(i=Lr);var a=function(e,t,n){var i;return null!==(i=null==n?void 0:n.path)&&void 0!==i?i:e.config().pluginPathPrefix+"/"+(t.replace(/([a-z])([A-Z])/g,(function(e,t,n){return t+"-"+n.toLowerCase()}))+".")+Qo+".js"}(e,r,o);i(a,(function(){n()}))}function ha(e,t,n){if(void 0===n&&(n=Gr(br())),n){var i=fa(n,t);if(i)try{if(e.destroyAgent.has(t))return;i.apply(e)}catch(e){Or(e),sr("[loader].applyPlugin failed",t,e)}else sr("[loader].applyPlugin not found",t)}}function fa(e,t){return e.plugins.filter((function(e){return e.name===t&&e.version===Qo}))[0]}function da(e,t){return void 0===t&&(t=Gr(br())),!(!t||!t.plugins||!fa(t,e))}function pa(e,t,n){void 0===n&&(n=Gr(br())),n&&n.plugins&&(fa(n,e)||n.plugins.push({name:e,version:Qo,apply:t}))}function ga(e){var t,n;try{for(var i=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(["userId","deviceId","sessionId","env"]),r=i.next();!r.done;r=i.next()){var o=r.value;e[o]||delete e[o]}}catch(e){t={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return e}function _a(e){var t=e.plugins||{};for(var n in t)t[n]&&!ji(t[n])&&(t[n]={});return ga(Mi(Mi({},e),{plugins:t}))}function va(e){return ji(e)&&"bid"in e}function ma(e){return ga(Mi({},e))}var ya=function(e,t){return void 0===t&&(t=ea),(e&&e.indexOf("//")>=0?"":"https://")+e+t},ba=function(e){return"SLARDAR"+e},za=function(e,t){void 0===e&&(e="");var n={userId:hr(),deviceId:hr()};if(t<=0)return n;var i=ba(e);!function(e,t){try{var n=localStorage.getItem(e);if(!n||!jr()||"{"!==n[0])return;Pr(e,JSON.parse(n),t)}catch(e){}}(i,t);var r=function(e){try{var t=localStorage.getItem(e),n=t;t&&"string"==typeof t&&(n=JSON.parse((a=t,jr()?decodeURI(atob(a)):a)));var i=n,r=i.expires,o=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]])}return n}(i,["expires"]);return r>=yr()?o:void 0}catch(e){return}var a}(i);return{userId:(null==r?void 0:r.userId)||n.userId,deviceId:(null==r?void 0:r.deviceId)||n.deviceId}},wa={get:function(){return this.__SLARDAR__REPALCE__HOLDER__}},Ea=function(e){var t,n,i=e,r={},o=wa.get(),a=Li,s=Li;return{getConfig:function(){return i},setConfig:function(e){var s,l,h,f;return r=Mi(Mi({},r),e||{}),c(),t||(t=e,i.useLocalConfig||!i.bid?(n={},a()):o?u():(s=i.transport,l=i.domain,h=i.bid,f=function(e){o=e,u()},s.get({withCredentials:!0,url:ya(l)+"?bid="+h+"&store=1",success:function(e){f(e.data||{})},fail:function(){f({sample:{sample_rate:.001}})}}))),i},onChange:function(e){s=e},onReady:function(e){a=function(){!function(e){var t=e.bid,n=e.userId,i=e.deviceId,r=e.storageExpires,o=ba(t);Pr(o,{userId:n,deviceId:i},Kr(r))}(i),e()},n&&a()}};function c(){var t=Mi(Mi(Mi({},e),n||{}),r);t.plugins=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n={},i=0;i<e.length;)n=Ji(n,e[i++]);return n}(e.plugins,(null==n?void 0:n.plugins)||{},r.plugins||{}),t.sample=Sa(Sa(e.sample,null==n?void 0:n.sample),r.sample),i=t,s()}function u(){n=function(e){var t;if(!e)return{};var n=e.sample,i=e.plugins,r=e.timestamp,o=e.quota_rate,a=void 0===o?1:o,s=e.apdex;if(!n)return{};var c=n.sample_rate,u=n.sample_granularity,l=void 0===u?ia:u,h=n.include_users,f=n.rules;return{sample:{include_users:h,sample_rate:c*a,sample_granularity:l,rules:(void 0===f?[]:f).reduce((function(e,t){var n=t.name,i=t.enable,r=t.sample_rate,o=t.conditional_sample_rules;return e[n]={enable:i,sample_rate:r,conditional_sample_rules:o},e}),{})},plugins:{heatmap:null!==(t=null==i?void 0:i.heatmap)&&void 0!==t&&t},apdex:s,serverTimestamp:r}}(o),c(),a()}};function Sa(e,t){if(!e||!t)return e||t;var n=Mi(Mi({},e),t);return n.include_users=Ii(Ii([],Ci(e.include_users||[]),!1),Ci(t.include_users||[]),!1),n.rules=Ii(Ii([],Ci(Object.keys(e.rules||{})),!1),Ci(Object.keys(t.rules||{})),!1).reduce((function(n,i){var r,o;return i in n||(i in(e.rules||{})&&i in(t.rules||{})?(n[i]=Mi(Mi({},e.rules[i]),t.rules[i]),n[i].conditional_sample_rules=Ii(Ii([],Ci(e.rules[i].conditional_sample_rules||[]),!1),Ci(t.rules[i].conditional_sample_rules||[]),!1)):n[i]=(null===(r=e.rules)||void 0===r?void 0:r[i])||(null===(o=t.rules)||void 0===o?void 0:o[i])),n}),{}),n}var ka,xa={build:function(e){return{ev_type:e.ev_type,payload:e.payload,common:Mi(Mi({},e.extra||{}),e.overrides||{})}}},Da={sri:"reportSri",st:"reportResourceError",err:"captureException",reject:"captureException"},Aa=function(e,t){return"err"===t?!1!==Yi(e,"plugins."+Ao+".onerror",(function(e,t){return e[t]})):"reject"!==t||!1!==Yi(e,"plugins."+Ao+".onunhandledrejection",(function(e,t){return e[t]}))},Ta=function(e){var t,n,i=Ci(e,2),r=i[0],o=i[1];return{ev_type:eo,payload:{error:(t=r,n=t,"[object ErrorEvent]"===Object.prototype.toString.call(n)?function(e){var t=Do(e.error);if(!t)return t;var n=e.colno,i=e.lineno,r=e.filename;return n&&!t.colno&&(t.colno=String(n)),i&&!t.lineno&&(t.lineno=String(i)),r&&!t.filename&&(t.filename=r),t}(t):function(e){return"[object PromiseRejectionEvent]"===Object.prototype.toString.call(e)}(t)?function(e){var t;try{var n=void 0;if("reason"in e?n=e.reason:"detail"in e&&"reason"in e.detail&&(n=e.detail.reason),n){var i=Do(n);return Mi(Mi({},i),{name:null!==(t=i&&i.name)&&void 0!==t?t:"UnhandledRejection"})}}catch(e){}}(t):Do(t)),breadcrumbs:[],extra:o||{}},extra:{bid:"slardar_sdk"}}},Ra=function(e){var t=Kr(e.storageExpires),n=za(e.bid,t);return{bid:"",pid:"",viewId:Co("_"),userId:n.userId,deviceId:n.deviceId,storageExpires:t,sessionId:hr(),domain:"mon.zijieapi.com",pluginBundle:{name:"commonMonitors",plugins:["breadcrumb","jsError","performance","resourceError","resource"]},pluginPathPrefix:"https://lf3-short.ibytedapm.com/slardar/fe/sdk-web/plugins",plugins:{ajax:{ignoreUrls:na},fetch:{ignoreUrls:na},breadcrumb:{},pageview:{},jsError:{},resource:{},resourceError:{},performance:{},tti:{},fmp:{},blankScreen:!1,heatmap:!1},release:"",env:"production",sample:oa,transport:Xo()}},Ga=function(e){var t=void 0===e?{}:e,n=t.createSender,i=void 0===n?function(e){return Yo({size:20,endpoint:(t=e.domain,void 0===n&&(n=ta),(t&&t.indexOf("//")>=0?"":"https://")+t+n),transport:e.transport});var t,n}:n,r=t.builder,o=void 0===r?xa:r,a=t.createDefaultConfig,s=function(e){var t,n,i=e.builder,r=e.createSender,o=e.createDefaultConfig,a=e.createConfigManager,s=e.userConfigNormalizer,c=e.initConfigNormalizer,u=e.validateInitConfig,l={};Ui.forEach((function(e){return l[e]=[]}));var h=!1,f=!1,d=!1,p=[],g=[],_=function(){var e=!1,t={},n=function(e){e.length&&e.forEach((function(e){try{e()}catch(e){}})),e.length=0},i=function(e){t[e]&&t[e].forEach((function(e){n(e[1])})),t[e]=void 0};return{set:function(i,r,o){t[i]?t[i].push([r,o]):t[i]=[[r,o]],e&&n(o)},has:function(e){return!!t[e]},remove:i,removeByEvType:function(e){Object.keys(t).forEach((function(i){t[i]&&t[i].forEach((function(t){t[0]===e&&n(t[1])}))}))},clear:function(){e=!0,Object.keys(t).forEach((function(e){i(e)}))}}}(),v={getBuilder:function(){return i},getSender:function(){return t},getPreStartQueue:function(){return p},init:function(e){if(h)sr("already inited");else{if(!(e&&ji(e)&&u(e)))throw new Error("invalid InitConfig, init failed");var i=o(e);if(!i)throw new Error("defaultConfig missing");var s=c(e);if((n=a(i)).setConfig(s),n.onChange((function(){m("config")})),!(t=r(n.getConfig())))throw new Error("sender missing");h=!0,m("init",!0)}},set:function(e){h&&e&&ji(e)&&(m("beforeConfig",!1,e),null==n||n.setConfig(e))},config:function(e){if(h)return e&&ji(e)&&(m("beforeConfig",!1,e),null==n||n.setConfig(s(e))),null==n?void 0:n.getConfig()},provide:function(e,t){$i(g,e)?sr("cannot provide "+e+", reserved"):(v[e]=t,m("provide",!1,e))},start:function(){var e=this;h&&(f||null==n||n.onReady((function(){f=!0,m("start",!0),p.forEach((function(t){return e.build(t)})),p.length=0})))},report:function(e){if(e){var t=lr(l.beforeReport)(e);if(t){var n=lr(l.report)(t);n&&(f?this.build(n):p.push(n))}}},build:function(e){if(f){var t=lr(l.beforeBuild)(e);if(t){var n=i.build(t);if(n){var r=lr(l.build)(n);r&&this.send(r)}}}},send:function(e){if(f){var n=lr(l.beforeSend)(e);n&&(t.send(n),m("send",!1,n))}},destroy:function(){_.clear(),d=!0,p.length=0,m("beforeDestroy",!0)},on:function(e,t){if("init"===e&&h||"start"===e&&f||"beforeDestroy"===e&&d)try{t()}catch(e){}else l[e]&&l[e].push(t)},off:function(e,t){l[e]&&(l[e]=Xi(l[e],t))},destroyAgent:_};return g=Object.keys(v),v;function m(e,t){void 0===t&&(t=!1);for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];l[e].forEach((function(e){try{e.apply(void 0,Ii([],Ci(n),!1))}catch(e){}})),t&&(l[e].length=0)}}({validateInitConfig:va,initConfigNormalizer:_a,userConfigNormalizer:ma,createSender:i,builder:o,createDefaultConfig:void 0===a?Ra:a,createConfigManager:Ea});(function(e,t){void 0===t&&(t=.001);var n,i,r=Gr(br());r&&(r.errors||(r.errors=[]),"observe"in r.errors||cr(t)&&(r.errors=(n=r.errors,i=[],n.observe=function(e){i.push(e)},n.push=function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return t.forEach((function(e){i.forEach((function(t){return t(e)}))})),(e=[].push).call.apply(e,Ii([n],Ci(t),!1))},n),r.errors.forEach((function(t){e.report(Ta(t))})),r.errors.observe((function(t){e.report(Ta(t))}))))})(s),function(e){var t=function(){var e={},t={},n={set:function(i,r){return e[i]=r,t[i]=er(r),n},merge:function(i){return e=Mi(Mi({},e),i),Object.keys(i).forEach((function(e){t[e]=er(i[e])})),n},delete:function(i){return delete e[i],delete t[i],n},clear:function(){return e={},t={},n},get:function(e){return t[e]},toString:function(){return Mi({},t)}};return n}();e.provide("context",t),e.on("report",(function(e){return e.extra||(e.extra={}),e.extra.context=t.toString(),e}))}(s);var c=Gr(br());!function(e,t){var n=t||{},i={};e.provide("setFilter",(function(e,t){i[e]||(i[e]=[]),i[e].push(t)})),e.provide("initSubject",(function(t){var r=Ci(t,2),o=r[0],a=r[1],s=function(e){return e.split("_")[0]}(o),c=!!s&&i[s];return n[o]||(n[o]=pr(a,(function(){n[o]=void 0}))),c?mr(e,[o,gr(n[o],c)]):n[o]})),e.provide("getSubject",(function(e){return n[e]})),e.provide("privateSubject",{})}(s,c&&c.subject),function(e){var t,n=!1;e.on("init",(function(){t=(new Date).getTime(),e.on("config",(function(){var i,r=null===(i=e.config())||void 0===i?void 0:i.serverTimestamp;if(!(isNaN(r)||Number(r)<=0||n)){n=!0;var o=(new Date).getTime();if(o-t<700&&r){var a=r-(o+t)/2;!isNaN(a)&&(a>0||a<-6e5)&&e.on("beforeBuild",(function(e){var t;return Mi(Mi({},e),{extra:Mi(Mi({},null!==(t=e.extra)&&void 0!==t?t:{}),{sdk_offset:null!=a?a:0})})}))}}}))}))}(s),function(e){e.on("beforeBuild",(function(t){return function(e,t){var n={};return n.bid=t.bid,n.user_id=t.userId,n.device_id=t.deviceId,n.session_id=t.sessionId,n.release=t.release,n.env=t.env,Mi(Mi({},e),{extra:Mi(Mi({},n),e.extra||{})})}(t,e.config())}))}(s),function(e){e.on("report",(function(t){return function(e,t){var n=t||{},i=n.pid,r=void 0===i?"":i,o=n.viewId,a=void 0===o?"":o,s={url:Dr(),timestamp:yr(),sdk_version:Qo,sdk_name:"SDK_SLARDAR_WEB",pid:r,view_id:a};return Mi(Mi({},e),{extra:Mi(Mi({},s),e.extra||{})})}(t,e.config())}))}(s),function(e){var t=function(){var e=function(){if(br()&&"navigator"in window)return window.navigator}();if(e)return e.connection||e.mozConnection||e.webkitConnection}(),n=Fo(t);t&&(t.onchange=function(){n=Fo(t)}),e.on("report",(function(e){return Mi(Mi({},e),{extra:Mi(Mi({},e.extra||{}),{network_type:n})})}))}(s),function(e){e.on("start",(function(){var t=e.config().bid,n=e.getSender();n.setEndpoint(n.getEndpoint()+"?biz_id="+t)}))}(s);var u=function(e,t,n){var i={},r=function(){for(var n,o=[],a=0;a<arguments.length;a++)o[a]=arguments[a];var s=o[0];if(s){var c=s.split(".")[0];if(!(c in r)){var u=i[c]||[],l=null!==(n=null==t?void 0:t(e))&&void 0!==n?n:{};return u.push(Ii([l],Ci(o),!1)),void(i[c]=u)}return function(e,t,n){return Yi(e,t,(function(e,t){if(e&&t in e&&Hi(e[t]))try{return e[t].apply(e,n)}catch(e){return}}))}(r,s,[].slice.call(o,1))}};for(var o in tr(e,"provide",(function(t){return function(n,i){r[n]=i,t.call(e,n,i)}}))(),e)Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return e.on("provide",(function(t){i[t]&&(i[t].forEach((function(t){var i=Ci(t),r=i[0],o=i.slice(1);null==n||n(e,r,o)})),i[t]=null)})),r}(s,Mr,(function(e,t,n){return Cr(e,t)((function(){var e=Ci(n),t=e[0],i=e.slice(1);s[t].apply(s,Ii([],Ci(i),!1))}))}));return function(e,t){e.on("init",(function(){var n=[],i=function(i){i.forEach((function(i){var r=i.name;$i(n,r)||(n.push(r),i.setup(e),t&&t(r,i.setup),e.destroyAgent.set(r,r,[function(){n=Xi(n,r),i.tearDown&&i.tearDown()}]))}))};e.provide("applyIntegrations",i);var r=e.config();r&&r.integrations&&i(r.integrations)}))}(u,pa),u},Oa=((ka={})[Ho]=function(e){e.on("init",(function(){var t,n=null===(t=e.config())||void 0===t?void 0:t.plugins[Ho];!function(e,t){var n,i=Nr(t,Vo);if(i&&wr()){var r=i.routeMode,o=i.apdex,a=e.report.bind(e),s=Li;if(o){var c=[],u=Ci(function(e,t,n,i){var r,o,a,s=Ci(n,2),c=s[0],u=s[1],l=2===i.apdex,h=void 0,f=void 0,d=void 0,p=!1,g=Ci(Ko(),4),_=g[0],v=g[1],m=g[2],y=g[3],b=Ci(Ko(),4),z=b[0],w=b[1],E=b[2],S=b[3],k=Ci((r={start:yr(),end:0,time_spent:0,is_bounced:!1,entry:"",exit:"",p_count:0,a_count:0},[function(e,t){var n=Ci(e,3),i=n[0],o=n[1],a=n[2];r.end=yr(),r.time_spent+=t&&t.time_spent||0,r.last_page=t,r.p_count+=1,r.rank=i,r.apdex=o,r.apdex_detail=a;var s=zr();s&&(r.is_bounced=!function(e){return"complete"===e.readyState}(s))},function(e,t){r.time_spent+=e.time_spent,r.p_count+=1,r.exit=t},function(){r.a_count+=1},function(e){r.entry=e,r.exit=e},function(){return r}]),5),x=k[0],D=k[1],A=k[2],T=k[3],R=k[4],G=Ci((o=0,a=void 0,[function(e){if(e){if(!a)return;o+=yr()-a,a=void 0}else a=yr()},function(){a&&(o+=yr()-a);var e=o;return o=0,a=yr(),e}]),2),O=G[0],M=G[1];t.push(c[0](O)),!l&&t.push(u[0]((function(){if(p){var t=Ci(E(),2),n=t[0],i=t[1],r=jo(n,d);x([r,n,i],B()),e({ev_type:Qr,payload:R()}),S()}})));var C=Po(_,v),I=Po(z,w),B=function(){var e=Ci(m(),2),t=e[0],n=e[1];return{start:h[0],pid:h[1],view_id:h[2],end:yr(),time_spent:M(),apdex:t,rank:jo(t,d),detail:n}};return t.push((function(){p=!1})),[function(e,t){if(!h)return h=[yr(),e,t],T(e),void(p=!(!d||!h));p&&(f=B(),D(f,e)),h=[yr(),e,t],y()},function(e){p&&(l||(I(e,d),e.ev_type===io&&A()),e.common.pid===h[1]&&C(e,d))},function(t){p&&(t.payload.last=f),e(t)},function(e){if(!e)return t.forEach((function(e){return e()})),void(t.length=0);p=!(!(d=e)||!h)}]}(e.report.bind(e),c,[vr(e,Ro),vr(e,Go)],i),4),l=u[0],h=u[1],f=u[2],d=u[3];a=f,s=l,e.on("send",h),c.push((function(){return e.off("send",h)})),e.on("start",(function(){d(e.config().apdex)})),_r(e,Ho,Qr,c)}var p=[],g=Ci(qo(a,p,Bo(r)?[]:[e.initSubject(Oo),e.initSubject(Mo)],Mi(Mi({},i),{initPid:null===(n=e.config())||void 0===n?void 0:n.pid,onPidUpdate:function(t){var n=Co(t);s(t,n),e.set({pid:t,viewId:n,actionId:void 0})}})),1)[0];mr(e,["f_view_0",Rr(e)],-1);var _=function(){g(e.config().pid)};e.on("config",_),p.push((function(){return e.off("config",_)})),_r(e,Ho,Yr,p),e.provide("sendPageview",g)}}(e,n)}))},ka[po]=function(e){e.on("init",(function(){var t,n=null===(t=e.config())||void 0===t?void 0:t.plugins[po];!function(e,t){var n=Nr(t,go);if(n){var i=[],r=Mi(Mi({},n),{setContextAtReq:function(){return Tr(e,!0)},setTraceHeader:ao(n.trace)}),o=function(){return vr(e,Xr)};r.autoWrap&&ho(i,[vr(e,["xhr_0",Wr(XMLHttpRequest&&XMLHttpRequest.prototype)]),o],r),_r(e,po,to,i),e.provide("wrapXhr",(function(e){function t(){var t=new e;return ho(i,[pr(Wr(t)),o],r),t}return t.prototype=new e,["DONE","HEADERS_RECIEVED","LOADING","OPENED","UNSENT"].forEach((function(n){t[n]=e[n]})),t}))}}(e,n)}))},ka[So]=function(e){e.on("init",(function(){var t,n=null===(t=e.config())||void 0===t?void 0:t.plugins[So];!function(e,t){var n=Nr(t,ko);if(n){var i=[],r=Mi(Mi({},n),{setContextAtReq:function(){return Tr(e,!0)},setTraceHeader:ao(n.trace)}),o=function(){return vr(e,Xr)};r.autoWrap&&_o(i,[vr(e,Zr),o],r),_r(e,So,to,i),e.provide("wrapFetch",(function(e){var t=void 0;return _o(i,[pr((function(n){t=Jr(e,n)})),o],r),t}))}}(e,n)}))},ka),Ma=function(e){void 0===e&&(e={});var t=Ga(e);return function(e){e.on("start",(function(){var t=e.config(),n=function(e,t,n,i,r){if(!t)return Ni;var o=t.sample_rate,a=t.include_users,s=t.sample_granularity,c=t.rules,u=t.r,l=void 0===u?Math.random():u;if($i(a,e))return function(e){return Wo(e,1)};var h="session"===s,f=Jo(h,o,n,l,i),d=function(e,t,n,i,r,o){var a={};return Object.keys(e).forEach((function(s){var c=e[s],u=c.enable,l=c.sample_rate,h=c.conditional_sample_rules;u?(a[s]={enable:u,sample_rate:l,effectiveSampleRate:l*n,hit:Jo(t,l,i,r,o)},h&&(a[s].conditional_hit_rules=h.map((function(e){var a=e.sample_rate,s=e.filter;return{sample_rate:a,hit:Jo(t,a,i,r,o),effectiveSampleRate:a*n,filter:s}})))):a[s]={enable:u,hit:function(){return!1},sample_rate:0,effectiveSampleRate:0}})),a}(c,h,o,n,l,i);return function(e){var t;if(!f())return h&&r[0](),!1;if(!(e.ev_type in d))return Wo(e,o);if(!d[e.ev_type].enable)return h&&r[1](e.ev_type),!1;if(null===(t=e.common)||void 0===t?void 0:t.sample_rate)return e;var n=d[e.ev_type],i=n.conditional_hit_rules;if(i)for(var a=0;a<i.length;a++)if(Zo(e,i[a].filter))return!!i[a].hit()&&Wo(e,i[a].effectiveSampleRate);return n.hit()?Wo(e,n.effectiveSampleRate):((!i||!i.length)&&h&&r[1](e.ev_type),!1)}}(t.userId,t.sample,cr,ur,[function(){e.destroy()},function(t){e.destroyAgent.removeByEvType(t)}]);e.on("build",n)}))}(t),function(e,t){var n;void 0===t&&(t=Da);var i,r=function(e){return Object.keys(e).reduce((function(e,t){return e[t]=[],e}),{})}(t),o=(i=t,Object.keys(i).reduce((function(e,t){return e[i[t]]?e[i[t]].push(t):e[i[t]]=[t],e}),{})),a=function(e,t,n){return function(i,r,o,a){var s;void 0===o&&(o=yr()),void 0===a&&(a=location.href);var c=Mi(Mi({},Mr(e)),{url:a,timestamp:o});t[i]&&(e[n[i]]?Cr(e,c)((function(){e[n[i]](r)})):null===(s=t[i])||void 0===s||s.push([r,c]))}}(e,r,t);(null===(n=e.p)||void 0===n?void 0:n.a)&&"observe"in e.p.a&&e.p.a.observe((function(t){var n=Ci(t,5);n[0];var i=n[1],r=n[2],o=n[3],s=n[4],c=e.config();Aa(c,i)&&a(i,r,o,s)})),e.on("init",(function(){var t,n=e.config();null===(t=e.p)||void 0===t||t.a.forEach((function(e){var t=Ci(e,5);t[0];var i=t[1],r=t[2],o=t[3],s=t[4];Aa(n,i)&&a(i,r,o,s)})),e.p&&e.p.a&&(e.p.a.length=0),e.provide("precollect",(function(e,t,i,r){void 0===i&&(i=yr()),void 0===r&&(r=location.href),Aa(n,e)&&a(e,t,i,r)}))})),e.on("provide",function(e,t,n){return function(i){i in n&&n[i].forEach((function(n){var r;null===(r=t[n])||void 0===r||r.forEach((function(t){var n=Ci(t,2),r=n[0],o=n[1];Cr(e,o)((function(){e[i](r)}))})),t[n]=null}))}}(e,r,o))}(t),function(e){e.provide("sendEvent",(function(t){var n=function(e){if(e&&ji(e)&&e.name&&Fi(e.name)){var t={name:e.name,type:"event"};if("metrics"in e&&ji(e.metrics)){var n=e.metrics,i={};for(var r in n)Vi(n[r])&&(i[r]=n[r]);t.metrics=i}if("categories"in e&&ji(e.categories)){var o=e.categories,a={};for(var r in o)a[r]=er(o[r]);t.categories=a}return"attached_log"in e&&Fi(e.attached_log)&&(t.attached_log=e.attached_log),t}}(t);n&&e.report({ev_type:no,payload:n,extra:{timestamp:yr()}})})),e.provide("sendLog",(function(t){var n=function(e){if(e&&ji(e)&&e.content&&Fi(e.content)){var t={content:er(e.content),type:"log",level:"info"};if("level"in e&&(t.level=e.level),"extra"in e&&ji(e.extra)){var n=e.extra,i={},r={};for(var o in n)Vi(n[o])?i[o]=n[o]:r[o]=er(n[o]);t.metrics=i,t.categories=r}return"attached_log"in e&&Fi(e.attached_log)&&(t.attached_log=e.attached_log),t}}(t);n&&e.report({ev_type:no,payload:n,extra:{timestamp:yr()}})}))}(t),Object.keys(Oa).forEach((function(e){pa(e,Oa[e]),Oa[e](t)})),function(e,t,n){void 0===n&&(n=ua),function(e){var t=br(),n=zr();t&&n&&("complete"!==n.readyState?Ir(t,"load",(function(){setTimeout((function(){e()}),0)}),!1):e())}((function(){e.on("init",(function(){n(e,t)}))}))}(t),t.provide("create",Ma),t},Ca="precollect",Ia=3e5,Ba=Ma(),Ua=br();Ua&&function(e,t){if("addEventListener"in e){t.pcErr=function(n){var i=(n=n||e.event).target||n.srcElement||{};i instanceof Element||i instanceof HTMLElement?t(Ca,"st",{tagName:i.tagName,url:i.getAttribute("href")||i.getAttribute("src")}):t(Ca,"err",n.error)},t.pcRej=function(n){n=n||e.event,t(Ca,"reject",n.reason||n.detail&&n.detail.reason)};var n=[];n.push(Ir(e,"error",t.pcErr,!0)),n.push(Ir(e,"unhandledrejection",t.pcRej,!0)),setTimeout((function(){n.forEach((function(e){return e()}))}),Ia)}"PerformanceObserver"in e&&"PerformanceLongTaskTiming"in e&&(t.pp={entries:[]},t.pp.observer=new PerformanceObserver((function(e){t.pp.entries=t.pp.entries.concat(e.getEntries())})),t.pp.observer.observe({entryTypes:["longtask"]}),setTimeout((function(){t.pp.observer.disconnect()}),Ia))}(Ua,Ba),window.PassportCollector=Oi,window.PassportBrowserClient=Ma,window.ucSecureMonitorBase=e}();
//# sourceMappingURL=index.umd.production.js.map