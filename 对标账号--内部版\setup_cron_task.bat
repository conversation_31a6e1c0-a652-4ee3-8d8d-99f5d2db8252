@echo off
echo 设置设备使用时间限制检查定时任务
echo.

REM 获取当前脚本所在目录
set SCRIPT_DIR=%~dp0
set PHP_SCRIPT=%SCRIPT_DIR%check_expired_devices_cron.php

echo 脚本路径: %PHP_SCRIPT%
echo.

REM 检查PHP是否可用
php --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 系统中未找到PHP，请先安装PHP或将PHP添加到系统PATH环境变量中
    echo.
    pause
    exit /b 1
)

echo PHP已检测到，版本信息:
php --version
echo.

REM 创建定时任务
echo 正在创建定时任务...
schtasks /create /tn "设备使用时间限制检查" /tr "php \"%PHP_SCRIPT%\"" /sc hourly /mo 1 /f

if errorlevel 1 (
    echo.
    echo 错误: 创建定时任务失败
    echo 请确保以管理员身份运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo 定时任务创建成功！
echo 任务名称: 设备使用时间限制检查
echo 执行频率: 每小时一次
echo 脚本路径: %PHP_SCRIPT%
echo.

REM 显示任务信息
echo 任务详细信息:
schtasks /query /tn "设备使用时间限制检查" /fo table /v

echo.
echo 如需删除此定时任务，请运行以下命令:
echo   schtasks /delete /tn "设备使用时间限制检查" /f
echo.

REM 询问是否立即测试运行
set /p TEST_RUN=是否立即测试运行一次？(y/n): 
if /i "%TEST_RUN%"=="y" (
    echo.
    echo 开始测试运行...
    echo.
    php "%PHP_SCRIPT%"
    echo.
    echo 测试运行完成
)

echo.
echo 设置完成！定时任务将每小时自动检查一次过期设备。
pause