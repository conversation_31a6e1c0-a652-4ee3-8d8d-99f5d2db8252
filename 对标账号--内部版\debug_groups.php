<?php
/**
 * 调试分组数据脚本
 * 用于检查各个表中的分组数据
 */

// 数据库连接参数
$servername = "localhost";
$username = "long";
$password = "hql0830..";
$dbname = "device_records";

try {
    // 创建连接
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    // 检查连接
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset("utf8mb4");
    
    echo "=== 分组数据调试信息 ===\n";
    echo "时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 1. 检查device_groups表
    echo "1. 检查device_groups表:\n";
    $groups_sql = "SELECT group_name, description, created_at FROM device_groups ORDER BY group_name";
    $result = $conn->query($groups_sql);
    
    if ($result) {
        echo "   记录数: " . $result->num_rows . "\n";
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                echo "   - {$row['group_name']} ({$row['description']}) - {$row['created_at']}\n";
            }
        } else {
            echo "   device_groups表为空\n";
        }
    } else {
        echo "   查询失败: " . $conn->error . "\n";
    }
    
    echo "\n";
    
    // 2. 检查devices表中的分组
    echo "2. 检查devices表中的分组:\n";
    $devices_groups_sql = "SELECT DISTINCT device_group, COUNT(*) as count FROM devices 
                          WHERE device_group IS NOT NULL AND device_group != '' 
                          GROUP BY device_group ORDER BY device_group";
    $result = $conn->query($devices_groups_sql);
    
    if ($result) {
        echo "   分组数: " . $result->num_rows . "\n";
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                echo "   - {$row['device_group']} ({$row['count']} 个设备)\n";
            }
        } else {
            echo "   devices表中没有分组数据\n";
        }
    } else {
        echo "   查询失败: " . $conn->error . "\n";
    }
    
    echo "\n";
    
    // 3. 检查device_heartbeat表中的分组
    echo "3. 检查device_heartbeat表中的分组:\n";
    $heartbeat_groups_sql = "SELECT DISTINCT device_group, COUNT(*) as count FROM device_heartbeat 
                            WHERE device_group IS NOT NULL AND device_group != '' AND device_group != '未分组'
                            GROUP BY device_group ORDER BY device_group";
    $result = $conn->query($heartbeat_groups_sql);
    
    if ($result) {
        echo "   分组数: " . $result->num_rows . "\n";
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                echo "   - {$row['device_group']} ({$row['count']} 个心跳记录)\n";
            }
        } else {
            echo "   device_heartbeat表中没有分组数据\n";
        }
    } else {
        echo "   查询失败: " . $conn->error . "\n";
    }
    
    echo "\n";
    
    // 4. 模拟API调用
    echo "4. 模拟API调用结果:\n";
    $groups = [];
    
    // 从device_groups表获取
    $groups_sql = "SELECT group_name FROM device_groups ORDER BY group_name";
    $groups_result = $conn->query($groups_sql);
    
    if ($groups_result && $groups_result->num_rows > 0) {
        while ($row = $groups_result->fetch_assoc()) {
            $groups[] = $row['group_name'];
        }
        echo "   从device_groups表获取到 " . count($groups) . " 个分组\n";
    }
    
    // 如果为空，从devices表获取
    if (empty($groups)) {
        $devices_groups_sql = "SELECT DISTINCT device_group FROM devices
                              WHERE device_group IS NOT NULL AND device_group != ''
                              ORDER BY device_group";
        $devices_result = $conn->query($devices_groups_sql);
        
        if ($devices_result && $devices_result->num_rows > 0) {
            while ($row = $devices_result->fetch_assoc()) {
                if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                    $groups[] = $row['device_group'];
                }
            }
            echo "   从devices表获取到 " . count($groups) . " 个分组\n";
        }
    }
    
    // 如果还是为空，从心跳表获取
    if (empty($groups)) {
        $heartbeat_groups_sql = "SELECT DISTINCT device_group FROM device_heartbeat
                                WHERE device_group IS NOT NULL AND device_group != ''
                                AND device_group != '未分组'
                                ORDER BY device_group";
        $heartbeat_result = $conn->query($heartbeat_groups_sql);
        
        if ($heartbeat_result && $heartbeat_result->num_rows > 0) {
            while ($row = $heartbeat_result->fetch_assoc()) {
                if (!empty($row['device_group']) && $row['device_group'] !== '未分组') {
                    $groups[] = $row['device_group'];
                }
            }
            echo "   从device_heartbeat表获取到 " . count($groups) . " 个分组\n";
        }
    }
    
    echo "   最终API返回的分组: " . json_encode($groups) . "\n";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 调试完成 ===\n";
?>
