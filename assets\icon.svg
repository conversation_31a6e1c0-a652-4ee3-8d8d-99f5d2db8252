<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#bg)" stroke="white" stroke-width="8"/>
  
  <!-- 主图标 - 手机 -->
  <rect x="80" y="60" width="96" height="136" rx="12" ry="12" fill="white" opacity="0.9"/>
  <rect x="88" y="76" width="80" height="104" rx="4" ry="4" fill="url(#accent)"/>
  
  <!-- 屏幕内容 - 多个用户头像 -->
  <circle cx="108" cy="100" r="12" fill="white" opacity="0.8"/>
  <circle cx="148" cy="100" r="12" fill="white" opacity="0.8"/>
  <circle cx="108" cy="130" r="12" fill="white" opacity="0.8"/>
  <circle cx="148" cy="130" r="12" fill="white" opacity="0.8"/>
  
  <!-- 用户图标 -->
  <g transform="translate(102, 94)">
    <circle cx="6" cy="4" r="2" fill="url(#accent)"/>
    <path d="M2 10 C2 8, 4 7, 6 7 C8 7, 10 8, 10 10" stroke="url(#accent)" stroke-width="1" fill="none"/>
  </g>
  
  <g transform="translate(142, 94)">
    <circle cx="6" cy="4" r="2" fill="url(#accent)"/>
    <path d="M2 10 C2 8, 4 7, 6 7 C8 7, 10 8, 10 10" stroke="url(#accent)" stroke-width="1" fill="none"/>
  </g>
  
  <g transform="translate(102, 124)">
    <circle cx="6" cy="4" r="2" fill="url(#accent)"/>
    <path d="M2 10 C2 8, 4 7, 6 7 C8 7, 10 8, 10 10" stroke="url(#accent)" stroke-width="1" fill="none"/>
  </g>
  
  <g transform="translate(142, 124)">
    <circle cx="6" cy="4" r="2" fill="url(#accent)"/>
    <path d="M2 10 C2 8, 4 7, 6 7 C8 7, 10 8, 10 10" stroke="url(#accent)" stroke-width="1" fill="none"/>
  </g>
  
  <!-- 底部按钮 -->
  <circle cx="128" cy="190" r="6" fill="white" opacity="0.6"/>
  
  <!-- 装饰性元素 -->
  <circle cx="60" cy="80" r="8" fill="white" opacity="0.3"/>
  <circle cx="196" cy="176" r="6" fill="white" opacity="0.3"/>
  <circle cx="200" cy="100" r="4" fill="white" opacity="0.3"/>
  
  <!-- 文字 -->
  <text x="128" y="230" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">抖音管理器</text>
</svg>
