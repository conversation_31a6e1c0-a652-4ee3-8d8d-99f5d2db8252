"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isBooleanable = exports.boolean = void 0;
const boolean_1 = require("./boolean");
Object.defineProperty(exports, "boolean", { enumerable: true, get: function () { return boolean_1.boolean; } });
const isBooleanable_1 = require("./isBooleanable");
Object.defineProperty(exports, "isBooleanable", { enumerable: true, get: function () { return isBooleanable_1.isBooleanable; } });
